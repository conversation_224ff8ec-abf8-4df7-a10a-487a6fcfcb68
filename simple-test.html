<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2 {
            text-align: center;
        }
        .container {
            margin-bottom: 50px;
        }
        .section-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .section-title {
            font-size: 32px;
            margin-bottom: 10px;
        }
        .section-subtitle {
            color: #666;
        }
        .events-container, .team-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: center;
        }
        .event-card {
            display: flex;
            width: 350px;
            background-color: #fff;
            border-radius: 5px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        .event-date {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 20px;
            background-color: #f7b731;
            color: #fff;
            min-width: 80px;
        }
        .event-month {
            font-size: 16px;
            font-weight: 600;
        }
        .event-day {
            font-size: 28px;
            font-weight: 700;
        }
        .event-details {
            padding: 20px;
            flex: 1;
        }
        .event-title {
            font-size: 20px;
            margin-bottom: 10px;
        }
        .event-time, .event-location {
            color: #666;
            margin-bottom: 5px;
        }
        .team-member {
            width: 250px;
            text-align: center;
            background-color: #fff;
            border-radius: 5px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        .team-member-image {
            height: 250px;
            overflow: hidden;
        }
        .team-member-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .team-member-name {
            font-size: 20px;
            margin: 20px 0 5px;
        }
        .team-member-role {
            color: #666;
            margin-bottom: 20px;
        }
        #debug-console {
            position: fixed;
            bottom: 10px;
            right: 10px;
            width: 400px;
            height: 300px;
            background: rgba(0,0,0,0.8);
            color: #0f0;
            font-family: monospace;
            font-size: 12px;
            padding: 10px;
            overflow: auto;
            z-index: 9999;
        }
    </style>
</head>
<body>
    <h1>Simple Test</h1>
    
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">UPCOMING EVENTS</h2>
            <p class="section-subtitle">Join us for these special occasions</p>
        </div>
        <div class="events-container" id="events-container">
            <!-- Events will be dynamically loaded here -->
            <div class="event-card">
                <div class="event-date">
                    <span class="event-month">JUN</span>
                    <span class="event-day">15</span>
                </div>
                <div class="event-details">
                    <h3 class="event-title">Community Service Day</h3>
                    <p class="event-time">9:00 AM - 2:00 PM</p>
                    <p class="event-location">Church Grounds</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="container" id="leadership-container">
        <div class="section-header">
            <h2 class="section-title">OUR LEADERSHIP TEAM</h2>
            <p class="section-subtitle">Meet the people who serve our church</p>
        </div>
        <div class="team-container">
            <!-- Team members will be dynamically loaded here -->
            <div class="team-member">
                <div class="team-member-image">
                    <img src="https://via.placeholder.com/250x250" alt="Pastor John Smith">
                </div>
                <h3 class="team-member-name">Pastor John Smith</h3>
                <p class="team-member-role">Senior Pastor</p>
            </div>
        </div>
    </div>
    
    <!-- Debug Console -->
    <div id="debug-console">
        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
            <strong>Debug Console</strong>
            <button onclick="document.getElementById('debug-console').style.display='none'" style="background: none; border: none; color: white; cursor: pointer;">×</button>
        </div>
        <div id="debug-log"></div>
    </div>
    
    <!-- JavaScript Files -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="./template-connector.js"></script>
</body>
</html>
