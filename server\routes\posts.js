const express = require('express');
const router = express.Router();
const { check, validationResult } = require('express-validator');
const Post = require('../models/Post');
const { auth, editor } = require('../middleware/auth');
const multer = require('multer');
const path = require('path');

// Set up multer storage
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, path.join(__dirname, '../../images/blog'));
  },
  filename: (req, file, cb) => {
    cb(null, `post-${Date.now()}${path.extname(file.originalname)}`);
  }
});

// Initialize upload
const upload = multer({
  storage,
  limits: { fileSize: 1000000 }, // 1MB
  fileFilter: (req, file, cb) => {
    // Check file types
    const filetypes = /jpeg|jpg|png|gif/;
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = filetypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb('Error: Images only (jpeg, jpg, png, gif)!');
    }
  }
});

// @route   GET api/posts
// @desc    Get all posts
// @access  Public
router.get('/', async (req, res) => {
  try {
    const posts = await Post.findAll({
      order: [['createdAt', 'DESC']]
    });
    res.json(posts);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

// @route   GET api/posts/:id
// @desc    Get post by ID
// @access  Public
router.get('/:id', async (req, res) => {
  try {
    const post = await Post.findByPk(req.params.id);
    
    if (!post) {
      return res.status(404).json({ message: 'Post not found' });
    }
    
    res.json(post);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

// @route   POST api/posts
// @desc    Create a new post
// @access  Private (Editor)
router.post(
  '/',
  [
    auth,
    editor,
    upload.single('featuredImage'),
    [
      check('title', 'Title is required').not().isEmpty(),
      check('content', 'Content is required').not().isEmpty(),
      check('author', 'Author is required').not().isEmpty()
    ]
  ],
  async (req, res) => {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      // Create new post
      const newPost = await Post.create({
        title: req.body.title,
        content: req.body.content,
        excerpt: req.body.excerpt,
        featuredImage: req.file ? `/images/blog/${req.file.filename}` : null,
        author: req.body.author,
        category: req.body.category,
        tags: req.body.tags ? req.body.tags.split(',') : [],
        status: req.body.status || 'draft',
        publishedAt: req.body.status === 'published' ? new Date() : null
      });

      res.json(newPost);
    } catch (err) {
      console.error(err.message);
      res.status(500).send('Server error');
    }
  }
);

// @route   PUT api/posts/:id
// @desc    Update a post
// @access  Private (Editor)
router.put(
  '/:id',
  [
    auth,
    editor,
    upload.single('featuredImage'),
    [
      check('title', 'Title is required').not().isEmpty(),
      check('content', 'Content is required').not().isEmpty(),
      check('author', 'Author is required').not().isEmpty()
    ]
  ],
  async (req, res) => {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      // Find post by ID
      let post = await Post.findByPk(req.params.id);
      
      if (!post) {
        return res.status(404).json({ message: 'Post not found' });
      }
      
      // Check if status is changing to published
      const isPublishing = post.status !== 'published' && req.body.status === 'published';
      
      // Update post
      post.title = req.body.title;
      post.content = req.body.content;
      post.excerpt = req.body.excerpt;
      if (req.file) {
        post.featuredImage = `/images/blog/${req.file.filename}`;
      }
      post.author = req.body.author;
      post.category = req.body.category;
      post.tags = req.body.tags ? req.body.tags.split(',') : [];
      post.status = req.body.status || post.status;
      
      // Set publishedAt date if publishing for the first time
      if (isPublishing) {
        post.publishedAt = new Date();
      }
      
      await post.save();
      
      res.json(post);
    } catch (err) {
      console.error(err.message);
      res.status(500).send('Server error');
    }
  }
);

// @route   DELETE api/posts/:id
// @desc    Delete a post
// @access  Private (Editor)
router.delete('/:id', [auth, editor], async (req, res) => {
  try {
    // Find post by ID
    const post = await Post.findByPk(req.params.id);
    
    if (!post) {
      return res.status(404).json({ message: 'Post not found' });
    }
    
    // Delete post
    await post.destroy();
    
    res.json({ message: 'Post removed' });
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

module.exports = router;
