/* Main Styles for North Texas SDA Church Website */

/* Global Styles */
:root {
    --primary-color: var(--cmsmasters-colors-primary, #404f40);
    --secondary-color: var(--cmsmasters-colors-secondary, #1d1d1d);
    --accent-color: var(--cmsmasters-colors-accent, #ed5a2f);
    --text-color: var(--cmsmasters-colors-text, #252628);
    --bg-color: var(--cmsmasters-colors-bg, #fff);
    --alternate-bg-color: var(--cmsmasters-colors-alternate, #f8f1e6);
    --border-color: var(--cmsmasters-colors-bd, #dedede);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--cmsmasters-text-font-family, "Lora"), sans-serif;
    font-size: var(--cmsmasters-text-font-size, 18px);
    line-height: var(--cmsmasters-text-line-height, 1.555em);
    color: var(--text-color);
    background-color: var(--bg-color);
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: color 0.3s ease;
}

a:hover {
    color: var(--accent-color);
}

/* Header Styles */
.cmsmasters-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    background-color: var(--bg-color);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.cmsmasters-header__outer {
    width: 100%;
}

.cmsmasters-header__inner {
    max-width: 1320px;
    margin: 0 auto;
    padding: 0 20px;
}

.cmsmasters-header__content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100px;
}

.cmsmasters-header__logo {
    flex: 0 0 auto;
}

.cmsmasters-header__logo-link {
    display: flex;
    align-items: center;
    text-decoration: none;
}

.cmsmasters-header__logo-image {
    max-height: 60px;
    margin-right: 15px;
}

.cmsmasters-header__logo-text {
    font-family: var(--cmsmasters-primary-font-family, "Bebas Neue"), sans-serif;
    font-size: 28px;
    font-weight: 400;
    letter-spacing: -1px;
    color: var(--secondary-color);
}

.cmsmasters-header__navigation {
    flex: 1 1 auto;
    display: flex;
    justify-content: center;
}

.cmsmasters-header__menu {
    display: flex;
    list-style: none;
    gap: 30px;
}

.cmsmasters-header__menu-item {
    position: relative;
}

.cmsmasters-header__menu-link {
    font-family: var(--cmsmasters-accent-font-family, "Barlow Condensed"), sans-serif;
    font-size: 18px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 2px;
    color: var(--secondary-color);
    text-decoration: none;
}

.cmsmasters-header__menu-link:hover {
    color: var(--accent-color);
}

.cmsmasters-header__actions {
    display: flex;
    gap: 15px;
}

.cmsmasters-button-link {
    display: inline-block;
    padding: 15px 40px;
    font-family: var(--cmsmasters-button-font-family, "Figtree"), sans-serif;
    font-size: 20px;
    font-weight: 700;
    color: var(--bg-color);
    background-color: var(--accent-color);
    border: 1px solid var(--accent-color);
    transition: all 0.3s ease;
    text-decoration: none;
    text-align: center;
}

.cmsmasters-button-link:hover {
    color: var(--secondary-color);
    background-color: var(--bg-color);
    border-color: var(--border-color);
}

.cmsmasters-button-size-sm {
    padding: 10px 25px;
    font-size: 16px;
}

.cmsmasters-header__toggle {
    display: none;
    background: none;
    border: none;
    font-size: 24px;
    color: var(--secondary-color);
    cursor: pointer;
}

/* Hero Section Styles */
.hero-section {
    height: 100vh;
    min-height: 700px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--bg-color);
    text-align: left;
    padding-top: 100px;
    position: relative;
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 1320px;
    width: 100%;
    padding: 0 20px;
}

.hero-text-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.hero-heading {
    margin-bottom: 10px;
}

.hero-heading .elementor-heading-title {
    font-family: var(--cmsmasters-primary-font-family, "Bebas Neue"), sans-serif;
    font-size: 150px;
    line-height: 1.15em;
    color: var(--bg-color);
    font-weight: 400;
    letter-spacing: -1px;
}

.hero-subtitle {
    margin-bottom: 40px;
}

.hero-subtitle .elementor-heading-title {
    font-family: var(--cmsmasters-text-font-family, "Lora"), sans-serif;
    font-size: 24px;
    font-weight: 400;
    color: var(--bg-color);
}

.hero-buttons {
    display: flex;
    gap: 20px;
}

.service-times {
    position: absolute;
    bottom: 50px;
    right: 50px;
    background-color: var(--bg-color);
    color: var(--secondary-color);
    padding: 30px;
    border-radius: 10px;
    max-width: 300px;
    z-index: 2;
}

.service-times-container {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.service-time-heading h2 {
    font-family: var(--cmsmasters-primary-font-family, "Bebas Neue"), sans-serif;
    font-size: 32px;
    margin-right: 15px;
    font-weight: 400;
    letter-spacing: -1px;
}

.service-time-label {
    transform: rotate(-15deg);
    font-family: var(--cmsmasters-primary-font-family, "Bebas Neue"), sans-serif;
    font-size: 24px;
    color: var(--accent-color);
}

.service-day h2 {
    font-family: var(--cmsmasters-primary-font-family, "Bebas Neue"), sans-serif;
    font-size: 42px;
    margin-bottom: 10px;
    font-weight: 400;
    letter-spacing: -1px;
}

.service-note p {
    font-family: var(--cmsmasters-text-font-family, "Lora"), sans-serif;
    font-size: 16px;
    color: var(--text-color);
}

/* Welcome Section Styles */
.welcome-section {
    padding: 100px 0;
    background-color: var(--bg-color);
}

.welcome-container {
    max-width: 1320px;
    margin: 0 auto;
    padding: 0 20px;
}

.welcome-content {
    display: flex;
    position: relative;
}

.welcome-decoration {
    position: relative;
    flex: 0 0 40%;
}

.welcome-decoration-image {
    max-width: 280px;
}

.welcome-decoration-text {
    position: absolute;
    top: 50px;
    left: 150px;
}

.welcome-decoration-label {
    font-family: var(--cmsmasters-primary-font-family, "Bebas Neue"), sans-serif;
    font-size: 36px;
    transform: rotate(-15deg);
    color: var(--accent-color);
}

.welcome-text {
    flex: 0 0 60%;
    padding: 50px;
}

.cmsmasters-widget-title__heading {
    font-family: var(--cmsmasters-primary-font-family, "Bebas Neue"), sans-serif;
    font-size: 74px;
    margin-bottom: 30px;
    position: relative;
    font-weight: 400;
    letter-spacing: -1px;
    color: var(--secondary-color);
}

.title-inner-element {
    position: relative;
    display: inline-block;
}

.title-inner-element::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 100px;
    height: 3px;
    background-color: var(--accent-color);
}

.welcome-text p {
    font-family: var(--cmsmasters-text-font-family, "Lora"), sans-serif;
    font-size: 18px;
    line-height: 1.8;
    color: var(--text-color);
}

/* Who We Are Section */
.who-we-are-section {
    padding: 100px 0;
    background-color: var(--bg-color);
}

.who-we-are-container {
    max-width: 1320px;
    margin: 0 auto;
    padding: 0 20px;
}

.who-we-are-content {
    display: flex;
    flex-direction: column;
}

.who-we-are-heading h2 {
    font-family: var(--cmsmasters-primary-font-family, "Bebas Neue"), sans-serif;
    font-size: 42px;
    font-weight: 400;
    letter-spacing: -1px;
    color: var(--secondary-color);
    margin-bottom: 30px;
}

.who-we-are-box {
    background-color: var(--bg-color);
    padding: 50px;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
}

.who-we-are-box-title {
    font-family: var(--cmsmasters-secondary-font-family, "Barlow"), sans-serif;
    font-size: 32px;
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: 20px;
}

.who-we-are-box-description {
    font-family: var(--cmsmasters-text-font-family, "Lora"), sans-serif;
    font-size: 18px;
    line-height: 1.8;
    color: var(--text-color);
    margin-bottom: 30px;
}

.who-we-are-box-button a {
    display: inline-block;
    padding: 15px 40px;
    font-family: var(--cmsmasters-button-font-family, "Figtree"), sans-serif;
    font-size: 20px;
    font-weight: 700;
    color: var(--bg-color);
    background-color: var(--accent-color);
    border: 1px solid var(--accent-color);
    transition: all 0.3s ease;
    text-decoration: none;
}

.who-we-are-box-button a:hover {
    color: var(--secondary-color);
    background-color: var(--bg-color);
    border-color: var(--border-color);
}

/* Animation Classes */
.animated {
    animation-duration: 1s;
    animation-fill-mode: both;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fadeIn {
    animation-name: fadeIn;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translate3d(0, -50px, 0);
    }
    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

.fadeInDown {
    animation-name: fadeInDown;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translate3d(0, 50px, 0);
    }
    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

.fadeInUp {
    animation-name: fadeInUp;
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale3d(0.3, 0.3, 0.3);
    }
    50% {
        opacity: 1;
    }
}

.zoomIn {
    animation-name: zoomIn;
}

@keyframes rotateInDownLeft {
    from {
        transform-origin: left bottom;
        transform: rotate3d(0, 0, 1, -45deg);
        opacity: 0;
    }
    to {
        transform-origin: left bottom;
        transform: translate3d(0, 0, 0);
        opacity: 1;
    }
}

.rotateInDownLeft {
    animation-name: rotateInDownLeft;
}

@keyframes slideInUp {
    from {
        transform: translate3d(0, 100%, 0);
        visibility: visible;
    }
    to {
        transform: translate3d(0, 0, 0);
    }
}

.slideInUp {
    animation-name: slideInUp;
}
