<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - SDA</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Barlow+Condensed:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Barlow:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Bebas+Neue&family=Lora:ital,wght@0,400..700;1,400..700&family=Oswald:wght@200..700&display=swap"
        rel="stylesheet">
    <link
        href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap"
        rel="stylesheet">
    <link rel="stylesheet" href="css/admin-dashboard.css">
    <style>
        /* Toast Notifications */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
        }

        .toast {
            min-width: 300px;
            margin-bottom: 10px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            border: none;
        }

        .toast-success {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .toast-error {
            background: linear-gradient(135deg, #dc3545, #fd7e14);
            color: white;
        }

        .toast-info {
            background: linear-gradient(135deg, #17a2b8, #6f42c1);
            color: white;
        }

        .toast-header {
            background: transparent;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
        }

        .toast-body {
            font-weight: 500;
        }

        /* Improved Action Buttons */
        .action-buttons {
            display: flex;
            gap: 5px;
        }

        .btn-action {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            transition: all 0.2s ease;
            font-size: 12px;
        }

        .btn-action:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .btn-view {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
        }

        .btn-edit {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }

        .btn-delete {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
        }

        .btn-view:hover,
        .btn-edit:hover,
        .btn-delete:hover {
            color: white;
        }

        /* Activity Icons */
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        .bg-success-light {
            background-color: rgba(40, 167, 69, 0.1);
        }

        .bg-primary-light {
            background-color: rgba(0, 123, 255, 0.1);
        }

        .bg-danger-light {
            background-color: rgba(220, 53, 69, 0.1);
        }

        .bg-warning-light {
            background-color: rgba(255, 193, 7, 0.1);
        }

        .bg-info-light {
            background-color: rgba(23, 162, 184, 0.1);
        }

        /* Event Date Styling */
        .event-date {
            width: 50px;
            text-align: center;
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border-radius: 8px;
            padding: 8px 4px;
        }

        .event-month {
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .event-day {
            font-size: 18px;
            font-weight: bold;
            line-height: 1;
        }

        /* Modern View Details Modal */
        .view-details-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(5px);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .view-details-modal.show {
            opacity: 1;
            visibility: visible;
        }

        .modal-content-custom {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow: hidden;
            transform: scale(0.8) translateY(50px);
            transition: all 0.3s ease;
        }

        .view-details-modal.show .modal-content-custom {
            transform: scale(1) translateY(0);
        }

        .modal-header-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title-custom {
            font-size: 24px;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .modal-close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .modal-close-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: rotate(90deg);
        }

        .modal-body-custom {
            padding: 30px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .detail-item {
            display: flex;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .detail-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .detail-label {
            font-weight: 600;
            color: #555;
            min-width: 120px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .detail-value {
            flex: 1;
            color: #333;
            word-break: break-word;
        }

        .detail-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .badge-success {
            background: #d4edda;
            color: #155724;
        }

        .badge-warning {
            background: #fff3cd;
            color: #856404;
        }

        .badge-info {
            background: #d1ecf1;
            color: #0c5460;
        }

        .badge-secondary {
            background: #e2e3e5;
            color: #383d41;
        }

        .detail-description {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            margin-top: 10px;
            font-style: italic;
        }

        .detail-link {
            color: #667eea;
            text-decoration: none;
            word-break: break-all;
        }

        .detail-link:hover {
            text-decoration: underline;
        }

        /* Scrollbar styling for modal */
        .modal-body-custom::-webkit-scrollbar {
            width: 6px;
        }

        .modal-body-custom::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .modal-body-custom::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .modal-body-custom::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* Mobile Responsive Styles */
        .mobile-toggle {
            display: none;
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            padding: 10px;
            cursor: pointer;
            border-radius: 4px;
            transition: background-color 0.2s ease;
        }

        .mobile-toggle:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        /* Dashboard Cards Responsive */
        .dashboard-card {
            margin-bottom: 20px;
        }

        @media (min-width: 768px) {
            .dashboard-card {
                margin-bottom: 0;
            }
        }

        .view-all-btn-mobile {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 4px;
        }

        /* Table Responsive */
        .table-responsive-mobile {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        .table-responsive-mobile::-webkit-scrollbar {
            height: 6px;
        }

        .table-responsive-mobile::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .table-responsive-mobile::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .table-responsive-mobile::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* Sidebar States */
        .sidebar.collapsed {
            width: 70px;
        }

        .sidebar.collapsed .nav-link span {
            display: none;
        }

        .sidebar.collapsed .nav-link {
            justify-content: center;
            padding: 15px 10px;
        }

        .sidebar.collapsed .logo h4 {
            display: none;
        }

        .main-content.sidebar-collapsed {
            margin-left: 70px;
        }

        .main-content.sidebar-hidden {
            margin-left: 0;
        }

        /* Mobile Responsive Breakpoints */
        @media (max-width: 768px) {
            .mobile-toggle {
                display: block;
            }

            .sidebar {
                position: fixed;
                left: -250px;
                transition: left 0.3s ease;
                z-index: 1000;
            }

            .sidebar.show {
                left: 0;
            }

            .main-content {
                margin-left: 0 !important;
                padding: 15px;
            }

            .header {
                padding-left: 20px;
            }

            /* Dashboard Cards Mobile */
            .row.dashboard-cards-gap>.col-md-6 {
                margin-bottom: 20px;
            }

            /* Stats Cards - Keep as is */
            .stats-cards .col-md-3 {
                margin-bottom: 15px;
            }

            /* Table Mobile */
            .table-responsive-mobile {
                font-size: 14px;
            }

            .action-buttons {
                flex-direction: column;
                gap: 2px;
            }

            .btn-action {
                width: 28px;
                height: 28px;
                font-size: 10px;
            }

            /* Modal Mobile */
            .modal-content-custom {
                width: 95%;
                margin: 10px;
            }

            .modal-body-custom {
                padding: 20px;
            }

            .detail-label {
                min-width: 100px;
                font-size: 14px;
            }

            .detail-value {
                font-size: 14px;
            }
        }

        @media (max-width: 576px) {
            .main-content {
                padding: 10px;
            }

            .card {
                margin-bottom: 15px;
            }

            .table-responsive-mobile {
                font-size: 12px;
            }

            .btn-action {
                width: 24px;
                height: 24px;
                font-size: 9px;
            }

            .modal-content-custom {
                width: 98%;
                margin: 5px;
            }

            .detail-item {
                flex-direction: column;
                align-items: flex-start;
            }

            .detail-label {
                min-width: auto;
                margin-bottom: 5px;
            }
        }
    </style>
</head>

<body>
    <!-- Toast Container -->
    <div class="toast-container" id="toast-container"></div>

    <!-- Modern View Details Modal -->
    <div class="view-details-modal" id="view-details-modal">
        <div class="modal-content-custom">
            <div class="modal-header-custom">
                <h3 class="modal-title-custom" id="modal-title">
                    <i class="fas fa-eye"></i>
                    <span id="modal-title-text">Details</span>
                </h3>
                <button class="modal-close-btn" onclick="closeViewModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body-custom" id="modal-body">
                <!-- Content will be dynamically inserted here -->
            </div>
        </div>
    </div>
    <!-- Login Section -->
    <div id="login-section" class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white text-center">
                        <h4>SDA Church Admin Login</h4>
                    </div>
                    <div class="card-body">
                        <div id="login-alert" class="alert alert-danger d-none"></div>
                        <form id="login-form">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" value="<EMAIL>" required>
                            </div>
                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" class="form-control" id="password" value="admin123" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">Login</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard Section -->
    <div id="dashboard-section" class="dashboard-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-top">
                    <div class="sidebar-logo">
                        <img src="./images/logo.png" alt="">
                    </div>
                    <h2 class="sidebar-title">North Texas SDA Church</h2>
                </div>
            </div>

            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#dashboard" class="nav-link active" data-section="dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#events" class="nav-link" data-section="events">
                        <i class="fas fa-calendar-alt"></i>
                        <span>Events</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#sermons" class="nav-link" data-section="sermons">
                        <i class="fas fa-video"></i>
                        <span>Sermons</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#leadership" class="nav-link" data-section="leadership">
                        <i class="fas fa-users"></i>
                        <span>Leadership</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#blog" class="nav-link" data-section="blog">
                        <i class="fas fa-blog"></i>
                        <span>Blog</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#gallery" class="nav-link" data-section="gallery">
                        <i class="fas fa-images"></i>
                        <span>Gallery</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#members" class="nav-link" data-section="members">
                        <i class="fas fa-address-book"></i>
                        <span>Members</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#prayer-requests" class="nav-link" data-section="prayer-requests">
                        <i class="fas fa-pray"></i>
                        <span>Prayer Requests</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" id="logout-link">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Logout</span>
                    </a>
                </li>
            </ul>
        </div>

        <!-- Main Content -->
        <div class="main-content">

            <div class="circle"></div>
            <!-- Top Bar -->
            <div class="topbar">
                <div class="d-flex align-items-center">
                    <button class="mobile-toggle me-3" id="mobile-toggle" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="page-title" id="current-section-title">Overview</h1>
                </div>

                <div class="user-info">
                    <div class="user-avatar">A</div>
                    <span id="user-name">Admin User</span>
                </div>
            </div>

            <!-- Dashboard Overview Section -->
            <div id="dashboard-section" class="content-section active">
                <div class="dashboard-stats">
                    <div class="stat-card">
                        <div class="stat-icon bg-primary">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="stat-title">Church Members</h3>
                        <p class="stat-value" id="members-count">0</p>
                        <div class="stat-footer">
                            <span class="stat-label">Active Members</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon bg-success">
                            <i class="fas fa-pray"></i>
                        </div>
                        <h3 class="stat-title">Prayer Requests</h3>
                        <p class="stat-value" id="prayer-requests-count">0</p>
                        <div class="stat-footer">
                            <span class="stat-label">Pending Requests</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon bg-warning">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <h3 class="stat-title">Events</h3>
                        <p class="stat-value" id="events-count">0</p>
                        <div class="stat-footer">
                            <span class="stat-label">Upcoming Events</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon bg-info">
                            <i class="fas fa-video"></i>
                        </div>
                        <h3 class="stat-title">Sermons</h3>
                        <p class="stat-value" id="sermons-count">0</p>
                        <div class="stat-footer">
                            <span class="stat-label">Total Sermons</span>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card dashboard-card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Recent Activity</h5>
                                <button class="btn btn-sm btn-outline-primary view-all-btn-mobile">View All</button>
                            </div>
                            <div class="card-body p-0">
                                <ul class="list-group list-group-flush" id="recent-activity-list">
                                    <!-- Recent activity will be loaded dynamically (max 4 items) -->
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card dashboard-card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Upcoming Events</h5>
                                <button class="btn btn-sm btn-outline-primary view-all-btn-mobile"
                                    onclick="goToEvents()">View All</button>
                            </div>
                            <div class="card-body p-0">
                                <ul class="list-group list-group-flush" id="upcoming-events-list">
                                    <!-- Upcoming events will be loaded here -->
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card dashboard-card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Latest Prayer Requests</h5>
                                <button class="btn btn-sm btn-outline-primary"
                                    onclick="document.querySelector('.nav-link[data-section=\'prayer-requests\']').click()">View
                                    All</button>
                            </div>
                            <div class="card-body p-0">
                                <ul class="list-group list-group-flush" id="latest-prayer-requests-list">
                                    <!-- Latest prayer requests will be loaded here -->
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card dashboard-card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Quick Actions</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-6">
                                        <button
                                            class="btn btn-primary w-100 d-flex align-items-center justify-content-center"
                                            onclick="document.getElementById('add-prayer-request-btn').click()">
                                            <i class="fas fa-pray me-2"></i> New Prayer Request
                                        </button>
                                    </div>
                                    <div class="col-6">
                                        <button
                                            class="btn btn-success w-100 d-flex align-items-center justify-content-center"
                                            onclick="document.getElementById('add-event-btn').click()">
                                            <i class="fas fa-calendar-plus me-2"></i> Add Event
                                        </button>
                                    </div>
                                    <div class="col-6">
                                        <button
                                            class="btn btn-warning w-100 d-flex align-items-center justify-content-center"
                                            onclick="document.getElementById('add-sermon-btn').click()">
                                            <i class="fas fa-video me-2"></i> Add Sermon
                                        </button>
                                    </div>
                                    <div class="col-6">
                                        <button
                                            class="btn btn-info w-100 d-flex align-items-center justify-content-center"
                                            onclick="document.getElementById('add-member-btn').click()">
                                            <i class="fas fa-user-plus me-2"></i> Add Member
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>

            <!-- Events Section -->
            <div id="events-section" class="content-section">
                <div class="table-container">
                    <div class="table-header">
                        <h3>Events</h3>
                        <button class="btn btn-primary" id="add-event-btn">
                            <i class="fas fa-plus me-2"></i> Add Event
                        </button>
                    </div>

                    <div id="events-alert" class="alert d-none"></div>

                    <div class="table-responsive-mobile">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Title</th>
                                    <th>Date</th>
                                    <th>Time</th>
                                    <th>Location</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="events-table-body">
                                <!-- Events will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Event Form Section -->
            <div id="event-form-section" class="content-section">
                <div class="table-container">
                    <div class="table-header">
                        <h3 id="event-form-title">Add Event</h3>
                        <button class="btn btn-secondary" id="back-to-events-btn">
                            <i class="fas fa-arrow-left me-2"></i> Back to Events
                        </button>
                    </div>

                    <div id="event-form-alert" class="alert d-none"></div>

                    <form id="event-form" class="form-container">
                        <input type="hidden" id="event-id">

                        <div class="mb-3">
                            <label for="event-title" class="form-label">Title</label>
                            <input type="text" class="form-control" id="event-title" required>
                        </div>

                        <div class="mb-3">
                            <label for="event-description" class="form-label">Description</label>
                            <textarea class="form-control" id="event-description" rows="3" required></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="event-date" class="form-label">Date</label>
                            <input type="date" class="form-control" id="event-date" required>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="event-start-time" class="form-label">Start Time</label>
                                <input type="time" class="form-control" id="event-start-time" required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="event-end-time" class="form-label">End Time</label>
                                <input type="time" class="form-control" id="event-end-time" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="event-location" class="form-label">Location</label>
                            <input type="text" class="form-control" id="event-location" required>
                        </div>

                        <div class="mb-3">
                            <label for="event-image-url" class="form-label">Event Poster/Image URL</label>
                            <input type="url" class="form-control" id="event-image-url"
                                placeholder="https://example.com/event-poster.jpg">
                            <small class="form-text text-muted">Enter the URL for the event poster or image
                                (optional)</small>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="event-featured">
                            <label class="form-check-label" for="event-featured">Featured Event</label>
                        </div>

                        <div class="mb-3">
                            <label for="event-status" class="form-label">Status</label>
                            <select class="form-select" id="event-status" required>
                                <option value="upcoming">Upcoming</option>
                                <option value="ongoing">Ongoing</option>
                                <option value="completed">Completed</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary" onclick="handleEventSubmit(event)">Save
                                Event</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Sermons Section -->
            <div id="sermons-section" class="content-section">
                <div class="table-container">
                    <div class="table-header">
                        <h3>Sermons</h3>
                        <button class="btn btn-primary" id="add-sermon-btn">
                            <i class="fas fa-plus me-2"></i> Add Sermon
                        </button>
                    </div>

                    <div id="sermons-alert" class="alert d-none"></div>

                    <div class="table-responsive-mobile">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Title</th>
                                    <th>Preacher</th>
                                    <th>Date</th>
                                    <th>Featured</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="sermons-table-body">
                                <!-- Sermons will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Sermon Form Section -->
            <div id="sermon-form-section" class="content-section">
                <div class="table-container">
                    <div class="table-header">
                        <h3 id="sermon-form-title">Add Sermon</h3>
                        <button class="btn btn-secondary" id="back-to-sermons-btn">
                            <i class="fas fa-arrow-left me-2"></i> Back to Sermons
                        </button>
                    </div>

                    <div id="sermon-form-alert" class="alert d-none"></div>

                    <form id="sermon-form" class="form-container">
                        <input type="hidden" id="sermon-id">

                        <div class="mb-3">
                            <label for="sermon-title" class="form-label">Title</label>
                            <input type="text" class="form-control" id="sermon-title" required>
                        </div>

                        <div class="mb-3">
                            <label for="sermon-description" class="form-label">Description</label>
                            <textarea class="form-control" id="sermon-description" rows="3" required></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="sermon-preacher" class="form-label">Preacher</label>
                            <input type="text" class="form-control" id="sermon-preacher" required>
                        </div>

                        <div class="mb-3">
                            <label for="sermon-date" class="form-label">Date</label>
                            <input type="date" class="form-control" id="sermon-date" required>
                        </div>

                        <div class="mb-3">
                            <label for="sermon-video-url" class="form-label">Video URL</label>
                            <input type="url" class="form-control" id="sermon-video-url" required>
                            <small class="form-text text-muted">Enter the YouTube or other video platform URL</small>
                        </div>

                        <div class="mb-3">
                            <label for="sermon-thumbnail-url" class="form-label">Thumbnail URL</label>
                            <input type="url" class="form-control" id="sermon-thumbnail-url" required>
                            <small class="form-text text-muted">Enter the URL for the sermon thumbnail image</small>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="sermon-featured">
                            <label class="form-check-label" for="sermon-featured">Featured Sermon</label>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">Save Sermon</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Leadership Team Section -->
            <div id="leadership-section" class="content-section">
                <div class="table-container">
                    <div class="table-header">
                        <h3>Leadership Team</h3>
                        <button class="btn btn-primary" id="add-leader-btn">
                            <i class="fas fa-plus me-2"></i> Add Team Member
                        </button>
                    </div>

                    <div id="leadership-alert" class="alert d-none"></div>

                    <div class="table-responsive-mobile">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Order</th>
                                    <th>Name</th>
                                    <th>Position</th>
                                    <th>Email</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="leadership-table-body">
                                <!-- Leadership team members will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Leadership Form Section -->
            <div id="leadership-form-section" class="content-section">
                <div class="table-container">
                    <div class="table-header">
                        <h3 id="leadership-form-title">Add Team Member</h3>
                        <button class="btn btn-secondary" id="back-to-leadership-btn">
                            <i class="fas fa-arrow-left me-2"></i> Back to Leadership Team
                        </button>
                    </div>

                    <div id="leadership-form-alert" class="alert d-none"></div>

                    <form id="leadership-form" class="form-container">
                        <input type="hidden" id="leader-id">

                        <div class="mb-3">
                            <label for="leader-name" class="form-label">Name</label>
                            <input type="text" class="form-control" id="leader-name" required>
                        </div>

                        <div class="mb-3">
                            <label for="leader-position" class="form-label">Position</label>
                            <input type="text" class="form-control" id="leader-position" required>
                        </div>

                        <div class="mb-3">
                            <label for="leader-bio" class="form-label">Bio</label>
                            <textarea class="form-control" id="leader-bio" rows="3" required></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="leader-email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="leader-email" required>
                        </div>

                        <div class="mb-3">
                            <label for="leader-image-url" class="form-label">Image URL</label>
                            <input type="url" class="form-control" id="leader-image-url" required>
                            <small class="form-text text-muted">Enter the URL for the team member's photo</small>
                        </div>

                        <div class="mb-3">
                            <label for="leader-order" class="form-label">Display Order</label>
                            <input type="number" class="form-control" id="leader-order" min="1" value="1" required>
                            <small class="form-text text-muted">Lower numbers will be displayed first</small>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">Save Team Member</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Blog Section -->
            <div id="blog-section" class="content-section">
                <div class="table-container">
                    <div class="table-header">
                        <h3>Blog Posts</h3>
                        <button class="btn btn-primary" id="add-blog-btn">
                            <i class="fas fa-plus me-2"></i> Add Blog Post
                        </button>
                    </div>

                    <div id="blog-alert" class="alert d-none"></div>

                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Author</th>
                                <th>Date</th>
                                <th>Category</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="blog-table-body">
                            <!-- Blog posts will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Blog Form Section -->
            <div id="blog-form-section" class="content-section">
                <div class="table-container">
                    <div class="table-header">
                        <h3 id="blog-form-title">Add Blog Post</h3>
                        <button class="btn btn-secondary" id="back-to-blog-btn">
                            <i class="fas fa-arrow-left me-2"></i> Back to Blog Posts
                        </button>
                    </div>

                    <div id="blog-form-alert" class="alert d-none"></div>

                    <form id="blog-form" class="form-container">
                        <input type="hidden" id="blog-id">

                        <div class="mb-3">
                            <label for="blog-title" class="form-label">Title</label>
                            <input type="text" class="form-control" id="blog-title" required>
                        </div>

                        <div class="mb-3">
                            <label for="blog-content" class="form-label">Content</label>
                            <textarea class="form-control" id="blog-content" rows="6" required></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="blog-author" class="form-label">Author</label>
                            <input type="text" class="form-control" id="blog-author" required>
                        </div>

                        <div class="mb-3">
                            <label for="blog-date" class="form-label">Date</label>
                            <input type="date" class="form-control" id="blog-date" required>
                        </div>

                        <div class="mb-3">
                            <label for="blog-category" class="form-label">Category</label>
                            <input type="text" class="form-control" id="blog-category" required>
                            <small class="form-text text-muted">E.g., Devotional, Church News, Testimony, etc.</small>
                        </div>

                        <div class="mb-3">
                            <label for="blog-image-url" class="form-label">Image URL</label>
                            <input type="url" class="form-control" id="blog-image-url" required>
                            <small class="form-text text-muted">Enter the URL for the blog post featured image</small>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="blog-featured">
                            <label class="form-check-label" for="blog-featured">Featured Post</label>
                        </div>

                        <div class="mb-3">
                            <label for="blog-status" class="form-label">Status</label>
                            <select class="form-select" id="blog-status" required>
                                <option value="draft">Draft</option>
                                <option value="published">Published</option>
                            </select>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">Save Blog Post</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Gallery Section -->
            <div id="gallery-section" class="content-section">
                <div class="table-container">
                    <div class="table-header">
                        <h3>Gallery</h3>
                        <button class="btn btn-primary" id="add-gallery-btn">
                            <i class="fas fa-plus me-2"></i> Add Image
                        </button>
                    </div>

                    <div id="gallery-alert" class="alert d-none"></div>

                    <div class="row" id="gallery-items-container">
                        <!-- Gallery items will be loaded here -->
                        <div class="col-12 text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Gallery Form Section -->
            <div id="gallery-form-section" class="content-section">
                <div class="table-container">
                    <div class="table-header">
                        <h3 id="gallery-form-title">Add Gallery Image</h3>
                        <button class="btn btn-secondary" id="back-to-gallery-btn">
                            <i class="fas fa-arrow-left me-2"></i> Back to Gallery
                        </button>
                    </div>

                    <div id="gallery-form-alert" class="alert d-none"></div>

                    <form id="gallery-form" class="form-container">
                        <input type="hidden" id="gallery-id">

                        <div class="mb-3">
                            <label for="gallery-title" class="form-label">Title</label>
                            <input type="text" class="form-control" id="gallery-title" required>
                        </div>

                        <div class="mb-3">
                            <label for="gallery-description" class="form-label">Description</label>
                            <textarea class="form-control" id="gallery-description" rows="3" required></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="gallery-category" class="form-label">Category</label>
                            <input type="text" class="form-control" id="gallery-category" required>
                            <small class="form-text text-muted">E.g., Worship, Youth, Outreach, etc.</small>
                        </div>

                        <div class="mb-3">
                            <label for="gallery-date" class="form-label">Date</label>
                            <input type="date" class="form-control" id="gallery-date" required>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Image Upload</label>
                            <div class="image-upload-container">
                                <div class="image-drop-zone" id="gallery-drop-zone">
                                    <div class="drop-zone-content">
                                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                        <h5>Drag & Drop Image Here</h5>
                                        <p class="text-muted">or click to browse files</p>
                                        <button type="button" class="btn btn-outline-primary" id="gallery-browse-btn">
                                            <i class="fas fa-folder-open me-2"></i>Browse Files
                                        </button>
                                    </div>
                                    <input type="file" id="gallery-image-file" accept="image/*" style="display: none;">
                                </div>
                                <div class="image-preview" id="gallery-image-preview" style="display: none;">
                                    <img id="gallery-preview-img" src="" alt="Preview" class="img-fluid rounded">
                                    <button type="button" class="btn btn-sm btn-danger remove-image-btn"
                                        id="gallery-remove-image">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="mt-2">
                                <label for="gallery-image-url" class="form-label">Or enter Image URL</label>
                                <input type="url" class="form-control" id="gallery-image-url"
                                    placeholder="https://example.com/image.jpg">
                                <small class="form-text text-muted">You can either upload an image or provide a
                                    URL</small>
                            </div>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="gallery-featured">
                            <label class="form-check-label" for="gallery-featured">Featured Image</label>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">Save Gallery Image</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Members Section -->
            <div id="members-section" class="content-section">
                <div class="table-container">
                    <div class="table-header">
                        <h3 class="table-title">Church Members Directory</h3>
                        <div>
                            <button class="btn btn-primary" id="add-member-btn" onclick="openAddMemberForm()">
                                <i class="fas fa-plus me-2"></i> Add Member
                            </button>
                        </div>
                    </div>

                    <div id="members-alert" class="alert d-none"></div>

                    <div class="members-search mb-4">
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" id="member-search" placeholder="Search members...">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button"
                                data-bs-toggle="dropdown">
                                Filter
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="#" data-filter="all">All Members</a></li>
                                <li><a class="dropdown-item" href="#" data-filter="active">Active Members</a></li>
                                <li><a class="dropdown-item" href="#" data-filter="inactive">Inactive Members</a></li>
                                <li>
                                    <hr class="dropdown-divider">
                                </li>
                                <li><a class="dropdown-item" href="#" data-filter="worship">Worship Team</a></li>
                                <li><a class="dropdown-item" href="#" data-filter="children">Children's Ministry</a>
                                </li>
                                <li><a class="dropdown-item" href="#" data-filter="outreach">Outreach</a></li>
                            </ul>
                        </div>
                    </div>

                    <div class="table-responsive-mobile">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Profile</th>
                                    <th>Name</th>
                                    <th>Contact Details</th>
                                    <th>Address</th>
                                    <th>Ministry</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="members-table-body">
                                <!-- Members will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <div class="members-pagination">
                        <nav>
                            <ul class="pagination justify-content-center">
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" tabindex="-1">Previous</a>
                                </li>
                                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                <li class="page-item"><a class="page-link" href="#">2</a></li>
                                <li class="page-item"><a class="page-link" href="#">3</a></li>
                                <li class="page-item">
                                    <a class="page-link" href="#">Next</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- Member Form Section -->
            <div id="member-form-section" class="content-section">
                <div class="table-container">
                    <div class="table-header">
                        <h3 class="table-title" id="member-form-title">Add Member</h3>
                        <button class="btn btn-secondary" id="back-to-members-btn">
                            <i class="fas fa-arrow-left me-2"></i> Back to Members
                        </button>
                    </div>

                    <div id="member-form-alert" class="alert d-none"></div>

                    <form id="member-form" class="form-container">
                        <input type="hidden" id="member-id">

                        <div class="row">
                            <div class="mb-3">
                                <label for="member-name" class="form-label">Full Name</label>
                                <input type="text" class="form-control" id="member-name" required>
                            </div>

                            <!-- <div class="mb-3">
                                <label for="member-profile-picture" class="form-label">Profile Picture URL</label>
                                <input type="url" class="form-control" id="member-profile-picture"
                                    placeholder="https://example.com/profile.jpg">
                                <small class="form-text text-muted">Enter the URL for the member's profile
                                    picture</small>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="member-email" class="form-label">Email Address</label>
                                <input type="email" class="form-control" id="member-email" required>
                            </div> -->
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="member-phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="member-phone" required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="member-ministry" class="form-label">Ministry</label>
                                <select class="form-select" id="member-ministry" required>
                                    <option value="">Select Ministry</option>
                                    <option value="Worship Team">Worship Team</option>
                                    <option value="Children's Ministry">Children's Ministry</option>
                                    <option value="Outreach">Outreach</option>
                                    <option value="Prayer Team">Prayer Team</option>
                                    <option value="Hospitality">Hospitality</option>
                                    <option value="Media">Media</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="member-address" class="form-label">Address</label>
                            <input type="text" class="form-control" id="member-address" required>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="member-birthdate" class="form-label">Birthdate</label>
                                <input type="date" class="form-control" id="member-birthdate" required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="member-since" class="form-label">Member Since</label>
                                <input type="date" class="form-control" id="member-since" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="member-status" class="form-label">Status</label>
                                <select class="form-select" id="member-status" required>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="member-photo" class="form-label">Profile Photo</label>
                                <input type="file" class="form-control" id="member-photo">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="member-notes" class="form-label">Additional Notes</label>
                            <textarea class="form-control" id="member-notes" rows="3"></textarea>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">Save Member</button>
                            <button type="button" class="btn btn-secondary" onclick="saveMember()">Test Save Member
                                Function</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Prayer Requests Section -->
            <div id="prayer-requests-section" class="content-section">
                <div class="table-container">
                    <div class="table-header">
                        <h3 class="table-title">Prayer Requests</h3>
                        <div class="btn-group">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button"
                                data-bs-toggle="dropdown" aria-expanded="false">
                                Filter by Status
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item prayer-filter" href="#" data-filter="all">All Requests</a>
                                </li>
                                <li><a class="dropdown-item prayer-filter" href="#" data-filter="pending">Pending</a>
                                </li>
                                <li><a class="dropdown-item prayer-filter" href="#" data-filter="praying">Praying</a>
                                </li>
                                <li><a class="dropdown-item prayer-filter" href="#" data-filter="answered">Answered</a>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div id="prayer-requests-alert" class="alert d-none"></div>

                    <div class="prayer-requests-search mb-4">
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" id="prayer-request-search"
                                placeholder="Search prayer requests...">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button"
                                data-bs-toggle="dropdown">
                                Filter by Category
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item prayer-category-filter" href="#" data-filter="all">All
                                        Categories</a></li>
                                <li><a class="dropdown-item prayer-category-filter" href="#"
                                        data-filter="Healing">Healing</a></li>
                                <li><a class="dropdown-item prayer-category-filter" href="#"
                                        data-filter="Deliverance">Deliverance</a></li>
                                <li><a class="dropdown-item prayer-category-filter" href="#"
                                        data-filter="Finance">Finance</a></li>
                                <li><a class="dropdown-item prayer-category-filter" href="#"
                                        data-filter="Job & Business">Job & Business</a></li>
                                <li><a class="dropdown-item prayer-category-filter" href="#"
                                        data-filter="Family">Family</a></li>
                                <li><a class="dropdown-item prayer-category-filter" href="#"
                                        data-filter="Spiritual Growth">Spiritual Growth</a></li>
                                <li><a class="dropdown-item prayer-category-filter" href="#"
                                        data-filter="Other">Other</a></li>
                            </ul>
                        </div>
                    </div>

                    <div class="table-responsive-mobile">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Name</th>
                                    <th>Category</th>
                                    <th>Request</th>
                                    <th>Contact</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="prayer-requests-table-body">
                                <!-- Prayer requests will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <div class="prayer-requests-pagination">
                        <nav>
                            <ul class="pagination justify-content-center">
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" tabindex="-1">Previous</a>
                                </li>
                                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                <li class="page-item"><a class="page-link" href="#">2</a></li>
                                <li class="page-item"><a class="page-link" href="#">3</a></li>
                                <li class="page-item">
                                    <a class="page-link" href="#">Next</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- Prayer Request Detail Modal -->
            <div class="modal fade" id="prayer-request-modal" tabindex="-1" aria-labelledby="prayer-request-modal-label"
                aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="prayer-request-modal-label">Prayer Request Details</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div id="prayer-request-details">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <h6>Name</h6>
                                        <p id="prayer-detail-name"></p>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Date Submitted</h6>
                                        <p id="prayer-detail-date"></p>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <h6>Email</h6>
                                        <p id="prayer-detail-email"></p>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Phone</h6>
                                        <p id="prayer-detail-phone"></p>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <h6>Category</h6>
                                        <p id="prayer-detail-category"></p>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Status</h6>
                                        <select class="form-select" id="prayer-detail-status">
                                            <option value="pending">Pending</option>
                                            <option value="praying">Praying</option>
                                            <option value="answered">Answered</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <h6>Prayer Request</h6>
                                    <div class="p-3 bg-light rounded" id="prayer-detail-request"></div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox"
                                                id="prayer-detail-confidential" disabled>
                                            <label class="form-check-label" for="prayer-detail-confidential">
                                                Keep this request confidential
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="prayer-detail-contact"
                                                disabled>
                                            <label class="form-check-label" for="prayer-detail-contact">
                                                Contact me about this request
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <h6>Notes</h6>
                                    <textarea class="form-control" id="prayer-detail-notes" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-primary" id="save-prayer-request-btn">Save
                                Changes</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Direct navigation functions
        function goToEvents() {
            // Hide all sections
            document.querySelectorAll('.content-section').forEach(section => section.classList.remove('active'));

            // Remove active class from all nav links
            document.querySelectorAll('.nav-link').forEach(link => link.classList.remove('active'));

            // Add active class to the events nav link
            document.querySelector('.nav-link[data-section="events"]').classList.add('active');

            // Show the events section
            document.getElementById('events-section').classList.add('active');

            // Update the section title
            document.getElementById('current-section-title').textContent = 'Events';
        }

        // Add direct navigation buttons
        window.addEventListener('load', function () {
            // Add direct navigation to the Manage Events button
            const manageEventsBtn = document.querySelector('a[data-section="events"].nav-link-btn');
            if (manageEventsBtn) {
                manageEventsBtn.addEventListener('click', function (e) {
                    e.preventDefault();
                    goToEvents();
                });
            }

            // Fix sidebar navigation
            const sidebarLinks = document.querySelectorAll('.nav-link[data-section]');
            sidebarLinks.forEach(link => {
                link.addEventListener('click', function (e) {
                    e.preventDefault();
                    const section = this.getAttribute('data-section');

                    // Hide all sections
                    document.querySelectorAll('.content-section').forEach(s => s.classList.remove('active'));

                    // Remove active class from all nav links
                    document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));

                    // Add active class to the clicked nav link
                    this.classList.add('active');

                    // Show the corresponding section
                    if (section === 'dashboard') {
                        document.getElementById('dashboard-section').classList.add('active');
                        document.getElementById('current-section-title').textContent = 'Dashboard';
                    } else if (section === 'events') {
                        document.getElementById('events-section').classList.add('active');
                        document.getElementById('current-section-title').textContent = 'Events';
                    } else if (section === 'sermons') {
                        document.getElementById('sermons-section').classList.add('active');
                        document.getElementById('current-section-title').textContent = 'Sermons';
                    } else if (section === 'leadership') {
                        document.getElementById('leadership-section').classList.add('active');
                        document.getElementById('current-section-title').textContent = 'Leadership Team';
                    } else if (section === 'blog') {
                        document.getElementById('blog-section').classList.add('active');
                        document.getElementById('current-section-title').textContent = 'Blog';
                    } else if (section === 'gallery') {
                        document.getElementById('gallery-section').classList.add('active');
                        document.getElementById('current-section-title').textContent = 'Gallery';
                    } else if (section === 'members') {
                        document.getElementById('members-section').classList.add('active');
                        document.getElementById('current-section-title').textContent = 'Members';
                        // Load members when the section is shown
                        loadMembers();
                    } else if (section === 'prayer-requests') {
                        document.getElementById('prayer-requests-section').classList.add('active');
                        document.getElementById('current-section-title').textContent = 'Prayer Requests';
                        // Load prayer requests when the section is shown
                        loadPrayerRequests();
                    }
                });
            });
        });
        // API URL - Updated for production deployment
        const API_URL = 'https://sda-church-production.up.railway.app/api'; // Production API URL

        // DOM Elements - General
        const loginSection = document.getElementById('login-section');
        const dashboardSection = document.getElementById('dashboard-section');
        const loginForm = document.getElementById('login-form');
        const loginAlert = document.getElementById('login-alert');
        const logoutLink = document.getElementById('logout-link');
        const userName = document.getElementById('user-name');
        const currentSectionTitle = document.getElementById('current-section-title');
        const eventsCount = document.getElementById('events-count');
        const sermonsCount = document.getElementById('sermons-count');
        const blogCount = document.getElementById('blog-count');
        const galleryCount = document.getElementById('gallery-count');
        const recentActivityList = document.getElementById('recent-activity-list');
        const upcomingEventsList = document.getElementById('upcoming-events-list');

        // DOM Elements - Events
        const eventsSection = document.getElementById('events-section');
        const eventFormSection = document.getElementById('event-form-section');
        const eventsTableBody = document.getElementById('events-table-body');
        const eventsAlert = document.getElementById('events-alert');
        const eventForm = document.getElementById('event-form');
        const eventFormTitle = document.getElementById('event-form-title');
        const eventFormAlert = document.getElementById('event-form-alert');
        const addEventBtn = document.getElementById('add-event-btn');
        const backToEventsBtn = document.getElementById('back-to-events-btn');

        // DOM Elements - Sermons
        const sermonsSection = document.getElementById('sermons-section');
        const sermonFormSection = document.getElementById('sermon-form-section');
        const sermonsTableBody = document.getElementById('sermons-table-body');
        const sermonsAlert = document.getElementById('sermons-alert');
        const sermonForm = document.getElementById('sermon-form');
        const sermonFormTitle = document.getElementById('sermon-form-title');
        const sermonFormAlert = document.getElementById('sermon-form-alert');
        const addSermonBtn = document.getElementById('add-sermon-btn');
        const backToSermonsBtn = document.getElementById('back-to-sermons-btn');

        // DOM Elements - Leadership
        const leadershipSection = document.getElementById('leadership-section');
        const leadershipFormSection = document.getElementById('leadership-form-section');
        const leadershipTableBody = document.getElementById('leadership-table-body');
        const leadershipAlert = document.getElementById('leadership-alert');
        const leadershipForm = document.getElementById('leadership-form');
        const leadershipFormTitle = document.getElementById('leadership-form-title');
        const leadershipFormAlert = document.getElementById('leadership-form-alert');
        const addLeaderBtn = document.getElementById('add-leader-btn');
        const backToLeadershipBtn = document.getElementById('back-to-leadership-btn');

        // DOM Elements - Blog
        const blogSection = document.getElementById('blog-section');
        const blogFormSection = document.getElementById('blog-form-section');
        const blogTableBody = document.getElementById('blog-table-body');
        const blogAlert = document.getElementById('blog-alert');
        const blogForm = document.getElementById('blog-form');
        const blogFormTitle = document.getElementById('blog-form-title');
        const blogFormAlert = document.getElementById('blog-form-alert');
        const addBlogBtn = document.getElementById('add-blog-btn');
        const backToBlogBtn = document.getElementById('back-to-blog-btn');

        // DOM Elements - Gallery
        const gallerySection = document.getElementById('gallery-section');
        const galleryFormSection = document.getElementById('gallery-form-section');
        const galleryItemsContainer = document.getElementById('gallery-items-container');
        const galleryAlert = document.getElementById('gallery-alert');
        const galleryForm = document.getElementById('gallery-form');
        const galleryFormTitle = document.getElementById('gallery-form-title');
        const galleryFormAlert = document.getElementById('gallery-form-alert');
        const addGalleryBtn = document.getElementById('add-gallery-btn');
        const backToGalleryBtn = document.getElementById('back-to-gallery-btn');

        // DOM Elements - Members
        const membersSection = document.getElementById('members-section');
        const memberFormSection = document.getElementById('member-form-section');
        const membersTableBody = document.getElementById('members-table-body');
        const membersAlert = document.getElementById('members-alert');
        const memberForm = document.getElementById('member-form');
        const memberFormTitle = document.getElementById('member-form-title');
        const memberFormAlert = document.getElementById('member-form-alert');
        const addMemberBtn = document.getElementById('add-member-btn');
        const backToMembersBtn = document.getElementById('back-to-members-btn');

        // Check if user is logged in
        function checkAuth() {
            const token = localStorage.getItem('token');
            const user = JSON.parse(localStorage.getItem('user') || '{}');

            if (token && user.name) {
                loginSection.classList.add('d-none');
                dashboardSection.classList.remove('d-none');
                userName.textContent = user.name;

                // Load dashboard data
                loadDashboardData();
            } else {
                loginSection.classList.remove('d-none');
                dashboardSection.classList.add('d-none');
            }
        }

        // Load dashboard data
        function loadDashboardData() {
            // Load counts and data for dashboard overview
            loadDashboardCounts();
            loadRecentActivity();
            loadUpcomingEvents();
            loadLatestPrayerRequests();

            // Load data for each section
            loadEvents();
            loadSermons();
            loadLeadershipTeam();
            // loadBlogPosts(); // Commented out - function not implemented yet
            loadGallery();
            loadMembers();
            loadPrayerRequests();
        }

        // Load dashboard counts
        async function loadDashboardCounts() {
            try {
                // Get counts from API
                const [membersResponse, eventsResponse, prayerRequestsResponse, sermonsResponse] = await Promise.all([
                    fetch(`${API_URL}/members`),
                    fetch(`${API_URL}/events`),
                    fetch(`${API_URL}/prayer-requests`),
                    fetch(`${API_URL}/sermons`)
                ]);

                // Check if responses are ok
                if (!membersResponse.ok || !eventsResponse.ok || !prayerRequestsResponse.ok || !sermonsResponse.ok) {
                    throw new Error('Failed to fetch dashboard counts');
                }

                // Parse responses
                const members = await membersResponse.json();
                const events = await eventsResponse.json();
                const prayerRequests = await prayerRequestsResponse.json();
                const sermons = await sermonsResponse.json();

                // Update counts in the UI
                document.getElementById('members-count').textContent = members.length;
                document.getElementById('events-count').textContent = events.length;
                document.getElementById('prayer-requests-count').textContent = prayerRequests.length;
                document.getElementById('sermons-count').textContent = sermons.length;

                // Count active members
                const activeMembers = members.filter(member => member.status === 'active').length;

                // Count upcoming events (events in the future)
                const upcomingEvents = events.filter(event => new Date(event.date) > new Date()).length;

                // Count pending prayer requests
                const pendingPrayerRequests = prayerRequests.filter(request => request.status === 'pending').length;

                // Update stat footers
                document.querySelector('.stat-card:nth-child(1) .stat-label').textContent = `${activeMembers} Active Members`;
                document.querySelector('.stat-card:nth-child(2) .stat-label').textContent = `${pendingPrayerRequests} Pending Requests`;
                document.querySelector('.stat-card:nth-child(3) .stat-label').textContent = `${upcomingEvents} Upcoming Events`;

            } catch (err) {
                console.error('Error loading dashboard counts:', err);
            }
        }

        // Load recent activity
        async function loadRecentActivity() {
            try {
                // Get counts from API
                const [eventsResponse, sermonsResponse, blogResponse, galleryResponse] = await Promise.all([
                    fetch(`${API_URL}/events`),
                    fetch(`${API_URL}/sermons`),
                    fetch(`${API_URL}/blog`),
                    fetch(`${API_URL}/gallery`)
                ]);

                const events = await eventsResponse.json();
                const sermons = await sermonsResponse.json();
                const blogPosts = await blogResponse.json();
                const galleryItems = await galleryResponse.json();

                // Update count elements
                eventsCount.textContent = events.length;
                sermonsCount.textContent = sermons.length;
                blogCount.textContent = blogPosts.length;
                galleryCount.textContent = galleryItems.length;
            } catch (err) {
                console.error('Error loading dashboard counts:', err);
            }
        }

        // Load recent activity
        async function loadRecentActivity() {
            try {
                // For a real application, you would have an API endpoint for recent activity
                // For this demo, we'll create some sample activity
                const recentActivity = [
                    { type: 'event', action: 'added', title: 'Community Service Day', date: new Date() },
                    { type: 'blog', action: 'updated', title: 'Walking in Faith', date: new Date(Date.now() - 86400000) },
                    { type: 'gallery', action: 'added', title: '5 new images', date: new Date(Date.now() - 172800000) }
                ];

                recentActivityList.innerHTML = '';

                // Limit to 4 recent activities
                recentActivity.slice(0, 4).forEach(activity => {
                    const li = document.createElement('li');
                    li.className = 'list-group-item';

                    let icon = '';
                    switch (activity.action) {
                        case 'added':
                            icon = '<i class="fas fa-plus-circle text-success"></i>';
                            break;
                        case 'updated':
                            icon = '<i class="fas fa-edit text-primary"></i>';
                            break;
                        case 'deleted':
                            icon = '<i class="fas fa-trash text-danger"></i>';
                            break;
                        default:
                            icon = '<i class="fas fa-info-circle text-info"></i>';
                    }

                    const formattedDate = formatDate(activity.date);

                    li.innerHTML = `
                        <div class="d-flex">
                            <div class="activity-icon bg-${activity.action === 'added' ? 'success' : activity.action === 'updated' ? 'primary' : 'danger'}-light text-${activity.action === 'added' ? 'success' : activity.action === 'updated' ? 'primary' : 'danger'}">
                                ${icon}
                            </div>
                            <div class="ms-3">
                                <h6 class="mb-1">${activity.type.charAt(0).toUpperCase() + activity.type.slice(1)} "${activity.title}" was ${activity.action}</h6>
                                <p class="text-muted small mb-0">${formattedDate}</p>
                            </div>
                        </div>
                    `;

                    recentActivityList.appendChild(li);
                });
            } catch (err) {
                console.error('Error loading recent activity:', err);
                recentActivityList.innerHTML = '<li class="list-group-item text-center">Error loading recent activity</li>';
            }
        }

        // Load latest prayer requests for dashboard
        async function loadLatestPrayerRequests() {
            try {
                const response = await fetch(`${API_URL}/prayer-requests`);

                if (!response.ok) {
                    throw new Error('Failed to fetch prayer requests');
                }

                const prayerRequests = await response.json();

                // Get latest prayer requests
                const latestRequests = prayerRequests
                    .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
                    .slice(0, 3); // Get the 3 most recent requests

                const latestRequestsList = document.getElementById('latest-prayer-requests-list');

                if (latestRequests.length === 0) {
                    latestRequestsList.innerHTML = '<li class="list-group-item text-center">No prayer requests</li>';
                    return;
                }

                latestRequestsList.innerHTML = '';

                // Limit to 3 latest prayer requests
                latestRequests.slice(0, 3).forEach(request => {
                    const requestDate = new Date(request.createdAt);
                    const formattedDate = formatDate(request.createdAt);

                    // Truncate request text if too long
                    const truncatedRequest = request.request.length > 60
                        ? request.request.substring(0, 60) + '...'
                        : request.request;

                    // Set status badge color
                    let statusBadgeClass = 'bg-secondary';
                    if (request.status === 'pending') {
                        statusBadgeClass = 'bg-warning text-dark';
                    } else if (request.status === 'praying') {
                        statusBadgeClass = 'bg-primary';
                    } else if (request.status === 'answered') {
                        statusBadgeClass = 'bg-success';
                    }

                    const listItem = document.createElement('li');
                    listItem.className = 'list-group-item';
                    listItem.innerHTML = `
                        <div>
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <h6 class="mb-0">${request.name}</h6>
                                <span class="badge ${statusBadgeClass}">${capitalizeFirstLetter(request.status)}</span>
                            </div>
                            <span class="badge bg-info">${request.category}</span>
                            <p class="mb-1">${truncatedRequest}</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">${formattedDate}</small>

                            </div>
                        </div>
                    `;

                    listItem.addEventListener('click', () => {
                        // Navigate to prayer requests section and view this request
                        goToPrayerRequests();
                        viewPrayerRequest(request.id);
                    });

                    latestRequestsList.appendChild(listItem);
                });

            } catch (err) {
                console.error('Error loading latest prayer requests:', err);
                const latestRequestsList = document.getElementById('latest-prayer-requests-list');
                latestRequestsList.innerHTML = '<li class="list-group-item text-center">Error loading prayer requests</li>';
            }
        }

        // Load upcoming events
        async function loadUpcomingEvents() {
            try {
                const response = await fetch(`${API_URL}/events`);
                const events = await response.json();

                // Sort events by date and filter to only show upcoming events
                const upcomingEvents = events
                    .filter(event => new Date(event.date) >= new Date())
                    .sort((a, b) => new Date(a.date) - new Date(b.date))
                    .slice(0, 3); // Show only the next 3 events

                upcomingEventsList.innerHTML = '';

                if (upcomingEvents.length === 0) {
                    upcomingEventsList.innerHTML = '<li class="list-group-item text-center">No upcoming events</li>';
                    return;
                }

                upcomingEvents.forEach(event => {
                    const li = document.createElement('li');
                    li.className = 'list-group-item';

                    const eventDate = new Date(event.date);
                    const formattedDate = eventDate.toLocaleDateString();

                    li.innerHTML = `
                        <h6>${event.title}</h6>
                        <p class="mb-0">
                            <i class="far fa-calendar me-2"></i>
                            ${formattedDate}
                        </p>
                        <p class="mb-0">
                            <i class="far fa-clock me-2"></i>
                            ${event.startTime} - ${event.endTime}
                        </p>
                    `;

                    upcomingEventsList.appendChild(li);
                });
            } catch (err) {
                console.error('Error loading upcoming events:', err);
                upcomingEventsList.innerHTML = '<li class="list-group-item text-center">Error loading upcoming events</li>';
            }
        }

        // Setup navigation
        function setupNavigation() {
            // Get all nav links
            const navLinks = document.querySelectorAll('.nav-link[data-section]');
            const navLinkBtns = document.querySelectorAll('.nav-link-btn[data-section]');

            // Get all content sections
            const contentSections = document.querySelectorAll('.content-section');

            console.log('Content sections found:', contentSections.length);
            contentSections.forEach(section => {
                console.log('Section ID:', section.id, 'Class:', section.className);
            });

            // Function to show a section
            function showSection(sectionId) {
                console.log('Showing section:', sectionId);

                // Hide all sections
                contentSections.forEach(section => section.classList.remove('active'));

                // Remove active class from all nav links
                navLinks.forEach(link => link.classList.remove('active'));

                // Add active class to the clicked nav link
                const activeLink = document.querySelector(`.nav-link[data-section="${sectionId}"]`);
                if (activeLink) {
                    activeLink.classList.add('active');
                    console.log('Active link set:', activeLink.getAttribute('data-section'));
                } else {
                    console.log('No active link found for section:', sectionId);
                }

                // Show the selected section
                const sectionToShow = document.getElementById(`${sectionId}-section`);
                if (sectionToShow) {
                    sectionToShow.classList.add('active');
                    console.log('Section shown:', sectionToShow.id);

                    // Set the section title based on the nav link text
                    if (activeLink && activeLink.querySelector('span')) {
                        currentSectionTitle.textContent = activeLink.querySelector('span').textContent;
                    } else {
                        currentSectionTitle.textContent = sectionId.charAt(0).toUpperCase() + sectionId.slice(1);
                    }

                    // Load data for specific sections when they are shown
                    if (sectionId === 'members') {
                        console.log('🔄 Loading members for members section...');
                        loadMembers();
                    }
                } else if (sectionId === 'dashboard') {
                    dashboardSection.classList.add('active');
                    currentSectionTitle.textContent = 'Dashboard';
                    console.log('Dashboard section shown');

                    // Load dashboard data
                    loadDashboardCounts();
                    loadRecentActivity();
                    loadUpcomingEvents();
                    loadLatestPrayerRequests();
                } else if (sectionId === 'prayer-requests') {
                    document.getElementById('prayer-requests-section').classList.add('active');
                    currentSectionTitle.textContent = 'Prayer Requests';
                    console.log('Prayer requests section shown');

                    // Load prayer requests data
                    loadPrayerRequests();
                } else {
                    console.log('Section not found:', `${sectionId}-section`);
                }

                // If on mobile, close the sidebar
                if (window.innerWidth < 992) {
                    document.querySelector('.sidebar').classList.remove('active');
                }
            }

            // Add click event to nav links
            navLinks.forEach(link => {
                link.addEventListener('click', function (e) {
                    e.preventDefault();
                    const sectionId = this.getAttribute('data-section');
                    showSection(sectionId);
                });
            });

            // Add click event to nav link buttons
            navLinkBtns.forEach(btn => {
                btn.addEventListener('click', function (e) {
                    e.preventDefault();
                    const sectionId = this.getAttribute('data-section');
                    showSection(sectionId);
                });
            });

            // Make the navigation functions available globally
            window.goToSection = function (sectionId) {
                showSection(sectionId);
            };

            window.goToEvents = function () {
                showSection('events');
            };

            window.goToPrayerRequests = function () {
                showSection('prayer-requests');
            };

            window.goToMembers = function () {
                showSection('members');
            };

            window.goToDashboard = function () {
                showSection('dashboard');
            };
        }

        // Login Form Submit
        loginForm.addEventListener('submit', async function (e) {
            e.preventDefault();

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            console.log('🔐 LOGIN ATTEMPT:');
            console.log('Email:', email);
            console.log('Password length:', password.length);
            console.log('API URL:', `${API_URL}/auth/login`);

            try {
                const loginData = { email, password };
                console.log('📤 Sending login data:', loginData);

                const response = await fetch(`${API_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(loginData)
                });

                console.log('📡 Response status:', response.status);
                console.log('📡 Response ok:', response.ok);

                const data = await response.json();
                console.log('📥 Response data:', data);

                if (response.ok) {
                    console.log('✅ Login successful!');
                    localStorage.setItem('token', data.token);
                    localStorage.setItem('user', JSON.stringify(data.user || { name: 'Admin User' }));
                    checkAuth();
                } else {
                    console.log('❌ Login failed:', data);
                    loginAlert.textContent = data.error || data.message || 'Login failed';
                    loginAlert.classList.remove('d-none');
                }
            } catch (err) {
                loginAlert.textContent = 'An error occurred. Please try again.';
                loginAlert.classList.remove('d-none');
                console.error(err);
            }
        });

        // Logout
        logoutLink.addEventListener('click', function (e) {
            e.preventDefault();
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            checkAuth();
        });

        // Load Events
        async function loadEvents() {
            try {
                console.log('🔄 Loading events...');
                const response = await fetch(`${API_URL}/events`);

                console.log('📡 Events response status:', response.status);
                console.log('📡 Events response ok:', response.ok);

                if (!response.ok) {
                    throw new Error('Failed to load events');
                }

                const events = await response.json();
                console.log('📋 Loaded events:', events);

                eventsTableBody.innerHTML = '';

                events.forEach(event => {
                    const row = document.createElement('tr');

                    const date = new Date(event.date);
                    const formattedDate = date.toLocaleDateString();

                    row.innerHTML = `
                        <td>${event.title}</td>
                        <td>${formattedDate}</td>
                        <td>${event.startTime} - ${event.endTime}</td>
                        <td>${event.location}</td>
                        <td><span class="badge bg-primary">${event.status}</span></td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary me-2 edit-event-btn" data-id="${event.id}">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger delete-event-btn" data-id="${event.id}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    `;

                    eventsTableBody.appendChild(row);
                });

                // Add event listeners to edit and delete buttons
                document.querySelectorAll('.edit-event-btn').forEach(btn => {
                    btn.addEventListener('click', function () {
                        const eventId = parseInt(this.getAttribute('data-id'));
                        editEvent(eventId);
                    });
                });

                document.querySelectorAll('.delete-event-btn').forEach(btn => {
                    btn.addEventListener('click', function () {
                        const eventId = parseInt(this.getAttribute('data-id'));
                        deleteEvent(eventId);
                    });
                });
            } catch (err) {
                showAlert(eventsAlert, 'Error loading events', 'danger');
                console.error(err);
            }
        }

        // Add Event Button
        addEventBtn.addEventListener('click', function () {
            eventForm.reset();
            document.getElementById('event-id').value = '';
            eventFormTitle.textContent = 'Add Event';
            eventsSection.classList.remove('active');
            eventFormSection.classList.add('active');
            document.getElementById('current-section-title').textContent = 'Add Event';
        });

        // Back to Events Button
        backToEventsBtn.addEventListener('click', function () {
            eventFormSection.classList.remove('active');
            eventsSection.classList.add('active');
            document.getElementById('current-section-title').textContent = 'Events';
        });

        // Edit Event
        async function editEvent(eventId) {
            try {
                const token = localStorage.getItem('token');
                const response = await fetch(`${API_URL}/events/${eventId}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (!response.ok) {
                    throw new Error('Failed to load event details');
                }

                const event = await response.json();

                document.getElementById('event-id').value = event.id;
                document.getElementById('event-title').value = event.title;
                document.getElementById('event-description').value = event.description;
                document.getElementById('event-date').value = event.date;
                document.getElementById('event-start-time').value = event.startTime;
                document.getElementById('event-end-time').value = event.endTime;
                document.getElementById('event-location').value = event.location;
                document.getElementById('event-featured').checked = event.featured;
                document.getElementById('event-status').value = event.status;

                eventFormTitle.textContent = 'Edit Event';
                eventsSection.classList.remove('active');
                eventFormSection.classList.add('active');
                document.getElementById('current-section-title').textContent = 'Edit Event';
            } catch (err) {
                showAlert(eventsAlert, 'Error loading event details', 'danger');
                console.error(err);
            }
        }

        // Delete Event
        async function deleteEvent(eventId) {
            if (confirm('Are you sure you want to delete this event?')) {
                try {
                    const token = localStorage.getItem('token');
                    const response = await fetch(`${API_URL}/events/${eventId}`, {
                        method: 'DELETE',
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });

                    if (!response.ok) {
                        throw new Error('Failed to delete event');
                    }

                    showAlert(eventsAlert, 'Event deleted successfully', 'success');

                    // Reload events
                    loadEvents();
                } catch (err) {
                    showAlert(eventsAlert, 'Error deleting event', 'danger');
                    console.error(err);
                }
            }
        }

        // Event Form Submit - REMOVED DUPLICATE (handled in setupNavigation function)

        // Direct event submit handler as fallback
        function handleEventSubmit(event) {
            console.log('🔥 DIRECT EVENT SUBMIT HANDLER CALLED');
            event.preventDefault();
            saveEvent();
            return false;
        }

        // Helper function to show alerts
        function showAlert(element, message, type) {
            element.textContent = message;
            element.className = `alert alert-${type}`;
            element.classList.remove('d-none');

            setTimeout(() => {
                element.classList.add('d-none');
            }, 5000);
        }

        // The updateMainWebsite function is now handled by the server

        // Load Sermons
        async function loadSermons() {
            try {
                const response = await fetch(`${API_URL}/sermons`);
                const sermons = await response.json();

                sermonsTableBody.innerHTML = '';

                sermons.forEach(sermon => {
                    const row = document.createElement('tr');

                    const date = new Date(sermon.date);
                    const formattedDate = formatDate(sermon.date);

                    row.innerHTML = `
                        <td>${sermon.title}</td>
                        <td>${sermon.preacher}</td>
                        <td>${formattedDate}</td>
                        <td>${sermon.featured ? '<span class="badge bg-success">Featured</span>' : '<span class="badge bg-secondary">No</span>'}</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn-action btn-view" onclick="viewSermon(${sermon.id})" title="View Sermon">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn-action btn-edit edit-sermon-btn" data-id="${sermon.id}" title="Edit Sermon">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn-action btn-delete delete-sermon-btn" data-id="${sermon.id}" title="Delete Sermon">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    `;

                    sermonsTableBody.appendChild(row);
                });

                // Add event listeners to edit and delete buttons
                document.querySelectorAll('.edit-sermon-btn').forEach(btn => {
                    btn.addEventListener('click', function () {
                        const sermonId = parseInt(this.getAttribute('data-id'));
                        editSermon(sermonId);
                    });
                });

                document.querySelectorAll('.delete-sermon-btn').forEach(btn => {
                    btn.addEventListener('click', function () {
                        const sermonId = parseInt(this.getAttribute('data-id'));
                        deleteSermon(sermonId);
                    });
                });
            } catch (err) {
                showAlert(sermonsAlert, 'Error loading sermons', 'danger');
                console.error(err);
            }
        }

        // Edit Sermon
        async function editSermon(sermonId) {
            try {
                const response = await fetch(`${API_URL}/sermons/${sermonId}`);

                if (!response.ok) {
                    throw new Error('Failed to load sermon details');
                }

                const sermon = await response.json();

                document.getElementById('sermon-id').value = sermon.id;
                document.getElementById('sermon-title').value = sermon.title;
                document.getElementById('sermon-description').value = sermon.description;
                document.getElementById('sermon-preacher').value = sermon.preacher;
                document.getElementById('sermon-date').value = sermon.date;
                document.getElementById('sermon-video-url').value = sermon.videoUrl;
                document.getElementById('sermon-thumbnail-url').value = sermon.thumbnailUrl;
                document.getElementById('sermon-featured').checked = sermon.featured;

                sermonFormTitle.textContent = 'Edit Sermon';
                sermonsSection.classList.remove('active');
                sermonFormSection.classList.add('active');
                currentSectionTitle.textContent = 'Edit Sermon';
            } catch (err) {
                showAlert(sermonsAlert, 'Error loading sermon details', 'danger');
                console.error(err);
            }
        }

        // Delete Sermon
        async function deleteSermon(sermonId) {
            if (confirm('Are you sure you want to delete this sermon?')) {
                try {
                    const response = await fetch(`${API_URL}/sermons/${sermonId}`, {
                        method: 'DELETE'
                    });

                    if (!response.ok) {
                        throw new Error('Failed to delete sermon');
                    }

                    showAlert(sermonsAlert, 'Sermon deleted successfully', 'success');

                    // Reload sermons
                    loadSermons();

                    // Reload dashboard counts
                    loadDashboardCounts();
                } catch (err) {
                    showAlert(sermonsAlert, 'Error deleting sermon', 'danger');
                    console.error(err);
                }
            }
        }

        // Add Sermon Button
        if (addSermonBtn) {
            addSermonBtn.addEventListener('click', function () {
                sermonForm.reset();
                document.getElementById('sermon-id').value = '';
                sermonFormTitle.textContent = 'Add Sermon';
                sermonsSection.classList.remove('active');
                sermonFormSection.classList.add('active');
                currentSectionTitle.textContent = 'Add Sermon';
            });
        }

        // Back to Sermons Button
        if (backToSermonsBtn) {
            backToSermonsBtn.addEventListener('click', function () {
                sermonFormSection.classList.remove('active');
                sermonsSection.classList.add('active');
                currentSectionTitle.textContent = 'Sermons';
            });
        }

        // Sermon Form Submit
        if (sermonForm) {
            sermonForm.addEventListener('submit', async function (e) {
                e.preventDefault();

                const sermonId = document.getElementById('sermon-id').value;
                const sermonData = {
                    title: document.getElementById('sermon-title').value,
                    description: document.getElementById('sermon-description').value,
                    preacher: document.getElementById('sermon-preacher').value,
                    date: document.getElementById('sermon-date').value,
                    videoUrl: document.getElementById('sermon-video-url').value,
                    thumbnailUrl: document.getElementById('sermon-thumbnail-url').value,
                    featured: document.getElementById('sermon-featured').checked
                };

                try {
                    let url = `${API_URL}/sermons`;
                    let method = 'POST';

                    if (sermonId) {
                        url = `${API_URL}/sermons/${sermonId}`;
                        method = 'PUT';
                    }

                    const response = await fetch(url, {
                        method,
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(sermonData)
                    });

                    if (!response.ok) {
                        throw new Error(`Failed to ${sermonId ? 'update' : 'add'} sermon`);
                    }

                    showAlert(sermonFormAlert, `Sermon ${sermonId ? 'updated' : 'added'} successfully`, 'success');

                    // Show toast notification
                    showToast(`Sermon ${sermonId ? 'updated' : 'added'} successfully!`, 'success');

                    setTimeout(() => {
                        sermonFormSection.classList.remove('active');
                        sermonsSection.classList.add('active');
                        currentSectionTitle.textContent = 'Sermons';
                        loadSermons();

                        // Reload dashboard counts
                        loadDashboardCounts();
                    }, 1500);
                } catch (err) {
                    showAlert(sermonFormAlert, `Error ${sermonId ? 'updating' : 'adding'} sermon`, 'danger');
                    console.error(err);
                }
            });
        }

        // Load Leadership Team
        async function loadLeadershipTeam() {
            try {
                const response = await fetch(`${API_URL}/leadership`);
                const leaders = await response.json();

                // Sort by order
                leaders.sort((a, b) => a.order - b.order);

                leadershipTableBody.innerHTML = '';

                leaders.forEach(leader => {
                    const row = document.createElement('tr');

                    row.innerHTML = `
                        <td>${leader.order}</td>
                        <td>${leader.name}</td>
                        <td>${leader.position}</td>
                        <td>${leader.email}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary me-2 edit-leader-btn" data-id="${leader.id}">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger delete-leader-btn" data-id="${leader.id}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    `;

                    leadershipTableBody.appendChild(row);
                });

                // Add event listeners to edit and delete buttons
                document.querySelectorAll('.edit-leader-btn').forEach(btn => {
                    btn.addEventListener('click', function () {
                        const leaderId = parseInt(this.getAttribute('data-id'));
                        editLeader(leaderId);
                    });
                });

                document.querySelectorAll('.delete-leader-btn').forEach(btn => {
                    btn.addEventListener('click', function () {
                        const leaderId = parseInt(this.getAttribute('data-id'));
                        deleteLeader(leaderId);
                    });
                });
            } catch (err) {
                showAlert(leadershipAlert, 'Error loading leadership team', 'danger');
                console.error(err);
            }
        }

        // Edit Leader
        async function editLeader(leaderId) {
            try {
                const response = await fetch(`${API_URL}/leadership/${leaderId}`);

                if (!response.ok) {
                    throw new Error('Failed to load leader details');
                }

                const leader = await response.json();

                document.getElementById('leader-id').value = leader.id;
                document.getElementById('leader-name').value = leader.name;
                document.getElementById('leader-position').value = leader.position;
                document.getElementById('leader-bio').value = leader.bio;
                document.getElementById('leader-email').value = leader.email;
                document.getElementById('leader-image-url').value = leader.imageUrl;
                document.getElementById('leader-order').value = leader.order;

                leadershipFormTitle.textContent = 'Edit Team Member';
                leadershipSection.classList.remove('active');
                leadershipFormSection.classList.add('active');
                currentSectionTitle.textContent = 'Edit Team Member';
            } catch (err) {
                showAlert(leadershipAlert, 'Error loading leader details', 'danger');
                console.error(err);
            }
        }

        // Delete Leader
        async function deleteLeader(leaderId) {
            if (confirm('Are you sure you want to delete this team member?')) {
                try {
                    const response = await fetch(`${API_URL}/leadership/${leaderId}`, {
                        method: 'DELETE'
                    });

                    if (!response.ok) {
                        throw new Error('Failed to delete team member');
                    }

                    showAlert(leadershipAlert, 'Team member deleted successfully', 'success');

                    // Reload leadership team
                    loadLeadershipTeam();

                    // Reload dashboard counts
                    loadDashboardCounts();
                } catch (err) {
                    showAlert(leadershipAlert, 'Error deleting team member', 'danger');
                    console.error(err);
                }
            }
        }

        // Add Leader Button
        if (addLeaderBtn) {
            addLeaderBtn.addEventListener('click', function () {
                leadershipForm.reset();
                document.getElementById('leader-id').value = '';
                leadershipFormTitle.textContent = 'Add Team Member';
                leadershipSection.classList.remove('active');
                leadershipFormSection.classList.add('active');
                currentSectionTitle.textContent = 'Add Team Member';
            });
        }

        // Back to Leadership Button
        if (backToLeadershipBtn) {
            backToLeadershipBtn.addEventListener('click', function () {
                leadershipFormSection.classList.remove('active');
                leadershipSection.classList.add('active');
                currentSectionTitle.textContent = 'Leadership Team';
            });
        }

        // Leadership Form Submit
        if (leadershipForm) {
            leadershipForm.addEventListener('submit', async function (e) {
                e.preventDefault();

                const leaderId = document.getElementById('leader-id').value;
                const leaderData = {
                    name: document.getElementById('leader-name').value,
                    position: document.getElementById('leader-position').value,
                    bio: document.getElementById('leader-bio').value,
                    email: document.getElementById('leader-email').value,
                    imageUrl: document.getElementById('leader-image-url').value,
                    order: parseInt(document.getElementById('leader-order').value)
                };

                try {
                    let url = `${API_URL}/leadership`;
                    let method = 'POST';

                    if (leaderId) {
                        url = `${API_URL}/leadership/${leaderId}`;
                        method = 'PUT';
                    }

                    const response = await fetch(url, {
                        method,
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(leaderData)
                    });

                    if (!response.ok) {
                        throw new Error(`Failed to ${leaderId ? 'update' : 'add'} team member`);
                    }

                    showAlert(leadershipFormAlert, `Team member ${leaderId ? 'updated' : 'added'} successfully`, 'success');

                    setTimeout(() => {
                        leadershipFormSection.classList.remove('active');
                        leadershipSection.classList.add('active');
                        currentSectionTitle.textContent = 'Leadership Team';
                        loadLeadershipTeam();

                        // Reload dashboard counts
                        loadDashboardCounts();
                    }, 1500);
                } catch (err) {
                    showAlert(leadershipFormAlert, `Error ${leaderId ? 'updating' : 'adding'} team member`, 'danger');
                    console.error(err);
                }
            });
        }

        // Load events from the API
        async function loadEvents() {
            try {
                const response = await fetch(`${API_URL}/events`);
                const events = await response.json();

                // Update events count
                document.getElementById('events-count').textContent = events.length;

                // Clear the events table
                const eventsTableBody = document.getElementById('events-table-body');
                eventsTableBody.innerHTML = '';

                // Display events in the table
                if (events.length === 0) {
                    eventsTableBody.innerHTML = `
                        <tr>
                            <td colspan="6" class="text-center">No events found</td>
                        </tr>
                    `;
                    return;
                }

                // Sort events by date (newest first)
                events.sort((a, b) => new Date(b.date) - new Date(a.date));

                // Add events to the table
                events.forEach(event => {
                    const eventDate = new Date(event.date);
                    const formattedDate = eventDate.toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                    });

                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${event.title}</td>
                        <td>${formattedDate}</td>
                        <td>${event.startTime} - ${event.endTime}</td>
                        <td>${event.location}</td>
                        <td><span class="badge bg-${event.status === 'upcoming' ? 'success' : event.status === 'ongoing' ? 'primary' : event.status === 'completed' ? 'secondary' : 'danger'}">${event.status}</span></td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn-action btn-view" onclick="viewEvent(${event.id})" title="View Event">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn-action btn-edit edit-event-btn" data-id="${event.id}" title="Edit Event">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn-action btn-delete delete-event-btn" data-id="${event.id}" title="Delete Event">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    `;

                    eventsTableBody.appendChild(row);
                });

                // Add event listeners to edit and delete buttons
                document.querySelectorAll('.edit-event-btn').forEach(btn => {
                    btn.addEventListener('click', function () {
                        const eventId = this.getAttribute('data-id');
                        editEvent(eventId);
                    });
                });

                document.querySelectorAll('.delete-event-btn').forEach(btn => {
                    btn.addEventListener('click', function () {
                        const eventId = this.getAttribute('data-id');
                        deleteEvent(eventId);
                    });
                });

                // Update upcoming events list in dashboard
                updateUpcomingEventsList(events);

            } catch (err) {
                console.error('Error loading events:', err);
                showAlert('events-alert', 'Error loading events. Please try again.', 'danger');
            }
        }

        // Update upcoming events list in dashboard
        function updateUpcomingEventsList(events) {
            const upcomingEventsList = document.getElementById('upcoming-events-list');
            upcomingEventsList.innerHTML = '';

            // Filter upcoming events
            const upcomingEvents = events.filter(event =>
                new Date(event.date) >= new Date() && event.status === 'upcoming'
            );

            // Sort by date (soonest first)
            upcomingEvents.sort((a, b) => new Date(a.date) - new Date(b.date));

            // Display upcoming events
            if (upcomingEvents.length === 0) {
                upcomingEventsList.innerHTML = `
                    <li class="list-group-item text-center">No upcoming events</li>
                `;
                return;
            }

            // Add upcoming events to the list (limit to 3)
            upcomingEvents.slice(0, 3).forEach(event => {
                const eventDate = new Date(event.date);
                const formattedDate = formatEventDate(event.date); // Use event date format

                const listItem = document.createElement('li');
                listItem.className = 'list-group-item';
                listItem.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>${event.title}</strong>
                            <div class="text-muted">${formattedDate} | ${event.startTime} - ${event.endTime}</div>
                            <div class="text-muted">${event.location}</div>
                        </div>
                        <span class="badge bg-success">Upcoming</span>
                    </div>
                `;

                upcomingEventsList.appendChild(listItem);
            });
        }

        // Save event (create or update)
        async function saveEvent() {
            try {
                console.log('🚀 SAVING EVENT - Starting...');

                // Get form values
                const eventId = document.getElementById('event-id').value;
                const title = document.getElementById('event-title').value;
                const description = document.getElementById('event-description').value;
                const date = document.getElementById('event-date').value;
                const startTime = document.getElementById('event-start-time').value;
                const endTime = document.getElementById('event-end-time').value;
                const location = document.getElementById('event-location').value;
                const imageUrl = document.getElementById('event-image-url').value;
                const featured = document.getElementById('event-featured').checked;
                const status = document.getElementById('event-status').value;

                console.log('📝 Form values:', {
                    eventId, title, description, date, startTime, endTime, location, imageUrl, featured, status
                });

                // Validate required fields
                if (!title || !description || !date || !location) {
                    throw new Error('Please fill in all required fields');
                }

                // Create event object
                const eventData = {
                    title,
                    description,
                    date,
                    startTime,
                    endTime,
                    location,
                    imageUrl,
                    featured,
                    status
                };

                console.log('📦 Event data to send:', eventData);

                let response;
                let url;
                let method;

                // Update or create event
                if (eventId) {
                    // Update existing event
                    url = `${API_URL}/events/${eventId}`;
                    method = 'PUT';
                } else {
                    // Create new event
                    url = `${API_URL}/events`;
                    method = 'POST';
                }

                console.log(`🌐 Making ${method} request to: ${url}`);

                const token = localStorage.getItem('token');
                response = await fetch(url, {
                    method,
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(eventData)
                });

                console.log('📡 Response status:', response.status);
                console.log('📡 Response ok:', response.ok);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('❌ Response error:', errorText);
                    throw new Error(`Failed to save event: ${response.status} - ${errorText}`);
                }

                const savedEvent = await response.json();
                console.log('✅ Event saved successfully:', savedEvent);

                // Show success message
                showAlert(eventFormAlert, `Event ${eventId ? 'updated' : 'added'} successfully!`, 'success');

                // Show toast notification
                showToast(`Event ${eventId ? 'updated' : 'added'} successfully!`, 'success');

                // Reload events
                console.log('🔄 Reloading events...');
                loadEvents();

                // Reload dashboard counts
                loadDashboardCounts();

                // Go back to events list after a short delay
                setTimeout(() => {
                    const eventFormSection = document.getElementById('event-form-section');
                    const eventsSection = document.getElementById('events-section');
                    const currentSectionTitle = document.getElementById('current-section-title');

                    if (eventFormSection) eventFormSection.classList.remove('active');
                    if (eventsSection) eventsSection.classList.add('active');
                    if (currentSectionTitle) currentSectionTitle.textContent = 'Events';

                    console.log('🔙 Navigated back to events list');
                }, 1500);

            } catch (err) {
                console.error('❌ Error saving event:', err);
                showAlert(eventFormAlert, `Error saving event: ${err.message}`, 'danger');
            }
        }

        // Edit event
        async function editEvent(eventId) {
            try {
                // Fetch event details
                const response = await fetch(`${API_URL}/events/${eventId}`);
                const event = await response.json();

                // Populate form
                document.getElementById('event-id').value = event.id;
                document.getElementById('event-title').value = event.title;
                document.getElementById('event-description').value = event.description;
                document.getElementById('event-date').value = event.date;
                document.getElementById('event-start-time').value = event.startTime;
                document.getElementById('event-end-time').value = event.endTime;
                document.getElementById('event-location').value = event.location;
                document.getElementById('event-image-url').value = event.imageUrl || '';
                document.getElementById('event-featured').checked = event.featured;
                document.getElementById('event-status').value = event.status;

                // Update form title
                eventFormTitle.textContent = 'Edit Event';

                // Show form
                eventsSection.classList.remove('active');
                eventFormSection.classList.add('active');
                currentSectionTitle.textContent = 'Edit Event';

            } catch (err) {
                console.error('Error loading event details:', err);
                showAlert('events-alert', 'Error loading event details. Please try again.', 'danger');
            }
        }

        // Delete event
        async function deleteEvent(eventId) {
            // Confirm deletion
            if (!confirm('Are you sure you want to delete this event?')) {
                return;
            }

            try {
                // Delete event
                const response = await fetch(`${API_URL}/events/${eventId}`, {
                    method: 'DELETE'
                });

                if (!response.ok) {
                    throw new Error('Failed to delete event');
                }

                // Show success message
                showAlert('events-alert', 'Event deleted successfully!', 'success');

                // Reload events
                loadEvents();

            } catch (err) {
                console.error('Error deleting event:', err);
                showAlert('events-alert', 'Error deleting event. Please try again.', 'danger');
            }
        }

        // Show alert message
        function showAlert(alertId, message, type) {
            const alertElement = document.getElementById(alertId);
            alertElement.className = `alert alert-${type}`;
            alertElement.textContent = message;
            alertElement.classList.remove('d-none');

            // Hide alert after 5 seconds
            setTimeout(() => {
                alertElement.classList.add('d-none');
            }, 5000);
        }

        // Load leadership team from the API
        async function loadLeadership() {
            try {
                console.log('Fetching leadership team from API...');

                // Show loading message
                const leadershipTableBody = document.getElementById('leadership-table-body');
                leadershipTableBody.innerHTML = `
                    <tr>
                        <td colspan="5" class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <div>Loading leadership team...</div>
                        </td>
                    </tr>
                `;

                // Fetch leadership team from API
                const response = await fetch(`${API_URL}/leadership`);
                console.log('API response status:', response.status);

                if (!response.ok) {
                    throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
                }

                const leaders = await response.json();
                console.log('Leadership team loaded:', leaders);

                // Update leadership count
                if (document.getElementById('leadership-count')) {
                    document.getElementById('leadership-count').textContent = leaders.length;
                }

                // Clear the leadership table
                leadershipTableBody.innerHTML = '';

                // Display leadership team in the table
                if (leaders.length === 0) {
                    leadershipTableBody.innerHTML = `
                        <tr>
                            <td colspan="5" class="text-center">No team members found</td>
                        </tr>
                    `;
                    return;
                }

                // Sort leaders by order
                leaders.sort((a, b) => (a.order || 999) - (b.order || 999));

                // Add leaders to the table
                leaders.forEach(leader => {
                    console.log('Adding leader to table:', leader);

                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${leader.order || '-'}</td>
                        <td>${leader.name}</td>
                        <td>${leader.position}</td>
                        <td>${leader.email}</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn-action btn-view" onclick="viewLeader(${leader.id})" title="View Leader">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn-action btn-edit edit-leader-btn" data-id="${leader.id}" title="Edit Leader">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn-action btn-delete delete-leader-btn" data-id="${leader.id}" title="Delete Leader">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    `;

                    leadershipTableBody.appendChild(row);
                });

                // Add event listeners to edit and delete buttons
                document.querySelectorAll('.edit-leader-btn').forEach(btn => {
                    btn.addEventListener('click', function () {
                        const leaderId = this.getAttribute('data-id');
                        console.log('Edit leader clicked, ID:', leaderId);
                        editLeader(leaderId);
                    });
                });

                document.querySelectorAll('.delete-leader-btn').forEach(btn => {
                    btn.addEventListener('click', function () {
                        const leaderId = this.getAttribute('data-id');
                        console.log('Delete leader clicked, ID:', leaderId);
                        deleteLeader(leaderId);
                    });
                });

                console.log('Leadership team displayed successfully');

            } catch (err) {
                console.error('Error loading leadership team:', err);
                console.error('Error details:', err.message, err.stack);

                // Show error message in the table
                const leadershipTableBody = document.getElementById('leadership-table-body');
                leadershipTableBody.innerHTML = `
                    <tr>
                        <td colspan="5" class="text-center text-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Error loading leadership team: ${err.message}
                        </td>
                    </tr>
                `;

                showAlert('leadership-alert', 'Error loading leadership team: ' + err.message, 'danger');
            }
        }

        // Save leader (create or update)
        async function saveLeader() {
            try {
                console.log('Saving team member...');

                // Show loading message
                showAlert('leadership-form-alert', 'Saving team member...', 'info');

                // Get form values
                const leaderId = document.getElementById('leader-id').value;
                const name = document.getElementById('leader-name').value;
                const position = document.getElementById('leader-position').value;
                const bio = document.getElementById('leader-bio').value;
                const email = document.getElementById('leader-email').value;
                const imageUrl = document.getElementById('leader-image-url').value;
                const order = document.getElementById('leader-order').value;

                console.log('Form values:', {
                    leaderId,
                    name,
                    position,
                    bio,
                    email,
                    imageUrl,
                    order
                });

                // Create leader object
                const leaderData = {
                    name,
                    position,
                    bio,
                    email,
                    imageUrl,
                    order: parseInt(order)
                };

                console.log('Leader data to save:', leaderData);

                let response;
                let url;

                // Update or create leader
                if (leaderId) {
                    // Update existing leader
                    url = `${API_URL}/leadership/${leaderId}`;
                    console.log('Updating existing leader, URL:', url);

                    const token = localStorage.getItem('token');
                    response = await fetch(url, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${token}`
                        },
                        body: JSON.stringify(leaderData)
                    });
                } else {
                    // Create new leader
                    url = `${API_URL}/leadership`;
                    console.log('Creating new leader, URL:', url);

                    const token = localStorage.getItem('token');
                    response = await fetch(url, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${token}`
                        },
                        body: JSON.stringify(leaderData)
                    });
                }

                console.log('API response status:', response.status);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('API error response:', errorText);
                    throw new Error(`Failed to save team member: ${response.status} ${response.statusText}`);
                }

                const savedLeader = await response.json();
                console.log('Team member saved successfully:', savedLeader);

                // Show success message
                showAlert('leadership-form-alert', 'Team member saved successfully!', 'success');

                // Show toast notification
                showToast('Team member saved successfully!', 'success');

                // Reload leadership team
                loadLeadership();

                // Go back to leadership list after a short delay
                setTimeout(() => {
                    leadershipFormSection.classList.remove('active');
                    leadershipSection.classList.add('active');
                    currentSectionTitle.textContent = 'Leadership Team';
                }, 1500);

            } catch (err) {
                console.error('Error saving team member:', err);
                console.error('Error details:', err.message, err.stack);
                showAlert('leadership-form-alert', 'Error saving team member: ' + err.message, 'danger');
            }
        }

        // Edit leader
        async function editLeader(leaderId) {
            try {
                // Fetch leader details
                const response = await fetch(`${API_URL}/leadership/${leaderId}`);
                const leader = await response.json();

                // Populate form
                document.getElementById('leader-id').value = leader.id;
                document.getElementById('leader-name').value = leader.name;
                document.getElementById('leader-position').value = leader.position;
                document.getElementById('leader-bio').value = leader.bio || '';
                document.getElementById('leader-email').value = leader.email;
                document.getElementById('leader-image-url').value = leader.imageUrl;
                document.getElementById('leader-order').value = leader.order || 1;

                // Update form title
                leadershipFormTitle.textContent = 'Edit Team Member';

                // Show form
                leadershipSection.classList.remove('active');
                leadershipFormSection.classList.add('active');
                currentSectionTitle.textContent = 'Edit Team Member';

            } catch (err) {
                console.error('Error loading team member details:', err);
                showAlert('leadership-alert', 'Error loading team member details. Please try again.', 'danger');
            }
        }

        // Delete leader
        async function deleteLeader(leaderId) {
            // Confirm deletion
            if (!confirm('Are you sure you want to delete this team member?')) {
                return;
            }

            try {
                // Delete leader
                const response = await fetch(`${API_URL}/leadership/${leaderId}`, {
                    method: 'DELETE'
                });

                if (!response.ok) {
                    throw new Error('Failed to delete team member');
                }

                // Show success message
                showAlert('leadership-alert', 'Team member deleted successfully!', 'success');

                // Reload leadership team
                loadLeadership();

            } catch (err) {
                console.error('Error deleting team member:', err);
                showAlert('leadership-alert', 'Error deleting team member. Please try again.', 'danger');
            }
        }

        // Load members
        async function loadMembers() {
            try {
                console.log('🔄 Loading members...');
                console.log('🌐 API URL:', API_URL);
                console.log('📍 Making request to:', `${API_URL}/members`);

                // Show loading indicator
                membersTableBody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <div class="mt-2">Loading members...</div>
                        </td>
                    </tr>
                `;

                console.log('⏰ Starting fetch request...');
                const response = await fetch(`${API_URL}/members`);
                console.log('📡 Members API response status:', response.status);
                console.log('📡 Response headers:', response.headers);
                console.log('📡 Response ok:', response.ok);

                if (!response.ok) {
                    throw new Error(`Failed to fetch members: ${response.status} ${response.statusText}`);
                }

                console.log('📥 Getting response text...');
                const responseText = await response.text();
                console.log('📄 Raw response text:', responseText);

                console.log('🔄 Parsing JSON...');
                const members = JSON.parse(responseText);
                console.log('👥 Members received:', members);
                console.log('📊 Number of members:', members.length);
                console.log('🔍 Members array type:', typeof members);
                console.log('🔍 Is array:', Array.isArray(members));

                // Clear the loading indicator
                membersTableBody.innerHTML = '';

                if (members.length === 0) {
                    console.log('⚠️ No members found');
                    membersTableBody.innerHTML = `
                        <tr>
                            <td colspan="7" class="text-center">No members found. Add your first member!</td>
                        </tr>
                    `;
                    return;
                }

                console.log('✅ Processing', members.length, 'members for display');

                // Sort members by name
                members.sort((a, b) => a.name.localeCompare(b.name));

                members.forEach((member, index) => {
                    console.log(`📝 Processing member ${index + 1}:`, member);
                    const row = document.createElement('tr');

                    // Add data attributes for filtering
                    row.setAttribute('data-name', member.name.toLowerCase());
                    row.setAttribute('data-phone', (member.phone || '').toLowerCase());
                    row.setAttribute('data-ministry', member.ministry.toLowerCase());
                    row.setAttribute('data-status', member.status.toLowerCase());

                    row.innerHTML = `
                        <td>
                            <div class="member-profile-picture">
                                ${member.profilePicture ?
                            `<img src="${member.profilePicture}" alt="${member.name}" class="member-profile-img">` :
                            `<div class="member-avatar">${getInitials(member.name)}</div>`
                        }
                            </div>
                        </td>
                        <td>
                            <div>
                                <div class="fw-bold">${member.name}</div>
                                <small class="text-muted">Since ${formatDate(member.memberSince)}</small>
                            </div>
                        </td>
                        <td>
                            <div>
                                ${member.phone ? `<div><i class="fas fa-phone me-1"></i> ${member.phone}</div>` : '<span class="text-muted">No phone provided</span>'}
                            </div>
                        </td>
                        <td>
                            <div class="member-address">
                                ${member.address ? member.address : '<span class="text-muted">No address provided</span>'}
                            </div>
                        </td>
                        <td>${member.ministry}</td>
                        <td><span class="badge bg-${member.status === 'active' ? 'success' : 'secondary'}">${capitalizeFirstLetter(member.status)}</span></td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn-action btn-view" onclick="viewMember(${member.id})" title="View Member">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn-action btn-edit edit-member-btn" data-id="${member.id}" title="Edit Member">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn-action btn-delete delete-member-btn" data-id="${member.id}" title="Delete Member">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    `;

                    membersTableBody.appendChild(row);
                    console.log(`✅ Added member ${index + 1} to table:`, member.name);
                });

                console.log('🎉 All members added to table successfully!');

                // Add event listeners to edit and delete buttons
                document.querySelectorAll('.edit-member-btn').forEach(btn => {
                    btn.addEventListener('click', function () {
                        const memberId = parseInt(this.getAttribute('data-id'));
                        editMember(memberId);
                    });
                });

                document.querySelectorAll('.delete-member-btn').forEach(btn => {
                    btn.addEventListener('click', function () {
                        const memberId = parseInt(this.getAttribute('data-id'));
                        deleteMember(memberId);
                    });
                });

                // Setup search functionality
                setupMemberSearch();
            } catch (err) {
                showAlert(membersAlert, `Error loading members: ${err.message}`, 'danger');
                console.error('Error loading members:', err);

                membersTableBody.innerHTML = `
                    <tr>
                        <td colspan="6" class="text-center text-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Failed to load members. Please try again.
                        </td>
                    </tr>
                `;
            }
        }

        // Setup member search functionality
        function setupMemberSearch() {
            const searchInput = document.getElementById('member-search');
            const filterLinks = document.querySelectorAll('.dropdown-item[data-filter]');

            if (searchInput) {
                searchInput.addEventListener('input', function () {
                    const searchTerm = this.value.toLowerCase().trim();
                    filterMembers(searchTerm);
                });
            }

            if (filterLinks) {
                filterLinks.forEach(link => {
                    link.addEventListener('click', function (e) {
                        e.preventDefault();
                        const filter = this.getAttribute('data-filter');
                        applyFilter(filter);

                        // Update dropdown button text
                        const dropdownButton = document.querySelector('.members-search .dropdown-toggle');
                        if (dropdownButton) {
                            dropdownButton.textContent = this.textContent;
                        }
                    });
                });
            }
        }

        // Filter members based on search term
        function filterMembers(searchTerm) {
            const rows = membersTableBody.querySelectorAll('tr');

            rows.forEach(row => {
                const name = row.getAttribute('data-name') || '';
                const phone = row.getAttribute('data-phone') || '';
                const ministry = row.getAttribute('data-ministry') || '';

                if (name.includes(searchTerm) || phone.includes(searchTerm) || ministry.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });

            updateFilterResults(rows);
        }

        // Apply filter based on category
        function applyFilter(filter) {
            const rows = membersTableBody.querySelectorAll('tr');

            rows.forEach(row => {
                if (filter === 'all') {
                    row.style.display = '';
                } else if (filter === 'active' || filter === 'inactive') {
                    const status = row.getAttribute('data-status');
                    row.style.display = status === filter ? '' : 'none';
                } else {
                    const ministry = row.getAttribute('data-ministry') || '';
                    row.style.display = ministry.includes(filter) ? '' : 'none';
                }
            });

            updateFilterResults(rows);
        }

        // Update filter results message
        function updateFilterResults(rows) {
            let visibleCount = 0;

            rows.forEach(row => {
                if (row.style.display !== 'none') {
                    visibleCount++;
                }
            });

            // If no results found, show a message
            if (visibleCount === 0 && rows.length > 0) {
                if (!document.getElementById('no-results-row')) {
                    const noResultsRow = document.createElement('tr');
                    noResultsRow.id = 'no-results-row';
                    noResultsRow.innerHTML = `
                        <td colspan="6" class="text-center">
                            <i class="fas fa-search me-2"></i>
                            No members match your search criteria
                        </td>
                    `;
                    membersTableBody.appendChild(noResultsRow);
                }
            } else {
                const noResultsRow = document.getElementById('no-results-row');
                if (noResultsRow) {
                    noResultsRow.remove();
                }
            }
        }

        // Get initials from name
        function getInitials(name) {
            return name
                .split(' ')
                .map(part => part.charAt(0))
                .join('')
                .toUpperCase()
                .substring(0, 2);
        }

        // Format date to DD MMM YYYY format (25 May 2025)
        function formatDate(dateString) {
            if (!dateString) return 'N/A';
            const date = new Date(dateString);
            const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            const day = date.getDate();
            const month = months[date.getMonth()];
            const year = date.getFullYear();
            return `${day} ${month} ${year}`;
        }

        // Format date for events (keep original format)
        function formatEventDate(dateString) {
            if (!dateString) return 'N/A';
            const options = { year: 'numeric', month: 'short', day: 'numeric' };
            return new Date(dateString).toLocaleDateString(undefined, options);
        }

        // Capitalize first letter
        function capitalizeFirstLetter(string) {
            return string.charAt(0).toUpperCase() + string.slice(1);
        }

        // Modern View Details Modal Functions
        function showViewModal(title, content) {
            const modal = document.getElementById('view-details-modal');
            const modalTitleText = document.getElementById('modal-title-text');
            const modalBody = document.getElementById('modal-body');

            modalTitleText.textContent = title;
            modalBody.innerHTML = content;

            modal.classList.add('show');
            document.body.style.overflow = 'hidden'; // Prevent background scrolling
        }

        function closeViewModal() {
            const modal = document.getElementById('view-details-modal');
            modal.classList.remove('show');
            document.body.style.overflow = 'auto'; // Restore scrolling
        }

        // Close modal when clicking outside
        document.addEventListener('click', function (e) {
            const modal = document.getElementById('view-details-modal');
            if (e.target === modal) {
                closeViewModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function (e) {
            if (e.key === 'Escape') {
                closeViewModal();
            }
        });

        // Helper function to create detail item HTML
        function createDetailItem(label, value, icon = null, isLink = false, isBadge = false, badgeType = 'secondary') {
            const iconHtml = icon ? `<i class="fas fa-${icon}"></i>` : '';
            let valueHtml = value;

            if (isBadge) {
                valueHtml = `<span class="detail-badge badge-${badgeType}">${value}</span>`;
            } else if (isLink && value && (value.startsWith('http') || value.includes('youtube.com'))) {
                valueHtml = `<a href="${value}" target="_blank" class="detail-link">${value}</a>`;
            }

            return `
                <div class="detail-item">
                    <div class="detail-label">
                        ${iconHtml}
                        ${label}:
                    </div>
                    <div class="detail-value">
                        ${valueHtml || 'Not provided'}
                    </div>
                </div>
            `;
        }

        // Helper function to create description section
        function createDescriptionSection(description) {
            if (!description) return '';
            return `<div class="detail-description">${description}</div>`;
        }

        // Mobile Sidebar Toggle Function
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            const mainContent = document.querySelector('.main-content');

            if (window.innerWidth <= 768) {
                // Small screen (mobile)
                if (!sidebar.classList.contains('show')) {
                    // Sidebar is hidden, so show it and collapse main content
                    sidebar.classList.add('show');
                    mainContent.classList.add('sidebar-collapsed');
                } else {
                    // Sidebar is shown, so hide it and expand main content
                    sidebar.classList.remove('show');
                    mainContent.classList.remove('sidebar-collapsed');
                }
            }
        }

        document.addEventListener('DOMContentLoaded', function () {
            const sidebar = document.querySelector('.sidebar');
            const mainContent = document.querySelector('.main-content');

            if (window.innerWidth <= 768) {
                sidebar.classList.add('show');
                mainContent.classList.add('sidebar-collapsed');
            }
        });

        // Initialize sidebar state on page load
        function initializeSidebar() {
            const sidebar = document.querySelector('.sidebar');
            const mainContent = document.querySelector('.main-content');

            if (window.innerWidth > 768) {
                // On desktop, sidebar is active by default, so no margin adjustment needed
                sidebar.classList.remove('collapsed');
                mainContent.classList.remove('sidebar-collapsed', 'sidebar-hidden');
            }
        }

        // Close mobile sidebar when clicking outside
        document.addEventListener('click', function (e) {
            const sidebar = document.querySelector('.sidebar');
            const mobileToggle = document.getElementById('mobile-toggle');

            if (window.innerWidth <= 768 &&
                sidebar.classList.contains('show') &&
                !sidebar.contains(e.target) &&
                !mobileToggle.contains(e.target)) {
                sidebar.classList.remove('show');
            }
        });

        // Handle window resize
        window.addEventListener('resize', function () {
            const sidebar = document.querySelector('.sidebar');
            const mainContent = document.querySelector('.main-content');

            if (window.innerWidth > 768) {
                // Desktop mode
                sidebar.classList.remove('show'); // Remove mobile show class

                // If sidebar is collapsed, maintain 70px margin
                if (sidebar.classList.contains('collapsed')) {
                    mainContent.classList.add('sidebar-collapsed');
                    mainContent.classList.remove('sidebar-hidden');
                } else {
                    // Sidebar is expanded, no margin needed
                    mainContent.classList.remove('sidebar-collapsed', 'sidebar-hidden');
                }
            } else {
                // Mobile mode
                sidebar.classList.remove('collapsed'); // Remove desktop collapsed class
                mainContent.classList.remove('sidebar-collapsed', 'sidebar-hidden'); // Remove all margin classes
            }
        });

        // Toast notification function
        function showToast(message, type = 'success', duration = 3000) {
            const toastContainer = document.getElementById('toast-container');
            const toastId = 'toast-' + Date.now();

            const toastHTML = `
                <div class="toast toast-${type}" id="${toastId}" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="toast-header">
                        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
                        <strong class="me-auto">${type === 'success' ? 'Success' : type === 'error' ? 'Error' : 'Info'}</strong>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                    <div class="toast-body">
                        ${message}
                    </div>
                </div>
            `;

            toastContainer.insertAdjacentHTML('beforeend', toastHTML);

            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, {
                autohide: true,
                delay: duration
            });

            toast.show();

            // Remove toast element after it's hidden
            toastElement.addEventListener('hidden.bs.toast', () => {
                toastElement.remove();
            });
        }

        // View event details
        async function viewEvent(eventId) {
            try {
                console.log('👁️ Viewing event details for ID:', eventId);

                const response = await fetch(`${API_URL}/events/${eventId}`);
                if (!response.ok) {
                    throw new Error('Failed to fetch event details');
                }

                const event = await response.json();
                console.log('📋 Event details:', event);

                // Create beautiful modal content
                const modalContent = `
                    ${createDetailItem('Title', event.title, 'calendar-alt')}
                    ${createDetailItem('Date', formatEventDate(event.date), 'calendar')}
                    ${createDetailItem('Time', `${event.startTime} - ${event.endTime}`, 'clock')}
                    ${createDetailItem('Location', event.location, 'map-marker-alt')}
                    ${createDetailItem('Status', capitalizeFirstLetter(event.status), 'info-circle', false, true, event.status === 'active' ? 'success' : 'warning')}
                    ${createDetailItem('Featured', event.featured ? 'Yes' : 'No', 'star', false, true, event.featured ? 'success' : 'secondary')}
                    ${event.imageUrl ? createDetailItem('Image', event.imageUrl, 'image', true) : ''}
                    ${event.description ? createDescriptionSection(event.description) : ''}
                `;

                showViewModal('Event Details', modalContent);

            } catch (err) {
                console.error('Error viewing event details:', err);
                showToast('Error loading event details', 'error');
            }
        }

        // View sermon details
        async function viewSermon(sermonId) {
            try {
                console.log('👁️ Viewing sermon details for ID:', sermonId);

                const response = await fetch(`${API_URL}/sermons/${sermonId}`);
                if (!response.ok) {
                    throw new Error('Failed to fetch sermon details');
                }

                const sermon = await response.json();
                console.log('📋 Sermon details:', sermon);

                // Create beautiful modal content
                const modalContent = `
                    ${createDetailItem('Title', sermon.title, 'video')}
                    ${createDetailItem('Preacher', sermon.preacher, 'user')}
                    ${createDetailItem('Date', formatDate(sermon.date), 'calendar')}
                    ${createDetailItem('Featured', sermon.featured ? 'Yes' : 'No', 'star', false, true, sermon.featured ? 'success' : 'secondary')}
                    ${sermon.videoUrl ? createDetailItem('Video URL', sermon.videoUrl, 'play-circle', true) : ''}
                    ${sermon.thumbnailUrl ? createDetailItem('Thumbnail', sermon.thumbnailUrl, 'image', true) : ''}
                    ${sermon.description ? createDescriptionSection(sermon.description) : ''}
                `;

                showViewModal('Sermon Details', modalContent);

            } catch (err) {
                console.error('Error viewing sermon details:', err);
                showToast('Error loading sermon details', 'error');
            }
        }

        // View leader details
        async function viewLeader(leaderId) {
            try {
                console.log('👁️ Viewing leader details for ID:', leaderId);

                const response = await fetch(`${API_URL}/leadership/${leaderId}`);
                if (!response.ok) {
                    throw new Error('Failed to fetch leader details');
                }

                const leader = await response.json();
                console.log('📋 Leader details:', leader);

                // Create beautiful modal content
                const modalContent = `
                    ${createDetailItem('Name', leader.name, 'user')}
                    ${createDetailItem('Position', leader.position, 'briefcase')}
                    ${createDetailItem('Email', leader.email, 'envelope', true)}
                    ${createDetailItem('Display Order', leader.order || 'Not set', 'sort-numeric-up')}
                    ${leader.imageUrl ? createDetailItem('Image', leader.imageUrl, 'image', true) : ''}
                    ${leader.bio ? createDescriptionSection(leader.bio) : ''}
                `;

                showViewModal('Leadership Details', modalContent);

            } catch (err) {
                console.error('Error viewing leader details:', err);
                showToast('Error loading leader details', 'error');
            }
        }

        // Edit prayer request status (quick edit)
        async function editPrayerRequestStatus(requestId) {
            try {
                const response = await fetch(`${API_URL}/prayer-requests/${requestId}`);
                if (!response.ok) {
                    throw new Error('Failed to fetch prayer request details');
                }

                const request = await response.json();
                const newStatus = prompt(`Change status for "${request.name}":\n\nCurrent status: ${request.status}\n\nEnter new status (pending, praying, answered):`, request.status);

                if (newStatus && ['pending', 'praying', 'answered'].includes(newStatus.toLowerCase())) {
                    const updateResponse = await fetch(`${API_URL}/prayer-requests/${requestId}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            status: newStatus.toLowerCase(),
                            notes: request.notes
                        })
                    });

                    if (updateResponse.ok) {
                        showToast('Prayer request status updated successfully!', 'success');
                        loadPrayerRequests();
                    } else {
                        throw new Error('Failed to update status');
                    }
                }

            } catch (err) {
                console.error('Error updating prayer request status:', err);
                showToast('Error updating prayer request status', 'error');
            }
        }

        // View member details
        async function viewMember(memberId) {
            try {
                console.log('👁️ Viewing member details for ID:', memberId);

                // Fetch member details
                const response = await fetch(`${API_URL}/members/${memberId}`);
                if (!response.ok) {
                    throw new Error('Failed to fetch member details');
                }

                const member = await response.json();
                console.log('📋 Member details:', member);

                // Create beautiful modal content
                const modalContent = `
                    ${createDetailItem('Name', member.name, 'user')}
                    ${createDetailItem('Phone', member.phone, 'phone')}
                    ${createDetailItem('Address', member.address, 'home')}
                    ${createDetailItem('Birthdate', member.birthdate ? formatDate(member.birthdate) : 'Not provided', 'birthday-cake')}
                    ${createDetailItem('Member Since', formatDate(member.memberSince), 'calendar-plus')}
                    ${createDetailItem('Ministry', member.ministry, 'hands-helping')}
                    ${createDetailItem('Status', capitalizeFirstLetter(member.status), 'info-circle', false, true, member.status === 'active' ? 'success' : 'warning')}
                    ${member.notes ? createDescriptionSection(member.notes) : ''}
                `;

                showViewModal('Member Details', modalContent);

            } catch (err) {
                console.error('Error viewing member details:', err);
                showToast('Error loading member details', 'error');
            }
        }

        // Edit member
        async function editMember(memberId) {
            try {
                // Show loading message
                showAlert('member-form-alert', 'Loading member details...', 'info');

                // Fetch member details
                const response = await fetch(`${API_URL}/members/${memberId}`);

                if (!response.ok) {
                    throw new Error('Failed to load member details');
                }

                const member = await response.json();

                // Populate form
                document.getElementById('member-id').value = member.id;
                document.getElementById('member-name').value = member.name;
                document.getElementById('member-phone').value = member.phone || '';
                document.getElementById('member-address').value = member.address || '';
                document.getElementById('member-birthdate').value = member.birthdate || '';
                document.getElementById('member-since').value = member.memberSince || '';
                document.getElementById('member-ministry').value = member.ministry || '';
                document.getElementById('member-status').value = member.status || 'active';

                if (document.getElementById('member-notes')) {
                    document.getElementById('member-notes').value = member.notes || '';
                }

                // Update form title
                memberFormTitle.textContent = 'Edit Member';

                // Show form
                membersSection.classList.remove('active');
                memberFormSection.classList.add('active');
                currentSectionTitle.textContent = 'Edit Member';

                // Hide loading message
                document.getElementById('member-form-alert').classList.add('d-none');

            } catch (err) {
                console.error('Error loading member details:', err);
                showAlert('members-alert', 'Error loading member details. Please try again.', 'danger');
            }
        }

        // Delete member
        async function deleteMember(memberId) {
            // Confirm deletion
            if (!confirm('Are you sure you want to delete this member?')) {
                return;
            }

            try {
                // Delete member
                const response = await fetch(`${API_URL}/members/${memberId}`, {
                    method: 'DELETE'
                });

                if (!response.ok) {
                    throw new Error('Failed to delete member');
                }

                // Show success message
                showAlert('members-alert', 'Member deleted successfully!', 'success');

                // Reload members
                loadMembers();

            } catch (err) {
                console.error('Error deleting member:', err);
                showAlert('members-alert', 'Error deleting member. Please try again.', 'danger');
            }
        }

        // Load prayer requests
        async function loadPrayerRequests() {
            try {
                // Show loading indicator
                const prayerRequestsTableBody = document.getElementById('prayer-requests-table-body');
                prayerRequestsTableBody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <div class="mt-2">Loading prayer requests...</div>
                        </td>
                    </tr>
                `;

                const response = await fetch(`${API_URL}/prayer-requests`);

                if (!response.ok) {
                    throw new Error(`Failed to fetch prayer requests: ${response.status} ${response.statusText}`);
                }

                const prayerRequests = await response.json();

                // Clear the loading indicator
                prayerRequestsTableBody.innerHTML = '';

                if (prayerRequests.length === 0) {
                    prayerRequestsTableBody.innerHTML = `
                        <tr>
                            <td colspan="7" class="text-center">No prayer requests found.</td>
                        </tr>
                    `;
                    return;
                }

                // Sort prayer requests by date (newest first)
                prayerRequests.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

                prayerRequests.forEach(request => {
                    const row = document.createElement('tr');

                    // Add data attributes for filtering
                    row.setAttribute('data-name', request.name.toLowerCase());
                    row.setAttribute('data-category', request.category.toLowerCase());
                    row.setAttribute('data-status', request.status.toLowerCase());

                    // Format date
                    const requestDate = new Date(request.createdAt);
                    const formattedDate = formatDate(request.createdAt) + ' ' + requestDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

                    // Truncate request text if too long
                    const truncatedRequest = request.request.length > 50 ? request.request.substring(0, 50) + '...' : request.request;

                    // Set status badge color
                    let statusBadgeClass = 'bg-secondary';
                    if (request.status === 'pending') {
                        statusBadgeClass = 'bg-warning text-dark';
                    } else if (request.status === 'praying') {
                        statusBadgeClass = 'bg-primary';
                    } else if (request.status === 'answered') {
                        statusBadgeClass = 'bg-success';
                    }

                    row.innerHTML = `
                        <td>${formattedDate}</td>
                        <td>
                            <div class="fw-bold">${request.name}</div>
                            ${request.confidential ? '<span class="badge bg-danger">Confidential</span>' : ''}
                        </td>
                        <td><span class="badge bg-info">${request.category}</span></td>
                        <td>${truncatedRequest}</td>
                        <td>
                            ${request.phone ? `<div><i class="fas fa-phone me-1"></i> ${request.phone}</div>` : ''}
                            <div><i class="fas fa-envelope me-1"></i> ${request.email}</div>
                            ${request.contactMe ? '<div class="mt-1"><span class="badge bg-success">Wants Contact</span></div>' : ''}
                        </td>
                        <td><span class="badge ${statusBadgeClass}">${capitalizeFirstLetter(request.status)}</span></td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn-action btn-view view-prayer-request-btn" data-id="${request.id}" title="View Prayer Request">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn-action btn-edit" onclick="editPrayerRequestStatus(${request.id})" title="Edit Status">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn-action btn-delete delete-prayer-request-btn" data-id="${request.id}" title="Delete Request">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    `;

                    prayerRequestsTableBody.appendChild(row);
                });

                // Add event listeners to view and delete buttons
                document.querySelectorAll('.view-prayer-request-btn').forEach(btn => {
                    btn.addEventListener('click', function () {
                        const requestId = parseInt(this.getAttribute('data-id'));
                        viewPrayerRequest(requestId);
                    });
                });

                document.querySelectorAll('.delete-prayer-request-btn').forEach(btn => {
                    btn.addEventListener('click', function () {
                        const requestId = parseInt(this.getAttribute('data-id'));
                        deletePrayerRequest(requestId);
                    });
                });

                // Setup search and filter functionality
                setupPrayerRequestSearch();

            } catch (err) {
                showAlert('prayer-requests-alert', `Error loading prayer requests: ${err.message}`, 'danger');
                console.error('Error loading prayer requests:', err);

                document.getElementById('prayer-requests-table-body').innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center text-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Failed to load prayer requests. Please try again.
                        </td>
                    </tr>
                `;
            }
        }

        // View prayer request details
        async function viewPrayerRequest(requestId) {
            try {
                const response = await fetch(`${API_URL}/prayer-requests/${requestId}`);

                if (!response.ok) {
                    throw new Error('Failed to load prayer request details');
                }

                const request = await response.json();

                // Populate modal with request details
                document.getElementById('prayer-detail-name').textContent = request.name;
                document.getElementById('prayer-detail-date').textContent = formatDate(request.createdAt);
                document.getElementById('prayer-detail-email').textContent = request.email;
                document.getElementById('prayer-detail-phone').textContent = request.phone || 'Not provided';
                document.getElementById('prayer-detail-category').textContent = request.category;
                document.getElementById('prayer-detail-status').value = request.status;
                document.getElementById('prayer-detail-request').textContent = request.request;
                document.getElementById('prayer-detail-confidential').checked = request.confidential;
                document.getElementById('prayer-detail-contact').checked = request.contactMe;
                document.getElementById('prayer-detail-notes').value = request.notes || '';

                // Store the request ID in the save button
                document.getElementById('save-prayer-request-btn').setAttribute('data-id', requestId);

                // Show the modal
                const prayerRequestModal = new bootstrap.Modal(document.getElementById('prayer-request-modal'));
                prayerRequestModal.show();

            } catch (err) {
                console.error('Error loading prayer request details:', err);
                showAlert('prayer-requests-alert', 'Error loading prayer request details. Please try again.', 'danger');
            }
        }

        // Save prayer request changes
        async function savePrayerRequest(requestId) {
            try {
                const status = document.getElementById('prayer-detail-status').value;
                const notes = document.getElementById('prayer-detail-notes').value;

                const response = await fetch(`${API_URL}/prayer-requests/${requestId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        status,
                        notes
                    })
                });

                if (!response.ok) {
                    throw new Error('Failed to update prayer request');
                }

                // Hide the modal
                const prayerRequestModal = bootstrap.Modal.getInstance(document.getElementById('prayer-request-modal'));
                prayerRequestModal.hide();

                // Show success message
                showAlert('prayer-requests-alert', 'Prayer request updated successfully!', 'success');

                // Reload prayer requests
                loadPrayerRequests();

            } catch (err) {
                console.error('Error updating prayer request:', err);
                showAlert('prayer-requests-alert', 'Error updating prayer request. Please try again.', 'danger');
            }
        }

        // Delete prayer request
        async function deletePrayerRequest(requestId) {
            // Confirm deletion
            if (!confirm('Are you sure you want to delete this prayer request?')) {
                return;
            }

            try {
                const response = await fetch(`${API_URL}/prayer-requests/${requestId}`, {
                    method: 'DELETE'
                });

                if (!response.ok) {
                    throw new Error('Failed to delete prayer request');
                }

                // Show success message
                showAlert('prayer-requests-alert', 'Prayer request deleted successfully!', 'success');

                // Reload prayer requests
                loadPrayerRequests();

            } catch (err) {
                console.error('Error deleting prayer request:', err);
                showAlert('prayer-requests-alert', 'Error deleting prayer request. Please try again.', 'danger');
            }
        }

        // Setup prayer request search and filter functionality
        function setupPrayerRequestSearch() {
            const searchInput = document.getElementById('prayer-request-search');
            const statusFilters = document.querySelectorAll('.prayer-filter');
            const categoryFilters = document.querySelectorAll('.prayer-category-filter');

            if (searchInput) {
                searchInput.addEventListener('input', function () {
                    const searchTerm = this.value.toLowerCase().trim();
                    filterPrayerRequests(searchTerm);
                });
            }

            if (statusFilters) {
                statusFilters.forEach(filter => {
                    filter.addEventListener('click', function (e) {
                        e.preventDefault();
                        const filterValue = this.getAttribute('data-filter');
                        filterPrayerRequestsByStatus(filterValue);
                    });
                });
            }

            if (categoryFilters) {
                categoryFilters.forEach(filter => {
                    filter.addEventListener('click', function (e) {
                        e.preventDefault();
                        const filterValue = this.getAttribute('data-filter');
                        filterPrayerRequestsByCategory(filterValue);
                    });
                });
            }
        }

        // Filter prayer requests by search term
        function filterPrayerRequests(searchTerm) {
            const rows = document.querySelectorAll('#prayer-requests-table-body tr');

            rows.forEach(row => {
                const name = row.getAttribute('data-name') || '';
                const category = row.getAttribute('data-category') || '';
                const textContent = row.textContent.toLowerCase();

                if (name.includes(searchTerm) || category.includes(searchTerm) || textContent.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        // Filter prayer requests by status
        function filterPrayerRequestsByStatus(status) {
            const rows = document.querySelectorAll('#prayer-requests-table-body tr');

            rows.forEach(row => {
                if (status === 'all') {
                    row.style.display = '';
                } else {
                    const rowStatus = row.getAttribute('data-status');
                    row.style.display = rowStatus === status ? '' : 'none';
                }
            });
        }

        // Filter prayer requests by category
        function filterPrayerRequestsByCategory(category) {
            const rows = document.querySelectorAll('#prayer-requests-table-body tr');

            rows.forEach(row => {
                if (category === 'all') {
                    row.style.display = '';
                } else {
                    const rowCategory = row.getAttribute('data-category');
                    row.style.display = rowCategory === category.toLowerCase() ? '' : 'none';
                }
            });
        }

        // Global function to open Add Member form - FALLBACK
        window.openAddMemberForm = function () {
            console.log('🚀 GLOBAL openAddMemberForm() called!');

            // Get all required elements
            const membersSection = document.getElementById('members-section');
            const memberFormSection = document.getElementById('member-form-section');
            const memberForm = document.getElementById('member-form');
            const memberFormTitle = document.getElementById('member-form-title');
            const currentSectionTitle = document.getElementById('current-section-title');

            console.log('Elements found:');
            console.log('- Members section:', membersSection);
            console.log('- Member form section:', memberFormSection);
            console.log('- Member form:', memberForm);
            console.log('- Member form title:', memberFormTitle);
            console.log('- Current section title:', currentSectionTitle);

            // Reset form
            if (memberForm) {
                memberForm.reset();
                console.log('✅ Form reset');
            }

            // Clear member ID
            const memberIdField = document.getElementById('member-id');
            if (memberIdField) {
                memberIdField.value = '';
                console.log('✅ Member ID cleared');
            }

            // Set form title
            if (memberFormTitle) {
                memberFormTitle.textContent = 'Add Member';
                console.log('✅ Form title set');
            }

            // Hide members section
            if (membersSection) {
                membersSection.classList.remove('active');
                console.log('✅ Members section hidden');
            }

            // Show member form section
            if (memberFormSection) {
                memberFormSection.classList.add('active');
                console.log('✅ Member form section shown');
            }

            // Update page title
            if (currentSectionTitle) {
                currentSectionTitle.textContent = 'Add Member';
                console.log('✅ Page title updated');
            }

            console.log('🎉 GLOBAL openAddMemberForm() COMPLETE!');
        };

        // Setup Add Member Button - AGGRESSIVE FIX
        function setupAddMemberButton() {
            console.log('=== SETTING UP ADD MEMBER BUTTON ===');

            // Try multiple ways to find the button
            let addMemberBtn = document.getElementById('add-member-btn');
            console.log('Add Member button by ID:', addMemberBtn);

            if (!addMemberBtn) {
                // Try querySelector
                addMemberBtn = document.querySelector('#add-member-btn');
                console.log('Add Member button by querySelector:', addMemberBtn);
            }

            if (!addMemberBtn) {
                // Try finding by text content
                const buttons = document.querySelectorAll('button');
                for (let btn of buttons) {
                    if (btn.textContent.includes('Add Member')) {
                        addMemberBtn = btn;
                        console.log('Found Add Member button by text content:', btn);
                        break;
                    }
                }
            }

            if (addMemberBtn) {
                console.log('✅ ADD MEMBER BUTTON FOUND!');
                console.log('Button element:', addMemberBtn);
                console.log('Button ID:', addMemberBtn.id);
                console.log('Button classes:', addMemberBtn.className);

                // Remove any existing event listeners
                const newBtn = addMemberBtn.cloneNode(true);
                addMemberBtn.parentNode.replaceChild(newBtn, addMemberBtn);
                addMemberBtn = newBtn;

                // Add event listener
                addMemberBtn.addEventListener('click', function (e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('🚀 ADD MEMBER BUTTON CLICKED!');

                    // Get all required elements
                    const membersSection = document.getElementById('members-section');
                    const memberFormSection = document.getElementById('member-form-section');
                    const memberForm = document.getElementById('member-form');
                    const memberFormTitle = document.getElementById('member-form-title');
                    const currentSectionTitle = document.getElementById('current-section-title');

                    console.log('Members section:', membersSection);
                    console.log('Member form section:', memberFormSection);
                    console.log('Member form:', memberForm);
                    console.log('Member form title:', memberFormTitle);
                    console.log('Current section title:', currentSectionTitle);

                    // Reset form
                    if (memberForm) {
                        memberForm.reset();
                        console.log('✅ Form reset');
                    }

                    // Clear member ID
                    const memberIdField = document.getElementById('member-id');
                    if (memberIdField) {
                        memberIdField.value = '';
                        console.log('✅ Member ID cleared');
                    }

                    // Set form title
                    if (memberFormTitle) {
                        memberFormTitle.textContent = 'Add Member';
                        console.log('✅ Form title set');
                    }

                    // Hide members section
                    if (membersSection) {
                        membersSection.classList.remove('active');
                        console.log('✅ Members section hidden');
                    }

                    // Show member form section
                    if (memberFormSection) {
                        memberFormSection.classList.add('active');
                        console.log('✅ Member form section shown');
                    }

                    // Update page title
                    if (currentSectionTitle) {
                        currentSectionTitle.textContent = 'Add Member';
                        console.log('✅ Page title updated');
                    }

                    console.log('🎉 ADD MEMBER BUTTON SETUP COMPLETE!');
                });

                console.log('✅ Event listener added to Add Member button');

                // Also set up the back button
                const backToMembersBtn = document.getElementById('back-to-members-btn');
                if (backToMembersBtn) {
                    backToMembersBtn.addEventListener('click', function (e) {
                        e.preventDefault();
                        console.log('Back to Members clicked');

                        const membersSection = document.getElementById('members-section');
                        const memberFormSection = document.getElementById('member-form-section');
                        const currentSectionTitle = document.getElementById('current-section-title');

                        if (memberFormSection) memberFormSection.classList.remove('active');
                        if (membersSection) membersSection.classList.add('active');
                        if (currentSectionTitle) currentSectionTitle.textContent = 'Members';
                    });
                    console.log('✅ Back to Members button set up');
                }

            } else {
                console.error('❌ ADD MEMBER BUTTON NOT FOUND!');
                console.log('Available buttons:');
                document.querySelectorAll('button').forEach((btn, index) => {
                    console.log(`Button ${index}:`, btn.id, btn.textContent.trim());
                });
            }
        }

        // Initialize
        function initialize() {
            console.log('=== INITIALIZING DASHBOARD ===');

            // FIRST: Initialize sidebar state
            initializeSidebar();

            // Set up Add Member button immediately
            setupAddMemberButton();

            checkAuth();
            setupNavigation();

            // Set dashboard as active section on page load
            const dashboardSectionElement = document.getElementById('dashboard-section');
            const currentSectionTitleElement = document.getElementById('current-section-title');
            const dashboardNavLink = document.querySelector('.nav-link[data-section="dashboard"]');

            if (dashboardSectionElement) {
                dashboardSectionElement.classList.add('active');
            }
            if (currentSectionTitleElement) {
                currentSectionTitleElement.textContent = 'Dashboard';
            }
            if (dashboardNavLink) {
                dashboardNavLink.classList.add('active');
            }

            // Load dashboard data
            loadDashboardCounts();
            loadRecentActivity();
            loadUpcomingEvents();
            loadLatestPrayerRequests();

            // Load data for other sections
            loadEvents();
            loadLeadership();
            loadMembers();

            // Add event listeners for buttons
            if (addEventBtn) {
                addEventBtn.addEventListener('click', function () {
                    eventForm.reset();
                    document.getElementById('event-id').value = '';
                    eventFormTitle.textContent = 'Add Event';
                    eventsSection.classList.remove('active');
                    eventFormSection.classList.add('active');
                    currentSectionTitle.textContent = 'Add Event';
                });
            }

            if (backToEventsBtn) {
                backToEventsBtn.addEventListener('click', function () {
                    eventFormSection.classList.remove('active');
                    eventsSection.classList.add('active');
                    currentSectionTitle.textContent = 'Events';
                });
            }

            // Add event form submission handler
            if (eventForm) {
                eventForm.addEventListener('submit', function (e) {
                    e.preventDefault();
                    saveEvent();
                });
            }

            // Add leadership team button event listeners
            if (addLeaderBtn) {
                addLeaderBtn.addEventListener('click', function () {
                    leadershipForm.reset();
                    document.getElementById('leader-id').value = '';
                    leadershipFormTitle.textContent = 'Add Team Member';
                    leadershipSection.classList.remove('active');
                    leadershipFormSection.classList.add('active');
                    currentSectionTitle.textContent = 'Add Team Member';
                });
            }

            if (backToLeadershipBtn) {
                backToLeadershipBtn.addEventListener('click', function () {
                    leadershipFormSection.classList.remove('active');
                    leadershipSection.classList.add('active');
                    currentSectionTitle.textContent = 'Leadership Team';
                });
            }

            // Add leadership form submission handler
            if (leadershipForm) {
                leadershipForm.addEventListener('submit', function (e) {
                    e.preventDefault();
                    saveLeader();
                });
            }

            // Add Member button is now set up in setupAddMemberButton() function
            console.log('✅ Add Member button setup moved to dedicated function');

            // Add member form submission handler
            if (memberForm) {
                memberForm.addEventListener('submit', function (e) {
                    e.preventDefault();
                    saveMember();
                });
            }

            // Add blog button event listeners
            if (addBlogBtn) {
                console.log('Add Blog button found, adding event listener');
                addBlogBtn.addEventListener('click', function () {
                    console.log('Add Blog button clicked');
                    if (blogForm) blogForm.reset();
                    const blogIdField = document.getElementById('blog-id');
                    if (blogIdField) blogIdField.value = '';
                    if (blogFormTitle) blogFormTitle.textContent = 'Add Blog Post';
                    if (blogSection) blogSection.classList.remove('active');
                    if (blogFormSection) blogFormSection.classList.add('active');
                    if (currentSectionTitle) currentSectionTitle.textContent = 'Add Blog Post';
                });
            } else {
                console.log('Add Blog button NOT found');
            }

            if (backToBlogBtn) {
                backToBlogBtn.addEventListener('click', function () {
                    blogFormSection.classList.remove('active');
                    blogSection.classList.add('active');
                    currentSectionTitle.textContent = 'Blog';
                });
            }

            // Add gallery button event listeners
            if (addGalleryBtn) {
                console.log('Add Gallery button found, adding event listener');
                addGalleryBtn.addEventListener('click', function () {
                    console.log('Add Gallery button clicked');
                    if (galleryForm) galleryForm.reset();
                    const galleryIdField = document.getElementById('gallery-id');
                    if (galleryIdField) galleryIdField.value = '';
                    if (galleryFormTitle) galleryFormTitle.textContent = 'Add Gallery Image';
                    if (gallerySection) gallerySection.classList.remove('active');
                    if (galleryFormSection) galleryFormSection.classList.add('active');
                    if (currentSectionTitle) currentSectionTitle.textContent = 'Add Gallery Image';
                });
            } else {
                console.log('Add Gallery button NOT found');
            }

            if (backToGalleryBtn) {
                backToGalleryBtn.addEventListener('click', function () {
                    galleryFormSection.classList.remove('active');
                    gallerySection.classList.add('active');
                    currentSectionTitle.textContent = 'Gallery';
                });
            }

            // Setup image upload functionality
            setupImageUpload();

            // Add blog form submission handler
            if (blogForm) {
                blogForm.addEventListener('submit', function (e) {
                    e.preventDefault();
                    saveBlogPost();
                });
            }

            // Add gallery form submission handler
            if (galleryForm) {
                galleryForm.addEventListener('submit', function (e) {
                    e.preventDefault();
                    saveGalleryItem();
                });
            }
        }

        // Setup image upload drag and drop functionality
        function setupImageUpload() {
            const dropZone = document.getElementById('gallery-drop-zone');
            const fileInput = document.getElementById('gallery-image-file');
            const browseBtn = document.getElementById('gallery-browse-btn');
            const imagePreview = document.getElementById('gallery-image-preview');
            const previewImg = document.getElementById('gallery-preview-img');
            const removeBtn = document.getElementById('gallery-remove-image');
            const imageUrlInput = document.getElementById('gallery-image-url');

            if (!dropZone || !fileInput || !browseBtn) return;

            // Browse button click
            browseBtn.addEventListener('click', function () {
                fileInput.click();
            });

            // Drop zone click
            dropZone.addEventListener('click', function () {
                fileInput.click();
            });

            // File input change
            fileInput.addEventListener('change', function (e) {
                const file = e.target.files[0];
                if (file) {
                    handleImageFile(file);
                }
            });

            // Drag and drop events
            dropZone.addEventListener('dragover', function (e) {
                e.preventDefault();
                dropZone.classList.add('dragover');
            });

            dropZone.addEventListener('dragleave', function (e) {
                e.preventDefault();
                dropZone.classList.remove('dragover');
            });

            dropZone.addEventListener('drop', function (e) {
                e.preventDefault();
                dropZone.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    const file = files[0];
                    if (file.type.startsWith('image/')) {
                        handleImageFile(file);
                    } else {
                        alert('Please select an image file.');
                    }
                }
            });

            // Remove image button
            if (removeBtn) {
                removeBtn.addEventListener('click', function () {
                    clearImagePreview();
                });
            }

            // Image URL input change
            if (imageUrlInput) {
                imageUrlInput.addEventListener('input', function () {
                    if (this.value.trim()) {
                        clearImagePreview();
                    }
                });
            }

            function handleImageFile(file) {
                // Validate file size (max 5MB)
                if (file.size > 5 * 1024 * 1024) {
                    alert('File size must be less than 5MB.');
                    return;
                }

                // Create file reader
                const reader = new FileReader();
                reader.onload = function (e) {
                    previewImg.src = e.target.result;
                    imagePreview.style.display = 'block';
                    dropZone.style.display = 'none';

                    // Clear URL input if file is selected
                    if (imageUrlInput) {
                        imageUrlInput.value = '';
                    }
                };
                reader.readAsDataURL(file);
            }

            function clearImagePreview() {
                imagePreview.style.display = 'none';
                dropZone.style.display = 'flex';
                previewImg.src = '';
                fileInput.value = '';
            }
        }

        // Save blog post (create or update)
        async function saveBlogPost() {
            try {
                const blogId = document.getElementById('blog-id').value;
                const title = document.getElementById('blog-title').value;
                const content = document.getElementById('blog-content').value;
                const author = document.getElementById('blog-author').value;
                const date = document.getElementById('blog-date').value;
                const category = document.getElementById('blog-category').value;
                const imageUrl = document.getElementById('blog-image-url').value;
                const featured = document.getElementById('blog-featured').checked;

                // Validate required fields
                if (!title || !content || !author || !date || !category || !imageUrl) {
                    showAlert('blog-form-alert', 'Please fill in all required fields.', 'danger');
                    return;
                }

                // Create blog post object
                const blogData = {
                    title,
                    content,
                    author,
                    date,
                    category,
                    imageUrl,
                    featured,
                    status: 'published'
                };

                let response;

                // Update or create blog post
                if (blogId) {
                    // Update existing blog post
                    response = await fetch(`${API_URL}/blog/${blogId}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(blogData)
                    });
                } else {
                    // Create new blog post
                    response = await fetch(`${API_URL}/blog`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(blogData)
                    });
                }

                if (!response.ok) {
                    throw new Error(`Failed to ${blogId ? 'update' : 'add'} blog post`);
                }

                // Show success message
                showAlert('blog-form-alert', `Blog post ${blogId ? 'updated' : 'added'} successfully!`, 'success');

                // Go back to blog list after a short delay
                setTimeout(() => {
                    blogFormSection.classList.remove('active');
                    blogSection.classList.add('active');
                    currentSectionTitle.textContent = 'Blog';
                }, 1500);

            } catch (err) {
                console.error('Error saving blog post:', err);
                showAlert('blog-form-alert', `Error ${blogId ? 'updating' : 'adding'} blog post. Please try again.`, 'danger');
            }
        }

        // Save gallery item (create or update)
        async function saveGalleryItem() {
            try {
                const galleryId = document.getElementById('gallery-id').value;
                const title = document.getElementById('gallery-title').value;
                const description = document.getElementById('gallery-description').value;
                const category = document.getElementById('gallery-category').value;
                const date = document.getElementById('gallery-date').value;
                const imageUrl = document.getElementById('gallery-image-url').value;
                const featured = document.getElementById('gallery-featured').checked;
                const fileInput = document.getElementById('gallery-image-file');

                // Validate required fields
                if (!title || !description || !category || !date) {
                    showAlert('gallery-form-alert', 'Please fill in all required fields.', 'danger');
                    return;
                }

                // Check if we have an image (either file or URL)
                if (!fileInput.files[0] && !imageUrl) {
                    showAlert('gallery-form-alert', 'Please provide an image (upload file or enter URL).', 'danger');
                    return;
                }

                let finalImageUrl = imageUrl;

                // If a file is selected, we would normally upload it to a server
                // For this demo, we'll use a placeholder or the URL input
                if (fileInput.files[0] && !imageUrl) {
                    // In a real application, you would upload the file to your server
                    // For now, we'll use a placeholder
                    finalImageUrl = 'https://via.placeholder.com/400x300?text=Uploaded+Image';
                    showAlert('gallery-form-alert', 'Note: File upload simulation - using placeholder image.', 'warning');
                }

                // Create gallery item object
                const galleryData = {
                    title,
                    description,
                    category,
                    date,
                    imageUrl: finalImageUrl,
                    featured
                };

                let response;

                // Update or create gallery item
                if (galleryId) {
                    // Update existing gallery item
                    response = await fetch(`${API_URL}/gallery/${galleryId}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(galleryData)
                    });
                } else {
                    // Create new gallery item
                    response = await fetch(`${API_URL}/gallery`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(galleryData)
                    });
                }

                if (!response.ok) {
                    throw new Error(`Failed to ${galleryId ? 'update' : 'add'} gallery item`);
                }

                // Show success message
                showAlert('gallery-form-alert', `Gallery item ${galleryId ? 'updated' : 'added'} successfully!`, 'success');

                // Go back to gallery list after a short delay
                setTimeout(() => {
                    galleryFormSection.classList.remove('active');
                    gallerySection.classList.add('active');
                    currentSectionTitle.textContent = 'Gallery';
                }, 1500);

            } catch (err) {
                console.error('Error saving gallery item:', err);
                showAlert('gallery-form-alert', `Error ${galleryId ? 'updating' : 'adding'} gallery item. Please try again.`, 'danger');
            }
        }

        // Save member (create or update)
        async function saveMember() {
            try {
                console.log('🚀 === SAVE MEMBER FUNCTION CALLED ===');

                // Show loading message
                console.log('🔍 Checking for member-form-alert element...');
                const memberFormAlert = document.getElementById('member-form-alert');
                if (memberFormAlert) {
                    console.log('✅ member-form-alert element found:', memberFormAlert);
                    showAlert('member-form-alert', 'Saving member...', 'info');
                    console.log('✅ Loading message shown');
                } else {
                    console.log('❌ member-form-alert element not found');
                }

                // Get form values
                const memberId = document.getElementById('member-id').value;
                const name = document.getElementById('member-name').value;
                const phone = document.getElementById('member-phone').value;
                const address = document.getElementById('member-address').value;
                const birthdate = document.getElementById('member-birthdate').value;
                const memberSince = document.getElementById('member-since').value;
                const ministry = document.getElementById('member-ministry').value;
                const status = document.getElementById('member-status').value;
                const notes = document.getElementById('member-notes') ? document.getElementById('member-notes').value : '';

                console.log('📝 Member form values:', {
                    memberId,
                    name,
                    phone,
                    address,
                    birthdate,
                    memberSince,
                    ministry,
                    status,
                    notes
                });

                // Validate required fields
                if (!name || !phone || !ministry || !status) {
                    console.log('❌ Validation failed - missing required fields');
                    if (memberFormAlert) {
                        showAlert('member-form-alert', 'Please fill in all required fields (Name, Phone, Ministry, Status)', 'danger');
                    }
                    return;
                }
                console.log('✅ Validation passed');

                // Create member object
                const memberData = {
                    name,
                    phone,
                    address,
                    birthdate,
                    memberSince,
                    ministry,
                    status,
                    notes
                };

                let response;

                // Update or create member
                if (memberId) {
                    // Update existing member
                    const token = localStorage.getItem('token');
                    response = await fetch(`${API_URL}/members/${memberId}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${token}`
                        },
                        body: JSON.stringify(memberData)
                    });
                } else {
                    // Create new member
                    console.log('🚀 Creating new member with data:', memberData);
                    const token = localStorage.getItem('token');
                    response = await fetch(`${API_URL}/members`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${token}`
                        },
                        body: JSON.stringify(memberData)
                    });
                    console.log('📡 API response status:', response.status);
                }

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('❌ API error response:', errorText);
                    throw new Error(`Failed to ${memberId ? 'update' : 'add'} member: ${response.status} ${response.statusText}`);
                }

                const savedMember = await response.json();
                console.log('✅ Member saved successfully:', savedMember);

                // Show success message
                if (memberFormAlert) {
                    showAlert('member-form-alert', `Member ${memberId ? 'updated' : 'added'} successfully!`, 'success');
                }

                // Show toast notification
                showToast(`Member ${memberId ? 'updated' : 'added'} successfully!`, 'success');

                console.log('🔄 Reloading members table after successful save...');
                // Reload members immediately
                await loadMembers();

                console.log('✅ Members table reloaded, updating dashboard counts...');
                // Also reload dashboard counts
                loadDashboardCounts();

                // Reset the form
                memberForm.reset();
                document.getElementById('member-id').value = '';

                // Go back to members list after a short delay
                setTimeout(() => {
                    memberFormSection.classList.remove('active');
                    membersSection.classList.add('active');
                    currentSectionTitle.textContent = 'Members';

                    // Force reload members again when returning to the list
                    console.log('🔄 Force reloading members when returning to list...');
                    loadMembers();
                }, 1500);

            } catch (err) {
                console.error('❌ Error saving member:', err);
                console.error('❌ Error stack:', err.stack);
                showAlert('member-form-alert', `Error adding member. Please try again.`, 'danger');
            }
        }

        // Add event listener for save prayer request button
        document.getElementById('save-prayer-request-btn').addEventListener('click', function () {
            const requestId = parseInt(this.getAttribute('data-id'));
            savePrayerRequest(requestId);
        });

        // DOM Content Loaded - AGGRESSIVE INITIALIZATION
        document.addEventListener('DOMContentLoaded', function () {
            console.log('🚀 DOM Content Loaded - Starting aggressive initialization');
            initialize();

            // AGGRESSIVE FIX: Retry setting up Add Member button after delays
            setTimeout(() => {
                console.log('=== RETRY 1: ADD MEMBER BUTTON SETUP ===');
                setupAddMemberButton();
            }, 500);

            setTimeout(() => {
                console.log('=== RETRY 2: ADD MEMBER BUTTON SETUP ===');
                setupAddMemberButton();
            }, 1000);

            setTimeout(() => {
                console.log('=== FINAL RETRY: ADD MEMBER BUTTON SETUP ===');
                setupAddMemberButton();
            }, 2000);
        });

        // Window load event as backup
        window.addEventListener('load', function () {
            console.log('🚀 Window Load - Backup initialization');
            setTimeout(() => {
                console.log('=== WINDOW LOAD RETRY: ADD MEMBER BUTTON SETUP ===');
                setupAddMemberButton();
            }, 100);
        });

        // Setup mobile toggle functionality
        function setupMobileToggle() {
            const mobileToggle = document.getElementById('mobile-toggle');
            const sidebar = document.querySelector('.sidebar');
            const mainContent= document.querySelector('.main-content');

            if (mobileToggle && sidebar) {
                // Show mobile toggle on smaller screens
                function checkScreenSize() {
                    if (window.innerWidth < 1200) {
                        mobileToggle.style.display = 'block';
                    } else {
                        mobileToggle.style.display = 'none';
                        sidebar.classList.remove('active');
                    }
                }

                // Initial check
                checkScreenSize();

                // Check on resize
                window.addEventListener('resize', checkScreenSize);

                // Toggle sidebar on mobile
                mobileToggle.addEventListener('click', function () {
                    sidebar.classList.toggle('active');
                });

                // Close sidebar when clicking outside on mobile
                document.addEventListener('click', function (e) {
                    if (
                        window.innerWidth < 1200 &&
                        !sidebar.contains(e.target) &&
                        !mobileToggle.contains(e.target) &&
                        sidebar.classList.contains('active')
                    ) {
                        // Remove sidebar "active" class
                        sidebar.classList.remove('active');

                        // Also remove "sidebar-collapsed" from main content
                        mainContent.classList.remove('sidebar-collapsed');
                    }
                });

            }
        }

        // Initialize mobile toggle
        setupMobileToggle();
    </script>
</body>

</html>