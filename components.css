/* Components Styles for North Texas SDA Church Website */

/* Mission/Vision/Values Section */
.mission-section {
    padding: 100px 0;
    background-color: var(--bg-color);
}

.mission-container {
    max-width: 1320px;
    margin: 0 auto;
    padding: 0 20px;
}

.mission-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-bottom: 50px;
}

.mission-item {
    background-color: var(--bg-color);
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
    text-align: center;
}

.mission-item h3 {
    font-family: var(--cmsmasters-secondary-font-family, "Barlow"), sans-serif;
    font-size: 24px;
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: 20px;
}

.mission-item p {
    font-family: var(--cmsmasters-text-font-family, "Lora"), sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-color);
}

.mission-welcome {
    text-align: center;
}

.mission-welcome h2 {
    font-family: var(--cmsmasters-primary-font-family, "Bebas Neue"), sans-serif;
    font-size: 64px;
    font-weight: 400;
    letter-spacing: -1px;
    color: var(--secondary-color);
}

/* Ministries Section */
.ministries-section {
    padding: 100px 0;
    background-color: var(--alternate-bg-color);
}

.ministries-heading {
    text-align: center;
    margin-bottom: 50px;
}

.ministries-slider {
    display: flex;
    gap: 40px;
    margin: 0 auto;
    max-width: 1320px;
    padding: 0 20px;
}

.ministry-item {
    flex: 1;
    position: relative;
}

.ministry-image {
    height: 400px;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.ministry-content {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 30px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
    color: var(--bg-color);
    border-radius: 0 0 10px 10px;
}

.ministry-title {
    font-family: var(--cmsmasters-secondary-font-family, "Barlow"), sans-serif;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 10px;
}

.ministry-description {
    font-family: var(--cmsmasters-text-font-family, "Lora"), sans-serif;
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 20px;
}

.ministry-link {
    font-family: var(--cmsmasters-button-font-family, "Figtree"), sans-serif;
    font-size: 16px;
    font-weight: 700;
    color: var(--accent-color);
    text-decoration: none;
}

.ministry-link:hover {
    text-decoration: underline;
}

/* Leadership Team Section */
.leadership-section {
    padding: 100px 0;
    background-color: var(--bg-color);
}

.leadership-heading {
    text-align: center;
    margin-bottom: 50px;
}

.leadership-team {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 30px;
    max-width: 1320px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Events Section */
.events-section {
    padding: 100px 0;
    background-color: var(--alternate-bg-color);
}

.events-heading {
    text-align: center;
    margin-bottom: 50px;
}

.events-container {
    max-width: 1320px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Footer Styles */
.cmsmasters-footer {
    background-color: var(--secondary-color);
    color: var(--bg-color);
    padding: 80px 0 0;
}

.cmsmasters-footer__outer {
    width: 100%;
}

.cmsmasters-footer__inner {
    max-width: 1320px;
    margin: 0 auto;
    padding: 0 20px;
}

.cmsmasters-footer__content {
    display: flex;
    flex-direction: column;
}

.cmsmasters-footer__widgets {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    margin-bottom: 50px;
}

.cmsmasters-footer__widget-title {
    font-family: var(--cmsmasters-secondary-font-family, "Barlow"), sans-serif;
    font-size: 20px;
    font-weight: 600;
    color: var(--bg-color);
    margin-bottom: 20px;
}

.cmsmasters-footer__widget-content {
    font-family: var(--cmsmasters-text-font-family, "Lora"), sans-serif;
    font-size: 16px;
    line-height: 1.6;
}

.cmsmasters-footer__widget-content p {
    margin-bottom: 10px;
}

.cmsmasters-footer__widget-content p:last-child {
    margin-bottom: 0;
}

.cmsmasters-footer__widget-content i {
    margin-right: 10px;
    color: var(--accent-color);
}

.cmsmasters-footer__social {
    display: flex;
    gap: 15px;
}

.cmsmasters-footer__social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: var(--bg-color);
    transition: all 0.3s ease;
}

.cmsmasters-footer__social-link:hover {
    background-color: var(--accent-color);
    color: var(--bg-color);
}

.cmsmasters-footer__copyright {
    text-align: center;
    padding: 30px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    font-family: var(--cmsmasters-text-font-family, "Lora"), sans-serif;
    font-size: 14px;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background-color: var(--accent-color);
    color: var(--bg-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 999;
}

.back-to-top.active {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background-color: var(--secondary-color);
    color: var(--bg-color);
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .hero-heading .elementor-heading-title {
        font-size: 120px;
    }
    
    .cmsmasters-widget-title__heading {
        font-size: 60px;
    }
    
    .mission-welcome h2 {
        font-size: 50px;
    }
}

@media (max-width: 992px) {
    .cmsmasters-header__navigation {
        display: none;
    }
    
    .cmsmasters-header__toggle {
        display: block;
    }
    
    .hero-heading .elementor-heading-title {
        font-size: 100px;
    }
    
    .welcome-content {
        flex-direction: column;
    }
    
    .welcome-decoration {
        display: none;
    }
    
    .welcome-text {
        flex: 1;
        padding: 0;
    }
    
    .mission-grid {
        grid-template-columns: 1fr;
    }
    
    .cmsmasters-footer__widgets {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .cmsmasters-header__actions {
        display: none;
    }
    
    .hero-heading .elementor-heading-title {
        font-size: 80px;
    }
    
    .hero-buttons {
        flex-direction: column;
        gap: 10px;
    }
    
    .service-times {
        display: none;
    }
    
    .cmsmasters-widget-title__heading {
        font-size: 50px;
    }
    
    .ministries-slider {
        flex-direction: column;
    }
    
    .cmsmasters-footer__widgets {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 576px) {
    .hero-heading .elementor-heading-title {
        font-size: 60px;
    }
    
    .cmsmasters-widget-title__heading {
        font-size: 40px;
    }
    
    .mission-welcome h2 {
        font-size: 36px;
    }
}
