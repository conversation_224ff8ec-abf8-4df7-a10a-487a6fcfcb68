// Sermons sync script for real-time data from dashboard
const API_URL = 'http://localhost:3000/api';

// Function to format sermon date
function formatSermonDate(dateString) {
    const date = new Date(dateString);
    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    };
    return date.toLocaleDateString('en-US', options);
}

// Function to extract YouTube video ID from URL
function getYouTubeVideoId(url) {
    if (!url) return null;

    // Handle different YouTube URL formats
    const patterns = [
        /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/|youtube\.com\/v\/|youtube\.com\/watch\?.*&v=)([^#&?]*)/,
        /youtube\.com\/watch\?.*v=([^#&?]*)/,
        /youtu\.be\/([^#&?]*)/,
        /youtube\.com\/embed\/([^#&?]*)/,
        /youtube\.com\/v\/([^#&?]*)/
    ];

    for (const pattern of patterns) {
        const match = url.match(pattern);
        if (match && match[1] && match[1].length === 11) {
            return match[1];
        }
    }

    // Try to extract from query parameters
    try {
        const urlObj = new URL(url);
        const videoId = urlObj.searchParams.get('v');
        if (videoId && videoId.length === 11) {
            return videoId;
        }
    } catch (e) {
        console.log('URL parsing failed:', e);
    }

    return null;
}

// Function to get YouTube thumbnail URL
function getYouTubeThumbnail(videoUrl) {
    const videoId = getYouTubeVideoId(videoUrl);
    if (videoId) {
        return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
    }
    return 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=600&q=80';
}

// Function to create sermon card HTML
function createSermonCard(sermon) {
    const formattedDate = formatSermonDate(sermon.date);
    const thumbnailUrl = sermon.thumbnailUrl || getYouTubeThumbnail(sermon.videoUrl);

    return `
        <div class="sermon-card" data-scroll data-sermon-id="${sermon.id}">
            <div class="sermon-thumbnail">
                <img src="${thumbnailUrl}" alt="${sermon.title}">
                <div class="sermon-play-button">
                    <i class="fas fa-play"></i>
                </div>
            </div>
            <div class="sermon-details">
                <div class="sermon-date">${formattedDate}</div>
                <h3 class="sermon-title">${sermon.title}</h3>
                <p class="sermon-description">${sermon.description}</p>
                <div class="sermon-categories">
                    <span class="sermon-category">${sermon.preacher || 'Pastor'}</span>
                </div>
            </div>
        </div>
    `;
}

// Function to fetch and display sermons
async function fetchAndDisplaySermons() {
    const sermonsContainer = document.querySelector('.sermons-container');
    if (!sermonsContainer) {
        console.error('❌ Sermons container not found');
        return;
    }

    try {
        console.log('🎬 Fetching sermons from API...');

        // Try multiple API URLs in case of different server configurations
        const apiUrls = [
            'https://sda-church-production.up.railway.app/api/sermons',
            'http://localhost:3000/api/sermons',
            'http://localhost:3001/api/sermons',
            './api/sermons' // Relative path fallback
        ];

        let sermons = null;
        let lastError = null;

        for (const url of apiUrls) {
            try {
                console.log(`🔄 Trying API URL: ${url}`);
                const response = await fetch(url);

                if (response.ok) {
                    sermons = await response.json();
                    console.log('📺 Sermons received:', sermons);
                    break;
                } else {
                    lastError = new Error(`HTTP error! status: ${response.status} from ${url}`);
                }
            } catch (err) {
                lastError = err;
                console.log(`❌ Failed to fetch from ${url}:`, err.message);
                continue;
            }
        }

        if (!sermons) {
            throw lastError || new Error('All API endpoints failed');
        }

        // Clear existing content
        sermonsContainer.innerHTML = '';

        // Add sermons
        if (sermons && sermons.length > 0) {
            sermons.forEach(sermon => {
                sermonsContainer.innerHTML += createSermonCard(sermon);
            });

            // Add click event listeners to sermon cards
            addSermonClickListeners();

            // Reinitialize Locomotive Scroll if it exists
            if (window.scroll && typeof window.scroll.update === 'function') {
                setTimeout(() => {
                    window.scroll.update();
                }, 100);
            }

            console.log(`✅ Successfully displayed ${sermons.length} sermons`);
        } else {
            sermonsContainer.innerHTML = `
                <div class="no-sermons-message" style="text-align: center; padding: 40px;">
                    <h3 style="color: #333; margin-bottom: 15px;">No sermons available</h3>
                    <p style="color: #666;">Check back soon for new sermon content.</p>
                </div>
            `;
        }

    } catch (error) {
        console.error('❌ Error fetching sermons:', error);

        // Try to load fallback/demo sermons
        loadFallbackSermons(sermonsContainer);
    }
}

// Function to load fallback sermons when API is not available
function loadFallbackSermons(container) {
    console.log('🔄 Loading fallback sermons...');

    const fallbackSermons = [
        {
            id: 1,
            title: "Walking in Faith: Trusting God in Uncertain Times",
            description: "Pastor Greg Laurie shares his testimony and encourages us to give all our worries to God, trusting His plans for us are good.",
            preacher: "Pastor Greg Laurie",
            date: "2024-01-15",
            videoUrl: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            thumbnailUrl: "https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=600&q=80"
        },
        {
            id: 2,
            title: "The Power of Prayer",
            description: "Discover how prayer can transform your life and relationships with God and others.",
            preacher: "Pastor John Smith",
            date: "2024-01-08",
            videoUrl: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            thumbnailUrl: "https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=600&q=80"
        },
        {
            id: 3,
            title: "Living with Purpose",
            description: "Find your calling and live a life that matters for God's kingdom.",
            preacher: "Elder David Johnson",
            date: "2024-01-01",
            videoUrl: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            thumbnailUrl: "https://images.unsplash.com/photo-1519125323398-675f0ddb6308?auto=format&fit=crop&w=600&q=80"
        }
    ];

    container.innerHTML = '';

    fallbackSermons.forEach(sermon => {
        container.innerHTML += createSermonCard(sermon);
    });

    addSermonClickListeners();

    // Add a notice about fallback content
    const notice = document.createElement('div');
    notice.style.cssText = 'text-align: center; padding: 20px; background: #f0f8ff; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #007bff;';
    notice.innerHTML = `
        <p style="margin: 0; color: #0056b3; font-weight: 500;">
            <i class="fas fa-info-circle" style="margin-right: 8px;"></i>
            Showing sample sermons. Connect to the dashboard to see live content.
        </p>
    `;
    container.insertBefore(notice, container.firstChild);

    console.log('✅ Fallback sermons loaded');
}

// Function to add click event listeners to sermon cards
function addSermonClickListeners() {
    const sermonCards = document.querySelectorAll('.sermon-card');

    sermonCards.forEach(card => {
        card.addEventListener('click', function() {
            const sermonId = this.getAttribute('data-sermon-id');
            if (sermonId) {
                // Redirect to sermon video page
                window.location.href = `sermon-video.html?id=${sermonId}`;
            }
        });

        // Add hover effect
        card.style.cursor = 'pointer';
    });
}

// Function to start real-time sync
function startSermonSync() {
    // Initial load
    fetchAndDisplaySermons();

    // Set up periodic sync every 30 seconds
    setInterval(fetchAndDisplaySermons, 30000);

    console.log('🔄 Sermon sync started - checking for updates every 30 seconds');
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎬 Sermons sync script loaded');
    console.log('🔍 Current page URL:', window.location.href);
    console.log('🔍 Looking for sermons container...');

    const container = document.querySelector('.sermons-container');
    if (container) {
        console.log('✅ Sermons container found:', container);
    } else {
        console.error('❌ Sermons container not found! Available containers:');
        const allContainers = document.querySelectorAll('[class*="sermon"]');
        allContainers.forEach(el => console.log('  -', el.className, el));
    }

    startSermonSync();
});

// Export functions for external use
window.SermonSync = {
    fetchAndDisplaySermons,
    startSermonSync
};
