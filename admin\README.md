# Faith Connect Church Admin Dashboard

This is the admin dashboard for Faith Connect Church website. It allows authorized staff to manage content on the church website.

## Features

- Secure login for authorized staff
- Dashboard overview with content statistics
- Event management (create, edit, delete events)
- Blog post editor with rich text formatting
- Image gallery management with upload capability
- Website settings management (service times, contact info)

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm (v6 or higher)

### Installation

1. Clone the repository
2. Navigate to the admin directory
3. Install dependencies:

```bash
npm install
```

4. Start the development server:

```bash
npm start
```

The admin dashboard will be available at http://localhost:3000

## Project Structure

```
admin/
├── public/
├── src/
│   ├── assets/
│   ├── components/
│   │   ├── common/
│   │   ├── dashboard/
│   │   ├── events/
│   │   ├── blog/
│   │   ├── gallery/
│   │   └── settings/
│   ├── contexts/
│   ├── hooks/
│   ├── pages/
│   ├── services/
│   ├── utils/
│   ├── App.js
│   ├── index.js
│   └── routes.js
└── package.json
```

## Backend API

The admin dashboard communicates with a Node.js/Express backend API. The API endpoints are:

- Authentication: `/api/auth`
- Events: `/api/events`
- Blog Posts: `/api/posts`
- Gallery: `/api/gallery`
- Settings: `/api/settings`

## Deployment

To build the production version of the admin dashboard:

```bash
npm run build
```

The build files will be generated in the `build` directory.

## License

This project is licensed under the MIT License.
