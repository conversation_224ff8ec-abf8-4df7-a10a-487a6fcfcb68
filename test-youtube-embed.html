<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube Embed Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 10px 0;
        }
        .result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .video-container {
            position: relative;
            width: 100%;
            height: 0;
            padding-bottom: 56.25%;
            margin: 20px 0;
        }
        .video-container iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 8px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .test-urls {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>YouTube Embed URL Converter Test</h1>
        <p>Test different YouTube URL formats to see if they convert properly to embed URLs.</p>
        
        <div class="test-urls">
            <h3>Common YouTube URL Formats:</h3>
            <ul>
                <li><code>https://www.youtube.com/watch?v=0ieb8bOEFSk</code></li>
                <li><code>http://youtube.com/watch?app=desktop&v=0ieb8bOEFSk</code></li>
                <li><code>https://youtu.be/0ieb8bOEFSk</code></li>
                <li><code>https://www.youtube.com/embed/0ieb8bOEFSk</code></li>
                <li><code>https://m.youtube.com/watch?v=0ieb8bOEFSk</code></li>
            </ul>
        </div>
        
        <input type="text" 
               class="test-input" 
               id="youtube-url" 
               placeholder="Enter YouTube URL here..."
               value="http://youtube.com/watch?app=desktop&v=0ieb8bOEFSk">
        
        <button onclick="testUrl()">Test URL</button>
        <button onclick="testAllFormats()">Test All Formats</button>
        <button onclick="clearResults()">Clear Results</button>
        
        <div id="results"></div>
        <div id="video-preview"></div>
    </div>

    <script>
        // Copy the YouTube functions from sermon-video.js
        function getYouTubeVideoId(url) {
            if (!url) return null;
            
            // Handle different YouTube URL formats
            const patterns = [
                /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/|youtube\.com\/v\/|youtube\.com\/watch\?.*&v=)([^#&?]*)/,
                /youtube\.com\/watch\?.*v=([^#&?]*)/,
                /youtu\.be\/([^#&?]*)/,
                /youtube\.com\/embed\/([^#&?]*)/,
                /youtube\.com\/v\/([^#&?]*)/
            ];
            
            for (const pattern of patterns) {
                const match = url.match(pattern);
                if (match && match[1] && match[1].length === 11) {
                    return match[1];
                }
            }
            
            // Try to extract from query parameters
            try {
                const urlObj = new URL(url);
                const videoId = urlObj.searchParams.get('v');
                if (videoId && videoId.length === 11) {
                    return videoId;
                }
            } catch (e) {
                console.log('URL parsing failed:', e);
            }
            
            return null;
        }

        function getYouTubeEmbedUrl(url) {
            const videoId = getYouTubeVideoId(url);
            if (videoId) {
                return `https://www.youtube-nocookie.com/embed/${videoId}?autoplay=0&rel=0&modestbranding=1&fs=1&cc_load_policy=0&iv_load_policy=3&enablejsapi=1`;
            }
            return null;
        }

        function testUrl() {
            const url = document.getElementById('youtube-url').value.trim();
            const resultsDiv = document.getElementById('results');
            
            if (!url) {
                alert('Please enter a YouTube URL');
                return;
            }
            
            const videoId = getYouTubeVideoId(url);
            const embedUrl = getYouTubeEmbedUrl(url);
            
            const resultDiv = document.createElement('div');
            
            if (videoId && embedUrl) {
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>✅ SUCCESS</strong><br>
                    <strong>Original URL:</strong> ${url}<br>
                    <strong>Video ID:</strong> ${videoId}<br>
                    <strong>Embed URL:</strong> ${embedUrl}
                `;
                
                // Show video preview
                showVideoPreview(embedUrl);
                
            } else {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <strong>❌ FAILED</strong><br>
                    <strong>Original URL:</strong> ${url}<br>
                    <strong>Error:</strong> Could not extract video ID
                `;
            }
            
            resultsDiv.appendChild(resultDiv);
        }

        function testAllFormats() {
            const testUrls = [
                'https://www.youtube.com/watch?v=0ieb8bOEFSk',
                'http://youtube.com/watch?app=desktop&v=0ieb8bOEFSk',
                'https://youtu.be/0ieb8bOEFSk',
                'https://www.youtube.com/embed/0ieb8bOEFSk',
                'https://m.youtube.com/watch?v=0ieb8bOEFSk',
                'https://www.youtube.com/watch?v=0ieb8bOEFSk&t=30s',
                'https://www.youtube.com/watch?feature=share&v=0ieb8bOEFSk'
            ];
            
            clearResults();
            
            testUrls.forEach(url => {
                document.getElementById('youtube-url').value = url;
                testUrl();
            });
        }

        function showVideoPreview(embedUrl) {
            const previewDiv = document.getElementById('video-preview');
            previewDiv.innerHTML = `
                <h3>Video Preview:</h3>
                <div class="video-container">
                    <iframe src="${embedUrl}" allowfullscreen></iframe>
                </div>
            `;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('video-preview').innerHTML = '';
        }

        // Auto-test the example URL on page load
        window.addEventListener('load', function() {
            setTimeout(testUrl, 500);
        });
    </script>
</body>
</html>
