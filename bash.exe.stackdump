Stack trace:
Frame         Function      Args
0007FFFFAAA0  00021006116E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF99A0) msys-2.0.dll+0x2116E
0007FFFFAAA0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFAAA0  0002100469F2 (00021028DF99, 0007FFFFA958, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFAAA0  00021006A3FE (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A3FE
0007FFFFAAA0  00021006A525 (0007FFFFAAB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A525
0001004F94B7  00021006B985 (0007FFFFAAB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B985
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC48B40000 ntdll.dll
7FFC47E00000 KERNEL32.DLL
7FFC45D50000 KERNELBASE.dll
7FFC468E0000 USER32.dll
7FFC46280000 win32u.dll
7FFC47ED0000 GDI32.dll
7FFC46400000 gdi32full.dll
7FFC46140000 msvcp_win.dll
7FFC462B0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFC47F00000 advapi32.dll
7FFC47C00000 msvcrt.dll
7FFC47B50000 sechost.dll
7FFC472D0000 RPCRT4.dll
7FFC45320000 CRYPTBASE.DLL
7FFC46840000 bcryptPrimitives.dll
7FFC46FD0000 IMM32.DLL
