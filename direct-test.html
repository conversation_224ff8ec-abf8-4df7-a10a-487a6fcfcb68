<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            text-align: center;
        }
        .container {
            margin-bottom: 30px;
        }
        .events-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        .event-card {
            display: flex;
            width: 100%;
            background-color: #fff;
            border-radius: 5px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        .event-date {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 20px;
            background-color: #f7b731;
            color: #fff;
            min-width: 80px;
        }
        .event-month {
            font-size: 16px;
            font-weight: 600;
        }
        .event-day {
            font-size: 28px;
            font-weight: 700;
        }
        .event-details {
            padding: 20px;
            flex: 1;
        }
        .event-title {
            font-size: 20px;
            margin-bottom: 10px;
        }
        .event-time, .event-location {
            color: #666;
            margin-bottom: 5px;
        }
        #debug-console {
            position: fixed;
            bottom: 10px;
            right: 10px;
            width: 400px;
            height: 300px;
            background: rgba(0,0,0,0.8);
            color: #0f0;
            font-family: monospace;
            font-size: 12px;
            padding: 10px;
            overflow: auto;
            z-index: 9999;
        }
    </style>
</head>
<body>
    <h1>Direct Test</h1>
    
    <div class="container">
        <h2>Events</h2>
        <div class="events-container" id="events-container">
            <!-- Events will be dynamically loaded here -->
        </div>
        <button id="load-events">Load Events</button>
    </div>
    
    <!-- Debug Console -->
    <div id="debug-console">
        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
            <strong>Debug Console</strong>
            <button onclick="document.getElementById('debug-console').style.display='none'" style="background: none; border: none; color: white; cursor: pointer;">×</button>
        </div>
        <div id="debug-log"></div>
    </div>
    
    <script>
        // Function to log to the debug console
        function debugLog(message) {
            console.log(message);
            
            // Also log to the debug console on the page
            const debugLog = document.getElementById('debug-log');
            if (debugLog) {
                const logEntry = document.createElement('div');
                logEntry.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
                debugLog.appendChild(logEntry);
                debugLog.scrollTop = debugLog.scrollHeight;
            }
        }
        
        // Load events when the button is clicked
        document.getElementById('load-events').addEventListener('click', async () => {
            const container = document.getElementById('events-container');
            debugLog('Loading events...');
            
            try {
                // Fetch events from the API
                const response = await fetch('http://localhost:3000/api/events');
                
                if (!response.ok) {
                    throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
                }
                
                const events = await response.json();
                debugLog(`Loaded ${events.length} events`);
                
                // Clear the container
                container.innerHTML = '';
                debugLog('Container cleared');
                
                // Add events to the container
                events.forEach(event => {
                    debugLog(`Creating element for event: ${event.title}`);
                    
                    const eventDate = new Date(event.date);
                    const day = eventDate.getDate();
                    const month = eventDate.toLocaleString('en-US', { month: 'short' }).toUpperCase();
                    
                    // Format the time for display
                    const startTime = event.startTime ? 
                        new Date(`2000-01-01T${event.startTime}`).toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' }) : 
                        '9:00 AM';
                    
                    const endTime = event.endTime ? 
                        new Date(`2000-01-01T${event.endTime}`).toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' }) : 
                        '2:00 PM';
                    
                    debugLog(`Event times: ${startTime} - ${endTime}`);
                    
                    const eventElement = document.createElement('div');
                    eventElement.className = 'event-card';
                    
                    eventElement.innerHTML = `
                        <div class="event-date">
                            <span class="event-month">${month}</span>
                            <span class="event-day">${day}</span>
                        </div>
                        <div class="event-details">
                            <h3 class="event-title">${event.title}</h3>
                            <p class="event-time"><i class="far fa-clock"></i> ${startTime} - ${endTime}</p>
                            <p class="event-location"><i class="fas fa-map-marker-alt"></i> ${event.location}</p>
                        </div>
                    `;
                    
                    container.appendChild(eventElement);
                    debugLog(`Added event: ${event.title}`);
                });
                
                debugLog(`Added ${events.length} events to the container`);
            } catch (error) {
                debugLog(`Error: ${error.message}`);
                container.innerHTML = `<div style="color: red; padding: 20px;">Error loading events: ${error.message}</div>`;
            }
        });
    </script>
</body>
</html>
