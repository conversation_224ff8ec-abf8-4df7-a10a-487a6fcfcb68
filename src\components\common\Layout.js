import React, { useState } from 'react';
import { Outlet, NavLink, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { Dropdown } from 'react-bootstrap';

const Layout = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [userDropdownOpen, setUserDropdownOpen] = useState(false);
  const { currentUser, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const toggleUserDropdown = () => {
    setUserDropdownOpen(!userDropdownOpen);
  };

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  // Get page title based on current route
  const getPageTitle = () => {
    const path = location.pathname;
    if (path === '/') return 'Dashboard';
    if (path.startsWith('/events')) return path.includes('/new') || path.includes('/edit') ? 'Manage Event' : 'Events';
    if (path.startsWith('/blog')) return path.includes('/new') || path.includes('/edit') ? 'Manage Post' : 'Blog';
    if (path.startsWith('/gallery')) return path.includes('/new') || path.includes('/edit') ? 'Manage Gallery Item' : 'Gallery';
    if (path.startsWith('/settings')) return 'Settings';
    return 'Dashboard';
  };

  return (
    <div className="dashboard-container">
      {/* Sidebar */}
      <div className={`sidebar ${sidebarCollapsed ? 'sidebar-collapsed' : ''}`}>
        <div className="sidebar-header">
          <div className="sidebar-logo">
            {!sidebarCollapsed && (
              <>
                <img src="/logo.png" alt="Faith Connect Logo" />
                <h2>Faith Connect</h2>
              </>
            )}
            {sidebarCollapsed && <img src="/logo.png" alt="Faith Connect Logo" />}
          </div>
          <button className="sidebar-toggle" onClick={toggleSidebar}>
            <i className={`fas fa-${sidebarCollapsed ? 'angle-right' : 'angle-left'}`}></i>
          </button>
        </div>

        <ul className="nav-menu">
          <li className="nav-item">
            <NavLink to="/" className="nav-link" end>
              <i className="fas fa-tachometer-alt nav-icon"></i>
              {!sidebarCollapsed && <span className="nav-text">Dashboard</span>}
            </NavLink>
          </li>
          <li className="nav-item">
            <NavLink to="/events" className="nav-link">
              <i className="fas fa-calendar-alt nav-icon"></i>
              {!sidebarCollapsed && <span className="nav-text">Events</span>}
            </NavLink>
          </li>
          <li className="nav-item">
            <NavLink to="/blog" className="nav-link">
              <i className="fas fa-blog nav-icon"></i>
              {!sidebarCollapsed && <span className="nav-text">Blog</span>}
            </NavLink>
          </li>
          <li className="nav-item">
            <NavLink to="/gallery" className="nav-link">
              <i className="fas fa-images nav-icon"></i>
              {!sidebarCollapsed && <span className="nav-text">Gallery</span>}
            </NavLink>
          </li>
          <li className="nav-item">
            <NavLink to="/settings" className="nav-link">
              <i className="fas fa-cog nav-icon"></i>
              {!sidebarCollapsed && <span className="nav-text">Settings</span>}
            </NavLink>
          </li>
        </ul>
      </div>

      {/* Main Content */}
      <div className={`main-content ${sidebarCollapsed ? 'main-content-expanded' : ''}`}>
        {/* Top Bar */}
        <div className="topbar">
          <h1 className="page-title">{getPageTitle()}</h1>
          
          <Dropdown show={userDropdownOpen} onToggle={toggleUserDropdown}>
            <Dropdown.Toggle as="div" className="user-dropdown-toggle">
              <img 
                src={`https://ui-avatars.com/api/?name=${currentUser?.name || 'User'}&background=3b5998&color=fff`} 
                alt="User Avatar" 
                className="user-avatar" 
              />
              {!sidebarCollapsed && <span className="user-name">{currentUser?.name || 'User'}</span>}
              <i className={`fas fa-chevron-${userDropdownOpen ? 'up' : 'down'}`}></i>
            </Dropdown.Toggle>

            <Dropdown.Menu className="user-dropdown-menu">
              <Dropdown.Item href="#profile">
                <i className="fas fa-user mr-2"></i> Profile
              </Dropdown.Item>
              <Dropdown.Item onClick={handleLogout}>
                <i className="fas fa-sign-out-alt mr-2"></i> Logout
              </Dropdown.Item>
            </Dropdown.Menu>
          </Dropdown>
        </div>

        {/* Page Content */}
        <div className="page-content">
          <Outlet />
        </div>
      </div>
    </div>
  );
};

export default Layout;
