const express = require('express');
const fs = require('fs');
const path = require('path');
const bodyParser = require('body-parser');
const cors = require('cors');

// Initialize Express app
const app = express();

// Middleware
app.use(cors({
  origin: '*', // Allow all origins
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(bodyParser.json());
app.use(express.static(__dirname));

// In-memory data store (in a real app, this would be a database)
let events = [
  {
    id: 1,
    title: 'Community Service Day',
    description: 'Join us as we serve our community through various outreach activities.',
    date: '2023-06-15',
    startTime: '09:00',
    endTime: '14:00',
    location: 'Church Grounds',
    featured: true,
    status: 'upcoming'
  },
  {
    id: 2,
    title: 'Youth Worship Night',
    description: 'A special evening of praise and worship led by our youth ministry.',
    date: '2023-06-22',
    startTime: '18:00',
    endTime: '20:30',
    location: 'Fellowship Hall',
    featured: false,
    status: 'upcoming'
  },
  {
    id: 3,
    title: 'Family Potluck',
    description: 'Bring your favorite dish and join us for fellowship after the service.',
    date: '2023-06-29',
    startTime: '13:00',
    endTime: '15:00',
    location: 'Church Fellowship Hall',
    featured: true,
    status: 'upcoming'
  }
];

// Sermons data
let sermons = [
  {
    id: 1,
    title: 'Walking in Faith',
    description: 'Learn how to strengthen your faith through daily practice.',
    preacher: 'Pastor John Smith',
    date: '2023-06-10',
    videoUrl: 'https://www.youtube.com/watch?v=example1',
    thumbnailUrl: 'https://via.placeholder.com/300x200?text=Walking+in+Faith',
    featured: true
  },
  {
    id: 2,
    title: 'The Power of Prayer',
    description: 'Discover how prayer can transform your life and relationships.',
    preacher: 'Pastor Jane Doe',
    date: '2023-06-03',
    videoUrl: 'https://www.youtube.com/watch?v=example2',
    thumbnailUrl: 'https://via.placeholder.com/300x200?text=Power+of+Prayer',
    featured: true
  }
];

// Leadership team data - load from JSON file
let leadershipTeam = [];
try {
  console.log('Loading leadership team data from file...');
  const filePath = path.join(__dirname, 'data/leadership.json');
  console.log('Leadership data file path:', filePath);

  if (fs.existsSync(filePath)) {
    console.log('Leadership data file exists');
    const leadershipData = fs.readFileSync(filePath, 'utf8');
    console.log('Leadership data file content:', leadershipData);

    leadershipTeam = JSON.parse(leadershipData);
    console.log('Parsed leadership team data:', leadershipTeam);
  } else {
    console.error('Leadership data file does not exist');
    // Create the file with default data
    const defaultData = [
      {
        id: 1,
        name: 'Pastor John Smith',
        position: 'Senior Pastor',
        bio: 'Pastor John has been leading our church for over 10 years. He has a passion for teaching the Word and serving the community.',
        imageUrl: 'https://via.placeholder.com/300x300?text=Pastor+John',
        email: '<EMAIL>',
        order: 1
      }
    ];

    // Make sure the data directory exists
    if (!fs.existsSync(path.join(__dirname, 'data'))) {
      console.log('Creating data directory...');
      fs.mkdirSync(path.join(__dirname, 'data'));
    }

    // Write default data to file
    fs.writeFileSync(filePath, JSON.stringify(defaultData, null, 2), 'utf8');
    console.log('Created leadership data file with default data');

    leadershipTeam = defaultData;
  }
} catch (err) {
  console.error('Error loading leadership team data:', err);
  console.error('Error details:', err.message, err.stack);

  // Fallback to default data if file can't be loaded
  leadershipTeam = [
    {
      id: 1,
      name: 'Pastor John Smith',
      position: 'Senior Pastor',
      bio: 'Pastor John has been leading our church for over 10 years. He has a passion for teaching the Word and serving the community.',
      imageUrl: 'https://via.placeholder.com/300x300?text=Pastor+John',
      email: '<EMAIL>',
      order: 1
    }
  ];
}

// Blog posts data
let blogPosts = [
  {
    id: 1,
    title: 'Finding Peace in Troubled Times',
    content: 'In today\'s fast-paced world, finding peace can be challenging. This post explores biblical principles for maintaining peace despite external circumstances.',
    author: 'Pastor John Smith',
    date: '2023-06-12',
    imageUrl: 'https://via.placeholder.com/800x400?text=Finding+Peace',
    category: 'Devotional',
    featured: true,
    status: 'published'
  },
  {
    id: 2,
    title: 'Community Service: Making a Difference',
    content: 'Our recent community service event was a huge success. Learn how our church is making a difference in the local community.',
    author: 'Jane Doe',
    date: '2023-06-05',
    imageUrl: 'https://via.placeholder.com/800x400?text=Community+Service',
    category: 'Church News',
    featured: false,
    status: 'published'
  }
];

// Gallery data
let gallery = [
  {
    id: 1,
    title: 'Sunday Worship',
    description: 'Moments from our Sunday worship service.',
    imageUrl: 'https://via.placeholder.com/800x600?text=Sunday+Worship',
    category: 'Worship',
    date: '2023-06-11',
    featured: true
  },
  {
    id: 2,
    title: 'Youth Retreat',
    description: 'Our youth enjoying their annual retreat.',
    imageUrl: 'https://via.placeholder.com/800x600?text=Youth+Retreat',
    category: 'Youth',
    date: '2023-05-20',
    featured: false
  },
  {
    id: 3,
    title: 'Community Outreach',
    description: 'Serving our community with love.',
    imageUrl: 'https://via.placeholder.com/800x600?text=Community+Outreach',
    category: 'Outreach',
    date: '2023-05-15',
    featured: true
  }
];

// Church members data
let members = [
  {
    id: 1,
    name: 'John Doe',
    email: '<EMAIL>',
    phone: '(*************',
    address: '123 Main St, Anytown, TX 75001',
    birthdate: '1980-05-15',
    memberSince: '2010-03-10',
    ministry: 'Worship Team',
    status: 'active'
  },
  {
    id: 2,
    name: 'Jane Smith',
    email: '<EMAIL>',
    phone: '(*************',
    address: '456 Oak St, Anytown, TX 75001',
    birthdate: '1985-08-22',
    memberSince: '2012-06-15',
    ministry: 'Children\'s Ministry',
    status: 'active'
  },
  {
    id: 3,
    name: 'Michael Johnson',
    email: '<EMAIL>',
    phone: '(*************',
    address: '789 Pine St, Anytown, TX 75001',
    birthdate: '1975-11-30',
    memberSince: '2008-01-20',
    ministry: 'Outreach',
    status: 'active'
  }
];

// Prayer requests data
let prayerRequests = [
  {
    id: 1,
    name: 'Sarah Thompson',
    email: '<EMAIL>',
    phone: '(*************',
    category: 'Healing',
    request: 'Please pray for my mother who is recovering from surgery.',
    confidential: false,
    contactMe: true,
    status: 'pending',
    createdAt: '2023-06-14T10:30:00Z'
  },
  {
    id: 2,
    name: 'David Wilson',
    email: '<EMAIL>',
    phone: '(*************',
    category: 'Finance',
    request: 'I am facing financial difficulties and need prayer for a new job opportunity.',
    confidential: true,
    contactMe: false,
    status: 'praying',
    createdAt: '2023-06-13T15:45:00Z'
  }
];

// API Routes

// ===== EVENTS API =====

// Get all events
app.get('/api/events', (req, res) => {
  res.json(events);
});

// Get a single event
app.get('/api/events/:id', (req, res) => {
  const event = events.find(e => e.id === parseInt(req.params.id));
  if (!event) return res.status(404).json({ message: 'Event not found' });
  res.json(event);
});

// Create a new event
app.post('/api/events', (req, res) => {
  const newEvent = {
    id: events.length > 0 ? Math.max(...events.map(e => e.id)) + 1 : 1,
    ...req.body,
    createdAt: new Date().toISOString()
  };
  events.push(newEvent);

  // Save data to file
  saveAllData();

  // Update the index.html file to reflect the new event
  updateMainWebsite();

  res.status(201).json(newEvent);
});

// Update an event
app.put('/api/events/:id', (req, res) => {
  const eventIndex = events.findIndex(e => e.id === parseInt(req.params.id));
  if (eventIndex === -1) return res.status(404).json({ message: 'Event not found' });

  events[eventIndex] = { ...events[eventIndex], ...req.body };

  // Save data to file
  saveAllData();

  // Update the index.html file to reflect the updated event
  updateMainWebsite();

  res.json(events[eventIndex]);
});

// Delete an event
app.delete('/api/events/:id', (req, res) => {
  const eventIndex = events.findIndex(e => e.id === parseInt(req.params.id));
  if (eventIndex === -1) return res.status(404).json({ message: 'Event not found' });

  events.splice(eventIndex, 1);

  // Save data to file
  saveAllData();

  // Update the index.html file to reflect the deleted event
  updateMainWebsite();

  res.json({ message: 'Event deleted' });
});

// ===== SERMONS API =====

// Get all sermons
app.get('/api/sermons', (req, res) => {
  res.json(sermons);
});

// Get a single sermon
app.get('/api/sermons/:id', (req, res) => {
  const sermon = sermons.find(s => s.id === parseInt(req.params.id));
  if (!sermon) return res.status(404).json({ message: 'Sermon not found' });
  res.json(sermon);
});

// Create a new sermon
app.post('/api/sermons', (req, res) => {
  const newSermon = {
    id: sermons.length > 0 ? Math.max(...sermons.map(s => s.id)) + 1 : 1,
    ...req.body,
    createdAt: new Date().toISOString()
  };
  sermons.push(newSermon);

  // Save data to file
  saveAllData();

  // Update the index.html file
  updateMainWebsite();

  res.status(201).json(newSermon);
});

// Update a sermon
app.put('/api/sermons/:id', (req, res) => {
  const sermonIndex = sermons.findIndex(s => s.id === parseInt(req.params.id));
  if (sermonIndex === -1) return res.status(404).json({ message: 'Sermon not found' });

  sermons[sermonIndex] = { ...sermons[sermonIndex], ...req.body };

  // Save data to file
  saveAllData();

  // Update the index.html file
  updateMainWebsite();

  res.json(sermons[sermonIndex]);
});

// Delete a sermon
app.delete('/api/sermons/:id', (req, res) => {
  const sermonIndex = sermons.findIndex(s => s.id === parseInt(req.params.id));
  if (sermonIndex === -1) return res.status(404).json({ message: 'Sermon not found' });

  sermons.splice(sermonIndex, 1);

  // Save data to file
  saveAllData();

  // Update the index.html file
  updateMainWebsite();

  res.json({ message: 'Sermon deleted' });
});

// ===== LEADERSHIP TEAM API =====

// Get all leadership team members
app.get('/api/leadership', (req, res) => {
  console.log('GET /api/leadership - Returning leadership team:', leadershipTeam);
  res.json(leadershipTeam);
});

// Get a single leadership team member
app.get('/api/leadership/:id', (req, res) => {
  const leader = leadershipTeam.find(l => l.id === parseInt(req.params.id));
  if (!leader) return res.status(404).json({ message: 'Leader not found' });
  res.json(leader);
});

// Create a new leadership team member
app.post('/api/leadership', (req, res) => {
  console.log('POST /api/leadership - Request body:', req.body);

  const newLeader = {
    id: leadershipTeam.length > 0 ? Math.max(...leadershipTeam.map(l => l.id)) + 1 : 1,
    ...req.body,
    createdAt: new Date().toISOString()
  };

  console.log('POST /api/leadership - Created new leader:', newLeader);
  leadershipTeam.push(newLeader);
  console.log('POST /api/leadership - Updated leadership team:', leadershipTeam);

  // Save to JSON file
  saveLeadershipData();

  // Update the index.html file
  updateMainWebsite();

  res.status(201).json(newLeader);
});

// Update a leadership team member
app.put('/api/leadership/:id', (req, res) => {
  const leaderIndex = leadershipTeam.findIndex(l => l.id === parseInt(req.params.id));
  if (leaderIndex === -1) return res.status(404).json({ message: 'Leader not found' });

  leadershipTeam[leaderIndex] = { ...leadershipTeam[leaderIndex], ...req.body };

  // Save to JSON file
  saveLeadershipData();

  // Update the index.html file
  updateMainWebsite();

  res.json(leadershipTeam[leaderIndex]);
});

// Delete a leadership team member
app.delete('/api/leadership/:id', (req, res) => {
  const leaderIndex = leadershipTeam.findIndex(l => l.id === parseInt(req.params.id));
  if (leaderIndex === -1) return res.status(404).json({ message: 'Leader not found' });

  leadershipTeam.splice(leaderIndex, 1);

  // Save to JSON file
  saveLeadershipData();

  // Update the index.html file
  updateMainWebsite();

  res.json({ message: 'Leader deleted' });
});

// ===== BLOG POSTS API =====

// Get all blog posts
app.get('/api/blog', (req, res) => {
  res.json(blogPosts);
});

// Get a single blog post
app.get('/api/blog/:id', (req, res) => {
  const post = blogPosts.find(p => p.id === parseInt(req.params.id));
  if (!post) return res.status(404).json({ message: 'Blog post not found' });
  res.json(post);
});

// Create a new blog post
app.post('/api/blog', (req, res) => {
  const newPost = {
    id: blogPosts.length > 0 ? Math.max(...blogPosts.map(p => p.id)) + 1 : 1,
    ...req.body,
    createdAt: new Date().toISOString()
  };
  blogPosts.push(newPost);

  // Save data to file
  saveAllData();

  // Update the index.html file
  updateMainWebsite();

  res.status(201).json(newPost);
});

// Update a blog post
app.put('/api/blog/:id', (req, res) => {
  const postIndex = blogPosts.findIndex(p => p.id === parseInt(req.params.id));
  if (postIndex === -1) return res.status(404).json({ message: 'Blog post not found' });

  blogPosts[postIndex] = { ...blogPosts[postIndex], ...req.body };

  // Save data to file
  saveAllData();

  // Update the index.html file
  updateMainWebsite();

  res.json(blogPosts[postIndex]);
});

// Delete a blog post
app.delete('/api/blog/:id', (req, res) => {
  const postIndex = blogPosts.findIndex(p => p.id === parseInt(req.params.id));
  if (postIndex === -1) return res.status(404).json({ message: 'Blog post not found' });

  blogPosts.splice(postIndex, 1);

  // Save data to file
  saveAllData();

  // Update the index.html file
  updateMainWebsite();

  res.json({ message: 'Blog post deleted' });
});

// ===== GALLERY API =====

// Get all gallery items
app.get('/api/gallery', (req, res) => {
  res.json(gallery);
});

// Get a single gallery item
app.get('/api/gallery/:id', (req, res) => {
  const item = gallery.find(i => i.id === parseInt(req.params.id));
  if (!item) return res.status(404).json({ message: 'Gallery item not found' });
  res.json(item);
});

// Create a new gallery item
app.post('/api/gallery', (req, res) => {
  const newItem = {
    id: gallery.length > 0 ? Math.max(...gallery.map(i => i.id)) + 1 : 1,
    ...req.body,
    createdAt: new Date().toISOString()
  };
  gallery.push(newItem);

  // Save data to file
  saveAllData();

  // Update the index.html file
  updateMainWebsite();

  res.status(201).json(newItem);
});

// Update a gallery item
app.put('/api/gallery/:id', (req, res) => {
  const itemIndex = gallery.findIndex(i => i.id === parseInt(req.params.id));
  if (itemIndex === -1) return res.status(404).json({ message: 'Gallery item not found' });

  gallery[itemIndex] = { ...gallery[itemIndex], ...req.body };

  // Save data to file
  saveAllData();

  // Update the index.html file
  updateMainWebsite();

  res.json(gallery[itemIndex]);
});

// Delete a gallery item
app.delete('/api/gallery/:id', (req, res) => {
  const itemIndex = gallery.findIndex(i => i.id === parseInt(req.params.id));
  if (itemIndex === -1) return res.status(404).json({ message: 'Gallery item not found' });

  gallery.splice(itemIndex, 1);

  // Save data to file
  saveAllData();

  // Update the index.html file
  updateMainWebsite();

  res.json({ message: 'Gallery item deleted' });
});

// ===== CHURCH MEMBERS API =====

// Get all church members
app.get('/api/members', (req, res) => {
  res.json(members);
});

// Get a single church member
app.get('/api/members/:id', (req, res) => {
  const member = members.find(m => m.id === parseInt(req.params.id));
  if (!member) return res.status(404).json({ message: 'Member not found' });
  res.json(member);
});

// Create a new church member
app.post('/api/members', (req, res) => {
  const newMember = {
    id: members.length > 0 ? Math.max(...members.map(m => m.id)) + 1 : 1,
    ...req.body,
    createdAt: new Date().toISOString()
  };
  members.push(newMember);

  res.status(201).json(newMember);
});

// ===== PRAYER REQUESTS API =====

// Get all prayer requests
app.get('/api/prayer-requests', (req, res) => {
  res.json(prayerRequests);
});

// Get a single prayer request
app.get('/api/prayer-requests/:id', (req, res) => {
  const request = prayerRequests.find(r => r.id === parseInt(req.params.id));
  if (!request) return res.status(404).json({ message: 'Prayer request not found' });
  res.json(request);
});

// Create a new prayer request
app.post('/api/prayer-requests', (req, res) => {
  const newRequest = {
    id: prayerRequests.length > 0 ? Math.max(...prayerRequests.map(r => r.id)) + 1 : 1,
    ...req.body,
    status: 'pending',
    createdAt: new Date().toISOString()
  };
  prayerRequests.push(newRequest);

  // Save data to file
  saveAllData();

  console.log('New prayer request received:', newRequest);

  res.status(201).json(newRequest);
});

// Update a prayer request
app.put('/api/prayer-requests/:id', (req, res) => {
  const requestIndex = prayerRequests.findIndex(r => r.id === parseInt(req.params.id));
  if (requestIndex === -1) return res.status(404).json({ message: 'Prayer request not found' });

  prayerRequests[requestIndex] = { ...prayerRequests[requestIndex], ...req.body };

  // Save data to file
  saveAllData();

  res.json(prayerRequests[requestIndex]);
});

// Delete a prayer request
app.delete('/api/prayer-requests/:id', (req, res) => {
  const requestIndex = prayerRequests.findIndex(r => r.id === parseInt(req.params.id));
  if (requestIndex === -1) return res.status(404).json({ message: 'Prayer request not found' });

  prayerRequests.splice(requestIndex, 1);

  // Save data to file
  saveAllData();

  res.json({ message: 'Prayer request deleted' });
});

// ===== MEMBERS API =====

// Get all members
app.get('/api/members', (req, res) => {
  res.json(members);
});

// Get a single member
app.get('/api/members/:id', (req, res) => {
  const member = members.find(m => m.id === parseInt(req.params.id));
  if (!member) return res.status(404).json({ message: 'Member not found' });
  res.json(member);
});

// Create a new member
app.post('/api/members', (req, res) => {
  const newMember = {
    id: members.length > 0 ? Math.max(...members.map(m => m.id)) + 1 : 1,
    ...req.body,
    createdAt: new Date().toISOString()
  };
  members.push(newMember);

  // Save data to file
  saveAllData();

  res.status(201).json(newMember);
});

// Update a church member
app.put('/api/members/:id', (req, res) => {
  const memberIndex = members.findIndex(m => m.id === parseInt(req.params.id));
  if (memberIndex === -1) return res.status(404).json({ message: 'Member not found' });

  members[memberIndex] = { ...members[memberIndex], ...req.body };

  // Save data to file
  saveAllData();

  res.json(members[memberIndex]);
});

// Delete a church member
app.delete('/api/members/:id', (req, res) => {
  const memberIndex = members.findIndex(m => m.id === parseInt(req.params.id));
  if (memberIndex === -1) return res.status(404).json({ message: 'Member not found' });

  members.splice(memberIndex, 1);

  // Save data to file
  saveAllData();

  res.json({ message: 'Member deleted' });
});

// Function to update the main website
function updateMainWebsite() {
  try {
    // Update template.html file
    updateTemplateFile();

    // Read the index.html file
    const indexPath = path.join(__dirname, 'index.html');
    let indexContent = fs.readFileSync(indexPath, 'utf8');

    // Update Events Section
    indexContent = updateEventsSection(indexContent);

    // Update Sermons Section
    indexContent = updateSermonsSection(indexContent);

    // Update Leadership Team Section
    indexContent = updateLeadershipSection(indexContent);

    // Update Blog Posts Section
    indexContent = updateBlogSection(indexContent);

    // Update Gallery Section
    indexContent = updateGallerySection(indexContent);

    // Write the updated content back to the file
    fs.writeFileSync(indexPath, indexContent, 'utf8');

    console.log('Main website updated successfully');
  } catch (err) {
    console.error('Error updating main website:', err);
  }
}

// Function to update the template.html file
function updateTemplateFile() {
  try {
    console.log('Updating template.html file...');

    // Read the template.html file
    const templatePath = path.join(__dirname, 'template.html');
    let templateContent = fs.readFileSync(templatePath, 'utf8');

    // Update leadership team section
    templateContent = updateTemplateLeadershipSection(templateContent);

    // Update events section
    templateContent = updateTemplateEventsSection(templateContent);

    // Write the updated content back to the file
    fs.writeFileSync(templatePath, templateContent, 'utf8');

    console.log('Template.html file updated successfully');
  } catch (err) {
    console.error('Error updating template.html file:', err);
  }
}

// Function to update the leadership team section in template.html
function updateTemplateLeadershipSection(templateContent) {
  try {
    // Find the leadership container
    const leadershipContainerId = 'leadership-container';
    const leadershipContainerRegex = new RegExp(`<div[^>]*id=["']${leadershipContainerId}["'][^>]*>(.*?)<\/div>`, 's');
    const leadershipContainerMatch = templateContent.match(leadershipContainerRegex);

    if (!leadershipContainerMatch) {
      console.warn(`Leadership container with ID '${leadershipContainerId}' not found in template.html`);
      return templateContent;
    }

    // Sort leadership team by order
    const sortedTeam = [...leadershipTeam].sort((a, b) => (a.order || 999) - (b.order || 999));

    // Create HTML for leadership team
    let leadershipHtml = '';

    sortedTeam.forEach(leader => {
      leadershipHtml += `
        <div class="cmsmasters-slider-item">
          <div class="cmsmasters-slider-item-inner">
            <div class="cmsmasters-slider-item-image">
              <img src="${leader.imageUrl || 'https://via.placeholder.com/280x280'}" alt="${leader.name}" class="cmsmasters-slider-item-img">
            </div>
            <div class="cmsmasters-slider-item-content">
              <h3 class="cmsmasters-slider-item-title">${leader.name}</h3>
              <div class="cmsmasters-slider-item-position">${leader.position}</div>
              <div class="cmsmasters-slider-item-description">
                <p>${leader.bio || ''}</p>
              </div>
            </div>
          </div>
        </div>
      `;
    });

    // Wrap the leadership team HTML
    const leadershipContainerHtml = `<div id="${leadershipContainerId}">
      <div class="cmsmasters-slider-wrapper">
        <div class="cmsmasters-slider-container">
          ${leadershipHtml}
        </div>
      </div>
    </div>`;

    // Replace the leadership container in the template
    const updatedContent = templateContent.replace(leadershipContainerRegex, leadershipContainerHtml);

    console.log('Leadership team section in template.html updated successfully');

    return updatedContent;
  } catch (err) {
    console.error('Error updating leadership team section in template.html:', err);
    return templateContent;
  }
}

// Function to update the events section in template.html
function updateTemplateEventsSection(templateContent) {
  try {
    // Find the events container
    const eventsContainerId = 'events-container';
    const eventsContainerRegex = new RegExp(`<div[^>]*id=["']${eventsContainerId}["'][^>]*>(.*?)<\/div>`, 's');
    const eventsContainerMatch = templateContent.match(eventsContainerRegex);

    if (!eventsContainerMatch) {
      console.warn(`Events container with ID '${eventsContainerId}' not found in template.html`);
      return templateContent;
    }

    // Create HTML for events
    let eventsHtml = '';

    // Sort events by date
    const sortedEvents = [...events].sort((a, b) => new Date(a.date) - new Date(b.date));

    sortedEvents.forEach(event => {
      const eventDate = new Date(event.date);
      const day = eventDate.getDate();
      const month = eventDate.toLocaleString('default', { month: 'short' });

      eventsHtml += `
        <div class="event-card">
          <div class="event-date">
            <span class="day">${day}</span>
            <span class="month">${month}</span>
          </div>
          <div class="event-details">
            <h3>${event.title}</h3>
            <p class="event-time"><i class="far fa-clock"></i> ${event.startTime} - ${event.endTime}</p>
            <p class="event-location"><i class="fas fa-map-marker-alt"></i> ${event.location}</p>
            <p class="event-description">${event.description}</p>
          </div>
        </div>
      `;
    });

    // Wrap the events HTML
    const eventsContainerHtml = `<div id="${eventsContainerId}">
      ${eventsHtml}
    </div>`;

    // Replace the events container in the template
    const updatedContent = templateContent.replace(eventsContainerRegex, eventsContainerHtml);

    console.log('Events section in template.html updated successfully');

    return updatedContent;
  } catch (err) {
    console.error('Error updating events section in template.html:', err);
    return templateContent;
  }
}

// Function to update the events section
function updateEventsSection(indexContent) {
  // Find the events section
  const eventsStartTag = '<!-- Upcoming Events Section -->';
  const eventsEndTag = '<!-- Latest Sermons Section -->';

  const startIndex = indexContent.indexOf(eventsStartTag);
  const endIndex = indexContent.indexOf(eventsEndTag);

  if (startIndex !== -1 && endIndex !== -1) {
    // Generate new events HTML
    let eventsHtml = eventsStartTag + `
    <section class="section events" style="background-color: var(--light-color);">
        <div class="container">
            <div class="section-title" data-aos="fade-up">
                <h2>Upcoming Events</h2>
                <p>Join us for these special occasions</p>
            </div>

            <div class="events-container" data-aos="fade-up" data-aos-delay="100">`;

    // Add each event
    events.forEach(event => {
      const eventDate = new Date(event.date);
      const day = eventDate.getDate();
      const month = eventDate.toLocaleString('default', { month: 'short' });

      eventsHtml += `
                <div class="event-card">
                    <div class="event-date">
                        <span class="day">${day}</span>
                        <span class="month">${month}</span>
                    </div>
                    <div class="event-details">
                        <h3>${event.title}</h3>
                        <p class="event-time"><i class="far fa-clock"></i> ${event.startTime} - ${event.endTime}</p>
                        <p class="event-location"><i class="fas fa-map-marker-alt"></i> ${event.location}</p>
                        <p class="event-description">${event.description}</p>
                        <a href="events.html" class="btn secondary-btn">Learn More</a>
                    </div>
                </div>`;
    });

    eventsHtml += `
            </div>

            <div class="text-center mt-5" data-aos="fade-up" data-aos-delay="200">
                <a href="events.html" class="btn primary-btn">View All Events</a>
            </div>
        </div>
    </section>`;

    // Replace the events section in the index.html file
    return indexContent.substring(0, startIndex) + eventsHtml + indexContent.substring(endIndex);
  }

  return indexContent;
}

// Function to update the sermons section
function updateSermonsSection(indexContent) {
  // Find the sermons section
  const sermonsStartTag = '<!-- Latest Sermons Section -->';
  const sermonsEndTag = '<!-- Leadership Team Section -->';

  const startIndex = indexContent.indexOf(sermonsStartTag);
  const endIndex = indexContent.indexOf(sermonsEndTag);

  if (startIndex !== -1 && endIndex !== -1) {
    // Generate new sermons HTML
    let sermonsHtml = sermonsStartTag + `
    <section class="section sermons">
        <div class="container">
            <div class="section-title" data-aos="fade-up">
                <h2>Latest Sermons</h2>
                <p>Watch and listen to our recent messages</p>
            </div>

            <div class="row" data-aos="fade-up" data-aos-delay="100">`;

    // Add each sermon (limit to 3 for the homepage)
    sermons.slice(0, 3).forEach(sermon => {
      const sermonDate = new Date(sermon.date);
      const formattedDate = sermonDate.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });

      sermonsHtml += `
                <div class="col-md-4 mb-4">
                    <div class="card h-100">
                        <div class="sermon-thumbnail">
                            <img src="${sermon.thumbnailUrl}" class="card-img-top" alt="${sermon.title}">
                            <a href="${sermon.videoUrl}" class="play-btn" target="_blank">
                                <i class="fas fa-play"></i>
                            </a>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">${sermon.title}</h5>
                            <p class="sermon-preacher"><i class="fas fa-user"></i> ${sermon.preacher}</p>
                            <p class="sermon-date"><i class="far fa-calendar"></i> ${formattedDate}</p>
                            <p class="card-text">${sermon.description}</p>
                        </div>
                    </div>
                </div>`;
    });

    sermonsHtml += `
            </div>

            <div class="text-center mt-4" data-aos="fade-up" data-aos-delay="200">
                <a href="sermons.html" class="btn primary-btn">View All Sermons</a>
            </div>
        </div>
    </section>`;

    // Replace the sermons section in the index.html file
    return indexContent.substring(0, startIndex) + sermonsHtml + indexContent.substring(endIndex);
  }

  return indexContent;
}

// Function to update the leadership team section
function updateLeadershipSection(indexContent) {
  // Find the leadership team section
  const leadershipStartTag = '<!-- Leadership Team Section -->';
  const leadershipEndTag = '<!-- Blog Section -->';

  const startIndex = indexContent.indexOf(leadershipStartTag);
  const endIndex = indexContent.indexOf(leadershipEndTag);

  if (startIndex !== -1 && endIndex !== -1) {
    // Generate new leadership team HTML
    let leadershipHtml = leadershipStartTag + `
    <section class="section leadership" style="background-color: var(--light-color);">
        <div class="container">
            <div class="section-title" data-aos="fade-up">
                <h2>Our Leadership Team</h2>
                <p>Meet the people who guide our church</p>
            </div>

            <div class="row" data-aos="fade-up" data-aos-delay="100">`;

    // Sort leadership team by order
    const sortedTeam = [...leadershipTeam].sort((a, b) => a.order - b.order);

    // Add each leadership team member
    sortedTeam.forEach(leader => {
      leadershipHtml += `
                <div class="col-md-4 mb-4">
                    <div class="card leader-card h-100">
                        <img src="${leader.imageUrl}" class="card-img-top" alt="${leader.name}">
                        <div class="card-body text-center">
                            <h5 class="card-title">${leader.name}</h5>
                            <p class="leader-position">${leader.position}</p>
                            <p class="card-text">${leader.bio}</p>
                            <a href="mailto:${leader.email}" class="btn secondary-btn">
                                <i class="fas fa-envelope"></i> Contact
                            </a>
                        </div>
                    </div>
                </div>`;
    });

    leadershipHtml += `
            </div>

            <div class="text-center mt-4" data-aos="fade-up" data-aos-delay="200">
                <a href="about.html" class="btn primary-btn">Learn More About Us</a>
            </div>
        </div>
    </section>`;

    // Replace the leadership team section in the index.html file
    return indexContent.substring(0, startIndex) + leadershipHtml + indexContent.substring(endIndex);
  }

  return indexContent;
}

// Function to update the blog section
function updateBlogSection(indexContent) {
  // Find the blog section
  const blogStartTag = '<!-- Blog Section -->';
  const blogEndTag = '<!-- Gallery Section -->';

  const startIndex = indexContent.indexOf(blogStartTag);
  const endIndex = indexContent.indexOf(blogEndTag);

  if (startIndex !== -1 && endIndex !== -1) {
    // Generate new blog HTML
    let blogHtml = blogStartTag + `
    <section class="section blog">
        <div class="container">
            <div class="section-title" data-aos="fade-up">
                <h2>Latest Blog Posts</h2>
                <p>Read our latest articles and devotionals</p>
            </div>

            <div class="row" data-aos="fade-up" data-aos-delay="100">`;

    // Get featured or recent blog posts (limit to 3 for the homepage)
    const featuredPosts = blogPosts.filter(post => post.featured && post.status === 'published');
    const postsToShow = featuredPosts.length >= 3 ?
      featuredPosts.slice(0, 3) :
      [...featuredPosts, ...blogPosts.filter(post => !post.featured && post.status === 'published')].slice(0, 3);

    // Add each blog post
    postsToShow.forEach(post => {
      const postDate = new Date(post.date);
      const formattedDate = postDate.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });

      blogHtml += `
                <div class="col-md-4 mb-4">
                    <div class="card blog-card h-100">
                        <img src="${post.imageUrl}" class="card-img-top" alt="${post.title}">
                        <div class="card-body">
                            <h5 class="card-title">${post.title}</h5>
                            <p class="blog-meta">
                                <span><i class="fas fa-user"></i> ${post.author}</span>
                                <span><i class="far fa-calendar"></i> ${formattedDate}</span>
                            </p>
                            <p class="card-text">${post.content.substring(0, 100)}...</p>
                            <a href="blog-post.html?id=${post.id}" class="btn secondary-btn">Read More</a>
                        </div>
                    </div>
                </div>`;
    });

    blogHtml += `
            </div>

            <div class="text-center mt-4" data-aos="fade-up" data-aos-delay="200">
                <a href="blog.html" class="btn primary-btn">View All Posts</a>
            </div>
        </div>
    </section>`;

    // Replace the blog section in the index.html file
    return indexContent.substring(0, startIndex) + blogHtml + indexContent.substring(endIndex);
  }

  return indexContent;
}

// Function to save leadership team data to JSON file
function saveLeadershipData() {
  try {
    console.log('Saving leadership team data to file...');
    console.log('Current leadership team:', leadershipTeam);

    const leadershipData = JSON.stringify(leadershipTeam, null, 2);
    const filePath = path.join(__dirname, 'data/leadership.json');

    console.log('Writing to file:', filePath);
    fs.writeFileSync(filePath, leadershipData, 'utf8');

    console.log('Leadership team data saved to file successfully');
  } catch (err) {
    console.error('Error saving leadership team data:', err);
    console.error('Error details:', err.message, err.stack);
  }
}

// Function to save all data to JSON files
function saveAllData() {
  try {
    // Ensure data directory exists
    const dataDir = path.join(__dirname, 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir);
    }

    // Save events
    fs.writeFileSync(path.join(dataDir, 'events.json'), JSON.stringify(events, null, 2), 'utf8');
    console.log('Events data saved');

    // Save sermons
    fs.writeFileSync(path.join(dataDir, 'sermons.json'), JSON.stringify(sermons, null, 2), 'utf8');
    console.log('Sermons data saved');

    // Save blog posts
    fs.writeFileSync(path.join(dataDir, 'blog.json'), JSON.stringify(blogPosts, null, 2), 'utf8');
    console.log('Blog posts data saved');

    // Save gallery
    fs.writeFileSync(path.join(dataDir, 'gallery.json'), JSON.stringify(gallery, null, 2), 'utf8');
    console.log('Gallery data saved');

    // Save members
    fs.writeFileSync(path.join(dataDir, 'members.json'), JSON.stringify(members, null, 2), 'utf8');
    console.log('Members data saved');

    // Save prayer requests
    fs.writeFileSync(path.join(dataDir, 'prayer-requests.json'), JSON.stringify(prayerRequests, null, 2), 'utf8');
    console.log('Prayer requests data saved');

    // Save leadership team
    saveLeadershipData();

    console.log('All data saved successfully');
  } catch (err) {
    console.error('Error saving data:', err);
  }
}

// Function to load all data from JSON files
function loadAllData() {
  try {
    const dataDir = path.join(__dirname, 'data');

    // Load events
    const eventsPath = path.join(dataDir, 'events.json');
    if (fs.existsSync(eventsPath)) {
      events = JSON.parse(fs.readFileSync(eventsPath, 'utf8'));
      console.log('Events data loaded');
    }

    // Load sermons
    const sermonsPath = path.join(dataDir, 'sermons.json');
    if (fs.existsSync(sermonsPath)) {
      sermons = JSON.parse(fs.readFileSync(sermonsPath, 'utf8'));
      console.log('Sermons data loaded');
    }

    // Load blog posts
    const blogPath = path.join(dataDir, 'blog.json');
    if (fs.existsSync(blogPath)) {
      blogPosts = JSON.parse(fs.readFileSync(blogPath, 'utf8'));
      console.log('Blog posts data loaded');
    }

    // Load gallery
    const galleryPath = path.join(dataDir, 'gallery.json');
    if (fs.existsSync(galleryPath)) {
      gallery = JSON.parse(fs.readFileSync(galleryPath, 'utf8'));
      console.log('Gallery data loaded');
    }

    // Load members
    const membersPath = path.join(dataDir, 'members.json');
    if (fs.existsSync(membersPath)) {
      members = JSON.parse(fs.readFileSync(membersPath, 'utf8'));
      console.log('Members data loaded');
    }

    // Load prayer requests
    const prayerRequestsPath = path.join(dataDir, 'prayer-requests.json');
    if (fs.existsSync(prayerRequestsPath)) {
      prayerRequests = JSON.parse(fs.readFileSync(prayerRequestsPath, 'utf8'));
      console.log('Prayer requests data loaded');
    }

    console.log('All data loaded successfully');
  } catch (err) {
    console.error('Error loading data:', err);
  }
}

// Function to update the gallery section
function updateGallerySection(indexContent) {
  // Find the gallery section
  const galleryStartTag = '<!-- Gallery Section -->';
  const galleryEndTag = '<!-- Contact Section -->';

  const startIndex = indexContent.indexOf(galleryStartTag);
  const endIndex = indexContent.indexOf(galleryEndTag);

  if (startIndex !== -1 && endIndex !== -1) {
    // Generate new gallery HTML
    let galleryHtml = galleryStartTag + `
    <section class="section gallery" style="background-color: var(--light-color);">
        <div class="container">
            <div class="section-title" data-aos="fade-up">
                <h2>Photo Gallery</h2>
                <p>Moments from our church community</p>
            </div>

            <div class="row gallery-container" data-aos="fade-up" data-aos-delay="100">`;

    // Get featured gallery items (limit to 6 for the homepage)
    const featuredItems = gallery.filter(item => item.featured);
    const itemsToShow = featuredItems.length >= 6 ?
      featuredItems.slice(0, 6) :
      [...featuredItems, ...gallery.filter(item => !item.featured)].slice(0, 6);

    // Add each gallery item
    itemsToShow.forEach(item => {
      galleryHtml += `
                <div class="col-md-4 col-sm-6 mb-4">
                    <div class="gallery-item">
                        <a href="${item.imageUrl}" class="gallery-lightbox">
                            <img src="${item.imageUrl}" alt="${item.title}" class="img-fluid">
                            <div class="gallery-overlay">
                                <div class="gallery-info">
                                    <h4>${item.title}</h4>
                                    <p>${item.category}</p>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>`;
    });

    galleryHtml += `
            </div>

            <div class="text-center mt-4" data-aos="fade-up" data-aos-delay="200">
                <a href="gallery.html" class="btn primary-btn">View Full Gallery</a>
            </div>
        </div>
    </section>`;

    // Replace the gallery section in the index.html file
    return indexContent.substring(0, startIndex) + galleryHtml + indexContent.substring(endIndex);
  }

  return indexContent;
}

// Simple authentication for demo purposes
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;

  if (email === '<EMAIL>' && password === 'password') {
    res.json({
      token: 'demo-token-123456',
      user: {
        id: 1,
        name: 'Admin User',
        email: '<EMAIL>',
        role: 'admin'
      }
    });
  } else {
    res.status(401).json({ message: 'Invalid credentials' });
  }
});

// Serve template.html as the main website
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'template.html'));
});

// Load all data on server startup
console.log('Loading all data from files...');
loadAllData();

// Start the server
const PORT = 3000;
console.log('About to start server...');
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Main website: http://localhost:${PORT}/`);
  console.log(`Admin dashboard: http://localhost:${PORT}/admin-dashboard.html`);
});
console.log('Server setup complete');
