import React, { createContext, useState, useContext, useEffect } from 'react';
import mockApi from '../services/mockApi';

const AuthContext = createContext();

export function useAuth() {
  return useContext(AuthContext);
}

export function AuthProvider({ children }) {
  // Initialize currentUser from localStorage if available
  const [currentUser, setCurrentUser] = useState(() => {
    const savedUser = localStorage.getItem('currentUser');
    return savedUser ? JSON.parse(savedUser) : null;
  });
  const [token, setToken] = useState(localStorage.getItem('token'));
  const [loading, setLoading] = useState(false); // Set to false to avoid loading screen
  const [error, setError] = useState('');

  // No need to set up axios defaults when using mock API

  // We don't need to load user data since we're getting it from localStorage
  // This effect just ensures token and currentUser are in sync
  useEffect(() => {
    if (!token) {
      localStorage.removeItem('currentUser');
      setCurrentUser(null);
    } else if (!currentUser) {
      // If we have a token but no user, try to get user from localStorage
      const savedUser = localStorage.getItem('currentUser');
      if (savedUser) {
        setCurrentUser(JSON.parse(savedUser));
      } else {
        // If no user in localStorage, remove the token
        localStorage.removeItem('token');
        setToken(null);
      }
    }
  }, [token, currentUser]);

  // Login function
  const login = async (email, password) => {
    try {
      // Make API call to the mock API
      const res = await mockApi.login(email, password);

      setToken(res.token);
      return true;
    } catch (err) {
      setError(err.message || 'Login failed');
      return false;
    }
  };

  // Logout function
  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('currentUser');
    setToken(null);
    setCurrentUser(null);
  };

  const value = {
    currentUser,
    isAuthenticated: !!currentUser,
    loading,
    error,
    login,
    logout
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
