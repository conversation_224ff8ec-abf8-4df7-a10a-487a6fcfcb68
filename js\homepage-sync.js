/**
 * Homepage Real-Time Sync Script
 * North Texas SDA Church
 *
 * This script fetches the latest 3 events and 3 sermons from the dashboard API
 * and displays them on the homepage while maintaining the exact same styling.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('🏠 Homepage sync script loaded');

    // API Configuration
    const API_URL = 'https://sda-church-production.up.railway.app/api';

    // Initialize sync
    initializeHomepageSync();
});

function initializeHomepageSync() {
    // Load events and sermons
    loadLatestEvents();
    loadLatestSermons();

    // Set up periodic refresh (every 60 seconds for homepage)
    setInterval(() => {
        loadLatestEvents();
        loadLatestSermons();
    }, 60000);

    console.log('✅ Homepage sync initialized');
}

// ===== EVENTS SYNC =====

async function loadLatestEvents() {
    try {
        console.log('📅 Loading latest events for homepage...');

        const response = await fetch(`${API_URL}/events`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const events = await response.json();
        console.log('📋 Events received:', events);

        // Filter upcoming events and get latest 3
        const upcomingEvents = events
            .filter(event => event.status === 'upcoming' || event.status === 'ongoing')
            .sort((a, b) => new Date(a.date) - new Date(b.date))
            .slice(0, 3);

        displayEventsOnHomepage(upcomingEvents);

    } catch (error) {
        console.error('❌ Error loading events for homepage:', error);
        // Keep existing static content on error
    }
}

function displayEventsOnHomepage(events) {
    const eventsContainer = document.querySelector('.modern-events-container');
    if (!eventsContainer) {
        console.error('❌ Events container not found on homepage');
        return;
    }

    if (events.length === 0) {
        console.log('📅 No upcoming events, keeping static content');
        return;
    }

    // Clear existing content
    eventsContainer.innerHTML = '';

    // Add each event
    events.forEach(event => {
        const eventCard = createEventCard(event);
        eventsContainer.appendChild(eventCard);
    });

    console.log(`✅ Displayed ${events.length} events on homepage`);
}

function createEventCard(event) {
    const { dayName, day, month } = formatEventDate(event.date);
    const startTime = formatTime(event.startTime);
    const endTime = formatTime(event.endTime);
    const timeDisplay = endTime ? `${month} ${day} @ ${startTime} - ${endTime}` : `${month} ${day} @ ${startTime}`;

    // Use event image if provided, otherwise use default
    const eventImage = event.imageUrl || 'https://blog.ronniefloyd.com/wp-content/uploads/Preaching.png';

    const eventCard = document.createElement('div');
    eventCard.className = 'modern-event-card';
    eventCard.innerHTML = `
        <div class="modern-event-left">
            <div class="modern-event-date">
                <span class="modern-event-day-name">${dayName}</span>
                <span class="modern-event-day">${day}</span>
                <span class="modern-event-month">${month}</span>
            </div>
        </div>
        <div class="modern-event-image">
            <img src="${eventImage}" alt="${event.title}">
        </div>
        <div class="modern-event-content">
            <h3 class="modern-event-title">${event.title}</h3>
            <p class="modern-event-description">${event.description}</p>
            <div class="modern-event-details">
                <p class="modern-event-time">${timeDisplay}</p>
            </div>
        </div>
        <div class="modern-event-action">
            <a href="events.html" class="modern-event-button">VIEW DETAILS</a>
        </div>
    `;

    return eventCard;
}

// ===== SERMONS SYNC =====

async function loadLatestSermons() {
    try {
        console.log('🎬 Loading latest sermons for homepage...');

        const response = await fetch(`${API_URL}/sermons`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const sermons = await response.json();
        console.log('📺 Sermons received:', sermons);

        // Get latest 3 sermons
        const latestSermons = sermons
            .sort((a, b) => new Date(b.date) - new Date(a.date))
            .slice(0, 3);

        displaySermonsOnHomepage(latestSermons);

    } catch (error) {
        console.error('❌ Error loading sermons for homepage:', error);
        // Keep existing static content on error
    }
}

function displaySermonsOnHomepage(sermons) {
    const sermonsContainer = document.querySelector('.featured-sermons-container');
    if (!sermonsContainer) {
        console.error('❌ Sermons container not found on homepage');
        return;
    }

    if (sermons.length === 0) {
        console.log('🎬 No sermons available, keeping static content');
        return;
    }

    // Clear existing content
    sermonsContainer.innerHTML = '';

    // Add each sermon
    sermons.forEach(sermon => {
        const sermonCard = createSermonCard(sermon);
        sermonsContainer.appendChild(sermonCard);
    });

    console.log(`✅ Displayed ${sermons.length} sermons on homepage`);
}

function createSermonCard(sermon) {
    const formattedDate = formatSermonDate(sermon.date);
    const thumbnailUrl = sermon.thumbnailUrl || getYouTubeThumbnail(sermon.videoUrl);

    const sermonCard = document.createElement('div');
    sermonCard.className = 'featured-sermon-card';
    sermonCard.style.cursor = 'pointer';
    sermonCard.innerHTML = `
        <div class="featured-sermon-thumbnail">
            <img src="${thumbnailUrl}" alt="${sermon.title}">
        </div>
        <div class="featured-sermon-date">${formattedDate.toUpperCase()}</div>
        <h3 class="featured-sermon-title">${sermon.title}</h3>
        <div class="featured-sermon-author">
            <div class="featured-sermon-author-image">
                <img src="images/logo.png" alt="Pastor">
            </div>
            <div class="featured-sermon-author-name">${sermon.preacher || 'Pastor'}</div>
        </div>
    `;

    // Add click event to go to sermon video page
    sermonCard.addEventListener('click', function() {
        window.location.href = `sermon-video.html?id=${sermon.id}`;
    });

    return sermonCard;
}

// ===== UTILITY FUNCTIONS =====

function formatEventDate(dateString) {
    const date = new Date(dateString);
    const dayNames = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];
    const monthNames = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];

    return {
        dayName: dayNames[date.getDay()],
        day: date.getDate().toString().padStart(2, '0'),
        month: monthNames[date.getMonth()]
    };
}

function formatSermonDate(dateString) {
    const date = new Date(dateString);
    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    };
    return date.toLocaleDateString('en-US', options);
}

function formatTime(timeString) {
    if (!timeString) return '';

    const [hours, minutes] = timeString.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;

    return `${displayHour}:${minutes} ${ampm}`;
}

function getYouTubeThumbnail(videoUrl) {
    if (!videoUrl) return 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=600&q=80';

    const videoId = getYouTubeVideoId(videoUrl);
    if (videoId) {
        return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
    }
    return 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=600&q=80';
}

function getYouTubeVideoId(url) {
    if (!url) return null;

    const patterns = [
        /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/|youtube\.com\/v\/|youtube\.com\/watch\?.*&v=)([^#&?]*)/,
        /youtube\.com\/watch\?.*v=([^#&?]*)/,
        /youtu\.be\/([^#&?]*)/,
        /youtube\.com\/embed\/([^#&?]*)/,
        /youtube\.com\/v\/([^#&?]*)/
    ];

    for (const pattern of patterns) {
        const match = url.match(pattern);
        if (match && match[1] && match[1].length === 11) {
            return match[1];
        }
    }

    try {
        const urlObj = new URL(url);
        const videoId = urlObj.searchParams.get('v');
        if (videoId && videoId.length === 11) {
            return videoId;
        }
    } catch (e) {
        console.log('URL parsing failed:', e);
    }

    return null;
}

// Export functions for external use
window.HomepageSync = {
    loadLatestEvents,
    loadLatestSermons,
    initializeHomepageSync
};
