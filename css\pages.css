/*
* North Texas SDA Church - Pages Stylesheet
* Author: Augment Agent
* Version: 1.0
*/

/* Common Page Styles */
.page-header {
    height: 100vh;
    min-height: 400px;
    position: relative;
    display: flex;
    align-items: end;
    color: #fff;
    text-align: left;
    padding-bottom: 40px;
}

.page-header-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    filter: brightness(0.7);
    z-index: -1;
}

.page-title {
    font-size: 150px;
    font-weight: 700;
    font-family: 'Bebas Neue', sans-serif;
}

.page-subtitle {
    font-size: 24px;
    max-width: 800px;
    font-family: '<PERSON>', serif;
}

/* About Page Styles */
.about-content-section {
    padding: 100px 0;
    background-color: #fff;
}

.about-content-container {
    display: flex;
    flex-wrap: wrap;
    gap: 50px;
}

.about-content-left {
    flex: 1;
    min-width: 300px;
}

.about-content-right {
    flex: 1;
    min-width: 300px;
}

.about-content-title {
    font-size: 48px;
    margin-bottom: 30px;
    color: #333;
    font-family: '<PERSON><PERSON> Neue', sans-serif;
}

.about-content-text {
    font-size: 18px;
    line-height: 1.8;
    color: #555;
    margin-bottom: 30px;
    font-family: 'Lora', serif;
}

.about-content-image {
    width: 100%;
    border-radius: 10px;
    overflow: hidden;
}

.about-content-image img {
    width: 100%;
    height: auto;
    display: block;
}

/* Sermon Page Styles */
.sermons-page {
    background-color: #fff;
}

.sermons-section {
    padding: 100px 0;
    background-color: #fff;
}

.sermons-header {
    margin-bottom: 50px;
    text-align: center;
}

.sermons-label {
    display: inline-block;
    background-color: #222;
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    padding: 8px 15px;
    margin-bottom: 20px;
    text-transform: uppercase;
    font-family: 'Oswald', sans-serif;
}

.sermons-title {
    font-size: 80px;
    line-height: 1;
    text-transform: uppercase;
    margin-bottom: 30px;
    font-family: 'Bebas Neue', sans-serif;
    background: linear-gradient(to right, #222 0%, #8B4513 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
}

.sermons-description {
    font-size: 18px;
    line-height: 1.6;
    color: #555;
    max-width: 800px;
    margin: 0 auto;
}

.sermons-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
}

.sermon-card {
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
    background-color: #fff;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.sermon-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.sermon-thumbnail {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.sermon-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.sermon-play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background-color: rgba(249, 92, 40, 0.8);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.sermon-play-button:hover {
    background-color: #F95C28;
}

.sermon-details {
    padding: 20px;
}

.sermon-date {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
    text-transform: uppercase;
    font-weight: 600;
}

.sermon-title {
    font-size: 22px;
    margin-bottom: 15px;
    color: #333;
    font-weight: 600;
    line-height: 1.3;
}

.sermon-speaker {
    display: flex;
    align-items: center;
    margin-top: 15px;
}

.sermon-speaker-image {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 10px;
    background-color: #F95C28;
}

.sermon-speaker-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.sermon-speaker-name {
    font-size: 14px;
    color: #666;
}

.sermon-categories {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 20px;
}

.sermon-category {
    font-size: 12px;
    padding: 5px 10px;
    background-color: #f5f5f5;
    border-radius: 20px;
    color: #666;
}

.sermon-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.sermon-action-button {
    font-size: 14px;
    color: #333;
    display: flex;
    align-items: center;
}

.sermon-action-button i {
    margin-right: 5px;
}

.contact-info-section{
    padding-top: 30px;
}

/* Events Page Styles */
.events-page {
    background-color: #fff;
}

.events-section {
    padding: 100px 0;
    background-color: #fff;
}

.events-header {
    margin-bottom: 50px;
    text-align: center;
}

.events-label {
    display: inline-block;
    background-color: #222;
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    padding: 8px 15px;
    margin-bottom: 20px;
    text-transform: uppercase;
    font-family: 'Oswald', sans-serif;
}

.events-title {
    font-size: 80px;
    line-height: 1;
    text-transform: uppercase;
    margin-bottom: 30px;
    font-family: 'Bebas Neue', sans-serif;
    background: linear-gradient(to right, #222 0%, #8B4513 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
}

.events-description {
    font-size: 18px;
    line-height: 1.6;
    color: #555;
    max-width: 800px;
    margin: 0 auto;
}

.events-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
}

.event-card {
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
    background-color: #fff;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
}

.event-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.event-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.event-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.event-date-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background-color: #F95C28;
    color: #fff;
    padding: 10px 15px;
    border-radius: 5px;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
}

.event-details {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.event-meta {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
}

.event-time, .event-location {
    font-size: 14px;
    color: #666;
    display: flex;
    align-items: center;
}

.event-time i, .event-location i {
    margin-right: 5px;
    color: #F95C28;
}

.event-title {
    font-size: 22px;
    margin-bottom: 15px;
    color: #333;
    font-weight: 600;
    line-height: 1.3;
}

.event-description {
    font-size: 16px;
    line-height: 1.6;
    color: #666;
    margin-bottom: 20px;
    flex: 1;
}

.event-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.event-category {
    font-size: 14px;
    color: #F95C28;
    font-weight: 600;
}

.event-button {
    display: inline-flex;
    align-items: center;
    color: #333;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    transition: all 0.3s ease;
}

.event-button svg {
    margin-left: 5px;
    transition: transform 0.3s ease;
}

.event-button:hover {
    color: #F95C28;
}

.event-button:hover svg {
    transform: translateX(5px);
}

/* Responsive Styles */
@media (max-width: 768px) {
    .page-title {
        font-size: 60px;
        line-height: 1;
    }

    .page-header{
        height: 60vh;
    }

    .page-subtitle {
        font-size: 18px;
        line-height: 1.2;
    }

    .about-content-container,
    .service-times-container {
        flex-direction: column;
    }

    .sermons-title,
    .events-title {
        font-size: 60px;
    }

    .sermons-container,
    .events-container {
        grid-template-columns: 1fr;
    }
}
