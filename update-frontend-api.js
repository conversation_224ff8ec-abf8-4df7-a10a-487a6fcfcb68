// Script to update frontend API URL after backend deployment
// Run this after you get your Railway backend URL

const fs = require('fs');
const path = require('path');

// Get the Railway backend URL from command line argument
const backendUrl = process.argv[2];

if (!backendUrl) {
    console.log('❌ Please provide your Railway backend URL');
    console.log('Usage: node update-frontend-api.js https://your-app.railway.app');
    console.log('');
    console.log('Example:');
    console.log('node update-frontend-api.js https://church-backend-production.railway.app');
    process.exit(1);
}

// Validate URL
if (!backendUrl.startsWith('https://') && !backendUrl.startsWith('http://')) {
    console.log('❌ Please provide a valid URL starting with https:// or http://');
    process.exit(1);
}

const apiUrl = backendUrl.endsWith('/') ? `${backendUrl}api` : `${backendUrl}/api`;

console.log(`🔄 Updating frontend to use API: ${apiUrl}`);

// Files to update
const filesToUpdate = [
    'admin-dashboard.html',
    'index.html'
];

let updatedFiles = 0;

filesToUpdate.forEach(filename => {
    const filePath = path.join(__dirname, filename);

    if (fs.existsSync(filePath)) {
        let content = fs.readFileSync(filePath, 'utf8');
        let originalContent = content;

        // Replace localhost API URL with production URL
        content = content.replace(
            /const API_URL = ['"`]http:\/\/localhost:3000\/api['"`];?/g,
            `const API_URL = '${apiUrl}';`
        );

        // Also replace any other localhost references
        content = content.replace(
            /['"`]http:\/\/localhost:3000\/api['"`]/g,
            `'${apiUrl}'`
        );

        // Replace the comment line too
        content = content.replace(
            /\/\/ API URL - Update this with your Railway backend URL after deployment/g,
            '// API URL - Updated for production deployment'
        );

        content = content.replace(
            /\/\/ Will be updated to production URL/g,
            '// Production API URL'
        );

        if (content !== originalContent) {
            fs.writeFileSync(filePath, content);
            console.log(`✅ Updated ${filename}`);
            updatedFiles++;
        } else {
            console.log(`⚠️  No changes needed in ${filename}`);
        }
    } else {
        console.log(`⚠️  File not found: ${filename}`);
    }
});

if (updatedFiles > 0) {
    console.log('\n🎉 Frontend API URLs updated successfully!');
    console.log('\n📝 Next steps:');
    console.log('1. Commit and push these changes to GitHub:');
    console.log('   git add .');
    console.log('   git commit -m "Update API URLs for production deployment"');
    console.log('   git push');
    console.log('2. Netlify will automatically redeploy');
    console.log('3. Test your live application');
} else {
    console.log('\n⚠️  No files were updated. API URLs may already be set correctly.');
}

console.log('\n🔗 Your API endpoints:');
console.log(`- Health Check: ${backendUrl}/api/health`);
console.log(`- Login: ${backendUrl}/api/auth/login`);
console.log(`- Events: ${backendUrl}/api/events`);
console.log(`- Sermons: ${backendUrl}/api/sermons`);
console.log(`- Leadership: ${backendUrl}/api/leadership`);
console.log(`- Members: ${backendUrl}/api/members`);
console.log(`- Prayer Requests: ${backendUrl}/api/prayer-requests`);

console.log('\n🎯 Test your deployment:');
console.log(`1. Visit: ${backendUrl}/api/health`);
console.log('2. Should return: {"status":"healthy","database":"connected"}');
console.log('3. Visit: https://sdachurch.netlify.app/admin-dashboard.html');
console.log('4. Login with: <EMAIL> / admin123');
