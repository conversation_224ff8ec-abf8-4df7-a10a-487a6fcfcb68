import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from './contexts/AuthContext';

// Pages
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import Events from './pages/Events';
import EventForm from './pages/EventForm';
import Blog from './pages/Blog';
import BlogForm from './pages/BlogForm';
import Gallery from './pages/Gallery';
import GalleryForm from './pages/GalleryForm';
import Settings from './pages/Settings';
import NotFound from './pages/NotFound';

// Components
import Layout from './components/common/Layout';

// Protected Route Component
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();
  
  if (loading) {
    return <div className="loading-screen">Loading...</div>;
  }
  
  if (!isAuthenticated) {
    return <Navigate to="/login" />;
  }
  
  return children;
};

function App() {
  return (
    <Routes>
      {/* Public Routes */}
      <Route path="/login" element={<Login />} />
      
      {/* Protected Routes */}
      <Route path="/" element={
        <ProtectedRoute>
          <Layout />
        </ProtectedRoute>
      }>
        <Route index element={<Dashboard />} />
        <Route path="events" element={<Events />} />
        <Route path="events/new" element={<EventForm />} />
        <Route path="events/:id" element={<EventForm />} />
        <Route path="blog" element={<Blog />} />
        <Route path="blog/new" element={<BlogForm />} />
        <Route path="blog/:id" element={<BlogForm />} />
        <Route path="gallery" element={<Gallery />} />
        <Route path="gallery/new" element={<GalleryForm />} />
        <Route path="gallery/:id" element={<GalleryForm />} />
        <Route path="settings" element={<Settings />} />
      </Route>
      
      {/* 404 Route */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
}

export default App;
