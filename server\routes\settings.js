const express = require('express');
const router = express.Router();
const { check, validationResult } = require('express-validator');
const Setting = require('../models/Setting');
const { auth, admin } = require('../middleware/auth');

// @route   GET api/settings
// @desc    Get all settings
// @access  Public
router.get('/', async (req, res) => {
  try {
    const settings = await Setting.findAll({
      order: [['group', 'ASC'], ['key', 'ASC']]
    });
    
    // Convert to object with key-value pairs
    const settingsObject = {};
    settings.forEach(setting => {
      // Parse value based on type
      let value = setting.value;
      if (setting.type === 'number') {
        value = parseFloat(value);
      } else if (setting.type === 'boolean') {
        value = value === 'true';
      } else if (setting.type === 'json' || setting.type === 'array') {
        try {
          value = JSON.parse(value);
        } catch (err) {
          console.error(`Error parsing JSON for setting ${setting.key}:`, err);
        }
      }
      
      settingsObject[setting.key] = {
        value,
        type: setting.type,
        group: setting.group,
        description: setting.description
      };
    });
    
    res.json(settingsObject);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

// @route   GET api/settings/group/:group
// @desc    Get settings by group
// @access  Public
router.get('/group/:group', async (req, res) => {
  try {
    const settings = await Setting.findAll({
      where: { group: req.params.group },
      order: [['key', 'ASC']]
    });
    
    // Convert to object with key-value pairs
    const settingsObject = {};
    settings.forEach(setting => {
      // Parse value based on type
      let value = setting.value;
      if (setting.type === 'number') {
        value = parseFloat(value);
      } else if (setting.type === 'boolean') {
        value = value === 'true';
      } else if (setting.type === 'json' || setting.type === 'array') {
        try {
          value = JSON.parse(value);
        } catch (err) {
          console.error(`Error parsing JSON for setting ${setting.key}:`, err);
        }
      }
      
      settingsObject[setting.key] = {
        value,
        type: setting.type,
        description: setting.description
      };
    });
    
    res.json(settingsObject);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

// @route   GET api/settings/:key
// @desc    Get setting by key
// @access  Public
router.get('/:key', async (req, res) => {
  try {
    const setting = await Setting.findOne({
      where: { key: req.params.key }
    });
    
    if (!setting) {
      return res.status(404).json({ message: 'Setting not found' });
    }
    
    // Parse value based on type
    let value = setting.value;
    if (setting.type === 'number') {
      value = parseFloat(value);
    } else if (setting.type === 'boolean') {
      value = value === 'true';
    } else if (setting.type === 'json' || setting.type === 'array') {
      try {
        value = JSON.parse(value);
      } catch (err) {
        console.error(`Error parsing JSON for setting ${setting.key}:`, err);
      }
    }
    
    res.json({
      key: setting.key,
      value,
      type: setting.type,
      group: setting.group,
      description: setting.description
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

// @route   POST api/settings
// @desc    Create or update a setting
// @access  Private (Admin)
router.post(
  '/',
  [
    auth,
    admin,
    [
      check('key', 'Key is required').not().isEmpty(),
      check('value', 'Value is required').exists(),
      check('type', 'Type is required').isIn(['text', 'number', 'boolean', 'json', 'array'])
    ]
  ],
  async (req, res) => {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { key, value, type, group, description } = req.body;

    try {
      // Check if setting already exists
      let setting = await Setting.findOne({ where: { key } });
      
      // Prepare value based on type
      let processedValue = value;
      if (type === 'json' || type === 'array') {
        if (typeof value !== 'string') {
          processedValue = JSON.stringify(value);
        }
      }
      
      if (setting) {
        // Update existing setting
        setting.value = processedValue;
        setting.type = type;
        if (group) setting.group = group;
        if (description) setting.description = description;
        
        await setting.save();
      } else {
        // Create new setting
        setting = await Setting.create({
          key,
          value: processedValue,
          type,
          group,
          description
        });
      }
      
      res.json(setting);
    } catch (err) {
      console.error(err.message);
      res.status(500).send('Server error');
    }
  }
);

// @route   DELETE api/settings/:key
// @desc    Delete a setting
// @access  Private (Admin)
router.delete('/:key', [auth, admin], async (req, res) => {
  try {
    // Find setting by key
    const setting = await Setting.findOne({
      where: { key: req.params.key }
    });
    
    if (!setting) {
      return res.status(404).json({ message: 'Setting not found' });
    }
    
    // Delete setting
    await setting.destroy();
    
    res.json({ message: 'Setting removed' });
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

module.exports = router;
