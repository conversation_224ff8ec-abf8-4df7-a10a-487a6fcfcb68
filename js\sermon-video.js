// Sermon Video Page JavaScript
const API_URL = 'http://localhost:3001/api';

// Function to get URL parameters
function getUrlParameter(name) {
    name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
    const regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
    const results = regex.exec(location.search);
    return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
}

// Function to extract YouTube video ID from URL
function getYouTubeVideoId(url) {
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);
    return (match && match[2].length === 11) ? match[2] : null;
}

// Function to convert YouTube URL to embed URL
function getYouTubeEmbedUrl(url) {
    const videoId = getYouTubeVideoId(url);
    if (videoId) {
        return `https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0&modestbranding=1`;
    }
    return url;
}

// Function to format date
function formatDate(dateString) {
    const date = new Date(dateString);
    const options = { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
    };
    return date.toLocaleDateString('en-US', options);
}

// Function to get YouTube thumbnail URL
function getYouTubeThumbnail(videoUrl) {
    const videoId = getYouTubeVideoId(videoUrl);
    if (videoId) {
        return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
    }
    return 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=600&q=80';
}

// Function to load sermon details
async function loadSermonDetails(sermonId) {
    try {
        console.log('🎬 Loading sermon details for ID:', sermonId);
        
        const response = await fetch(`${API_URL}/sermons/${sermonId}`);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const sermon = await response.json();
        console.log('📺 Sermon details loaded:', sermon);
        
        // Update page title
        document.title = `${sermon.title} - North Texas SDA Church`;
        
        // Update sermon details
        document.getElementById('sermon-title').textContent = sermon.title;
        document.getElementById('sermon-description').textContent = sermon.description;
        document.getElementById('sermon-preacher').textContent = sermon.preacher || 'Pastor';
        document.getElementById('sermon-date').textContent = formatDate(sermon.date);
        
        // Update video iframe
        const videoIframe = document.getElementById('sermon-video');
        const embedUrl = getYouTubeEmbedUrl(sermon.videoUrl);
        videoIframe.src = embedUrl;
        
        // Load related sermons
        loadRelatedSermons(sermonId);
        
    } catch (error) {
        console.error('❌ Error loading sermon details:', error);
        showErrorMessage();
    }
}

// Function to load related sermons
async function loadRelatedSermons(currentSermonId) {
    try {
        const response = await fetch(`${API_URL}/sermons`);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const allSermons = await response.json();
        
        // Filter out current sermon and get up to 3 related sermons
        const relatedSermons = allSermons
            .filter(sermon => sermon.id != currentSermonId)
            .slice(0, 3);
        
        const relatedContainer = document.getElementById('related-sermons');
        
        if (relatedSermons.length > 0) {
            relatedContainer.innerHTML = '';
            
            relatedSermons.forEach(sermon => {
                const thumbnailUrl = sermon.thumbnailUrl || getYouTubeThumbnail(sermon.videoUrl);
                
                const sermonCard = document.createElement('div');
                sermonCard.className = 'related-sermon-card';
                sermonCard.setAttribute('data-sermon-id', sermon.id);
                
                sermonCard.innerHTML = `
                    <div class="related-sermon-thumbnail">
                        <img src="${thumbnailUrl}" alt="${sermon.title}">
                        <div class="related-sermon-play">
                            <i class="fas fa-play"></i>
                        </div>
                    </div>
                    <div class="related-sermon-details">
                        <h4 class="related-sermon-title">${sermon.title}</h4>
                        <p class="related-sermon-preacher">${sermon.preacher || 'Pastor'}</p>
                        <p class="related-sermon-date">${formatDate(sermon.date)}</p>
                    </div>
                `;
                
                // Add click event listener
                sermonCard.addEventListener('click', function() {
                    const sermonId = this.getAttribute('data-sermon-id');
                    window.location.href = `sermon-video.html?id=${sermonId}`;
                });
                
                relatedContainer.appendChild(sermonCard);
            });
        } else {
            relatedContainer.innerHTML = `
                <div class="no-related-sermons">
                    <p>No related sermons available at this time.</p>
                </div>
            `;
        }
        
    } catch (error) {
        console.error('❌ Error loading related sermons:', error);
    }
}

// Function to show error message
function showErrorMessage() {
    document.getElementById('sermon-title').textContent = 'Sermon Not Found';
    document.getElementById('sermon-description').textContent = 'The requested sermon could not be found. Please check the URL or return to the sermons page.';
    document.getElementById('sermon-preacher').textContent = '';
    document.getElementById('sermon-date').textContent = '';
    
    // Hide video container
    const videoContainer = document.querySelector('.video-container');
    if (videoContainer) {
        videoContainer.style.display = 'none';
    }
    
    // Hide related sermons
    const relatedSermons = document.querySelector('.related-sermons');
    if (relatedSermons) {
        relatedSermons.style.display = 'none';
    }
}

// Function to initialize the page
function initializePage() {
    const sermonId = getUrlParameter('id');
    
    if (!sermonId) {
        console.error('❌ No sermon ID provided in URL');
        showErrorMessage();
        return;
    }
    
    console.log('🎬 Initializing sermon video page with ID:', sermonId);
    loadSermonDetails(sermonId);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎬 Sermon video page loaded');
    initializePage();
});

// Export functions for external use
window.SermonVideo = {
    loadSermonDetails,
    loadRelatedSermons,
    initializePage
};
