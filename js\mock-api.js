/**
 * Mock API for testing the website without a backend
 *
 * This script intercepts fetch requests to the API and returns mock data.
 * It's useful for testing the website without a backend API.
 *
 * To use this script, include it before api-connector.js:
 * <script src="./js/mock-api.js"></script>
 * <script src="./js/api-connector.js"></script>
 */

// Mock data for events
const mockEvents = [
    {
        id: 1,
        title: "Community Service Day",
        date: "2024-06-15",
        time: "9:00 AM - 2:00 PM",
        location: "Church Grounds",
        description: "Join us for a day of service to our community."
    },
    {
        id: 2,
        title: "Youth Concert",
        date: "2024-06-22",
        time: "5:00 PM - 7:00 PM",
        location: "Main Sanctuary",
        description: "An evening of music and worship led by our youth."
    },
    {
        id: 3,
        title: "Bible Study Series",
        date: "2024-07-01",
        time: "7:00 PM - 8:30 PM",
        location: "Fellowship Hall",
        description: "A deep dive into the book of Romans."
    }
];

// Mock data for sermons
const mockSermons = [
    {
        id: 1,
        title: "Walking in Faith",
        speaker: "Pastor <PERSON>",
        date: "2024-06-10",
        thumbnail: "images/sermon1.jpg",
        videoUrl: "https://www.youtube.com/watch?v=example1"
    },
    {
        id: 2,
        title: "The Power of Prayer",
        speaker: "Pastor Jane Doe",
        date: "2024-06-03",
        thumbnail: "images/sermon2.jpg",
        videoUrl: "https://www.youtube.com/watch?v=example2"
    },
    {
        id: 3,
        title: "Living with Purpose",
        speaker: "Elder David Johnson",
        date: "2024-05-27",
        thumbnail: "images/sermon3.jpg",
        videoUrl: "https://www.youtube.com/watch?v=example3"
    }
];

// Mock data for leadership team
const mockLeadership = [
    {
        id: 1,
        name: "Pastor John Smith",
        role: "Senior Pastor",
        image: "images/pastor.jpg",
        order: 1
    },
    {
        id: 2,
        name: "David Johnson",
        role: "Head Elder",
        image: "images/elder1.jpg",
        order: 2
    },
    {
        id: 3,
        name: "Sarah Williams",
        role: "Elder",
        image: "images/elder2.jpg",
        order: 3
    },
    {
        id: 4,
        name: "Michael Brown",
        role: "Head Deacon",
        image: "images/deacon.jpg",
        order: 4
    }
];

// Override the fetch function to intercept API requests
const originalFetch = window.fetch;
window.fetch = function(url, options) {
    // Check if the URL is an API request
    if (typeof url === 'string' && url.includes('/api/')) {
        console.log('Intercepting API request:', url);

        // Simulate network delay
        return new Promise((resolve) => {
            setTimeout(() => {
                // Return mock data based on the endpoint
                if (url.includes('/api/events')) {
                    resolve({
                        ok: true,
                        json: () => Promise.resolve(mockEvents)
                    });
                } else if (url.includes('/api/sermons')) {
                    resolve({
                        ok: true,
                        json: () => Promise.resolve(mockSermons)
                    });
                } else if (url.includes('/api/leadership')) {
                    resolve({
                        ok: true,
                        json: () => Promise.resolve(mockLeadership)
                    });
                } else {
                    // If the endpoint is not recognized, pass through to the original fetch
                    resolve(originalFetch(url, options));
                }
            }, 500); // 500ms delay to simulate network latency
        });
    }

    // For non-API requests, use the original fetch
    return originalFetch(url, options);
};

// Custom debug logger
function debugLog(message) {
    console.log(message);

    // Also log to the debug console on the page
    const debugLog = document.getElementById('debug-log');
    if (debugLog) {
        const logEntry = document.createElement('div');
        logEntry.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
        debugLog.appendChild(logEntry);
        debugLog.scrollTop = debugLog.scrollHeight;
    }
}

debugLog('Mock API initialized. API requests will return mock data.');
