<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            text-align: center;
        }
        .container {
            margin-bottom: 30px;
        }
        .item {
            border: 1px solid #ddd;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 5px;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <h1>API Test</h1>
    
    <div class="container">
        <h2>Events</h2>
        <button id="fetch-events">Fetch Events</button>
        <div id="events-container"></div>
    </div>
    
    <div class="container">
        <h2>Leadership Team</h2>
        <button id="fetch-leadership">Fetch Leadership Team</button>
        <div id="leadership-container"></div>
    </div>
    
    <script>
        document.getElementById('fetch-events').addEventListener('click', fetchEvents);
        document.getElementById('fetch-leadership').addEventListener('click', fetchLeadership);
        
        async function fetchEvents() {
            const container = document.getElementById('events-container');
            container.innerHTML = 'Loading...';
            
            try {
                const response = await fetch('http://localhost:3000/api/events');
                
                if (!response.ok) {
                    throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
                }
                
                const events = await response.json();
                
                if (events.length === 0) {
                    container.innerHTML = '<p>No events found</p>';
                    return;
                }
                
                container.innerHTML = '';
                events.forEach(event => {
                    const eventElement = document.createElement('div');
                    eventElement.className = 'item';
                    eventElement.innerHTML = `
                        <h3>${event.title}</h3>
                        <p><strong>Date:</strong> ${event.date}</p>
                        <p><strong>Time:</strong> ${event.startTime} - ${event.endTime}</p>
                        <p><strong>Location:</strong> ${event.location}</p>
                        <p>${event.description}</p>
                    `;
                    container.appendChild(eventElement);
                });
            } catch (error) {
                container.innerHTML = `<p class="error">Error: ${error.message}</p>`;
                console.error('Error fetching events:', error);
            }
        }
        
        async function fetchLeadership() {
            const container = document.getElementById('leadership-container');
            container.innerHTML = 'Loading...';
            
            try {
                const response = await fetch('http://localhost:3000/api/leadership');
                
                if (!response.ok) {
                    throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
                }
                
                const leaders = await response.json();
                
                if (leaders.length === 0) {
                    container.innerHTML = '<p>No leadership team members found</p>';
                    return;
                }
                
                container.innerHTML = '';
                leaders.forEach(leader => {
                    const leaderElement = document.createElement('div');
                    leaderElement.className = 'item';
                    leaderElement.innerHTML = `
                        <h3>${leader.name}</h3>
                        <p><strong>Position:</strong> ${leader.position}</p>
                        <p>${leader.bio || ''}</p>
                    `;
                    container.appendChild(leaderElement);
                });
            } catch (error) {
                container.innerHTML = `<p class="error">Error: ${error.message}</p>`;
                console.error('Error fetching leadership team:', error);
            }
        }
    </script>
</body>
</html>
