// Sermon Video Page JavaScript
const API_URL = 'http://localhost:3000/api';

// Function to get URL parameters
function getUrlParameter(name) {
    name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
    const regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
    const results = regex.exec(location.search);
    return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
}

// Function to extract YouTube video ID from URL
function getYouTubeVideoId(url) {
    if (!url) return null;

    // Handle different YouTube URL formats
    const patterns = [
        /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/|youtube\.com\/v\/|youtube\.com\/watch\?.*&v=)([^#&?]*)/,
        /youtube\.com\/watch\?.*v=([^#&?]*)/,
        /youtu\.be\/([^#&?]*)/,
        /youtube\.com\/embed\/([^#&?]*)/,
        /youtube\.com\/v\/([^#&?]*)/
    ];

    for (const pattern of patterns) {
        const match = url.match(pattern);
        if (match && match[1] && match[1].length === 11) {
            return match[1];
        }
    }

    // Try to extract from query parameters
    try {
        const urlObj = new URL(url);
        const videoId = urlObj.searchParams.get('v');
        if (videoId && videoId.length === 11) {
            return videoId;
        }
    } catch (e) {
        console.log('URL parsing failed:', e);
    }

    return null;
}

// Function to convert YouTube URL to embed URL
function getYouTubeEmbedUrl(url) {
    const videoId = getYouTubeVideoId(url);
    if (videoId) {
        // Use nocookie domain for better privacy and embedding compatibility
        return `https://www.youtube-nocookie.com/embed/${videoId}?autoplay=0&rel=0&modestbranding=1&fs=1&cc_load_policy=0&iv_load_policy=3&enablejsapi=1`;
    }

    console.error('Could not extract video ID from URL:', url);
    return null;
}

// Function to format date
function formatDate(dateString) {
    const date = new Date(dateString);
    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    };
    return date.toLocaleDateString('en-US', options);
}

// Function to get YouTube thumbnail URL
function getYouTubeThumbnail(videoUrl) {
    const videoId = getYouTubeVideoId(videoUrl);
    if (videoId) {
        return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
    }
    return 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=600&q=80';
}

// Function to load sermon details
async function loadSermonDetails(sermonId) {
    try {
        console.log('🎬 Loading sermon details for ID:', sermonId);

        const response = await fetch(`${API_URL}/sermons/${sermonId}`);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const sermon = await response.json();
        console.log('📺 Sermon details loaded:', sermon);

        // Update page title
        document.title = `${sermon.title} - North Texas SDA Church`;

        // Update sermon details
        document.getElementById('sermon-title').textContent = sermon.title;
        document.getElementById('sermon-description').textContent = sermon.description;
        document.getElementById('sermon-preacher').textContent = sermon.preacher || 'Pastor';
        document.getElementById('sermon-date').textContent = formatDate(sermon.date);

        // Update video iframe
        const videoIframe = document.getElementById('sermon-video');
        const embedUrl = getYouTubeEmbedUrl(sermon.videoUrl);

        if (embedUrl) {
            videoIframe.src = embedUrl;
            console.log('✅ Video loaded:', embedUrl);
        } else {
            // Show error message in video container
            const videoContainer = document.querySelector('.video-wrapper');
            videoContainer.innerHTML = `
                <div style="display: flex; align-items: center; justify-content: center; height: 100%; background: #1a1a1a; color: #fff; text-align: center; padding: 40px;">
                    <div>
                        <i class="fas fa-exclamation-triangle" style="font-size: 48px; color: #ff6b35; margin-bottom: 20px;"></i>
                        <h3 style="margin-bottom: 15px;">Video Not Available</h3>
                        <p style="margin-bottom: 20px; color: #ccc;">Unable to load video from: ${sermon.videoUrl}</p>
                        <a href="${sermon.videoUrl}" target="_blank" style="color: #ff6b35; text-decoration: none; font-weight: bold;">
                            <i class="fas fa-external-link-alt" style="margin-right: 8px;"></i>
                            Watch on YouTube
                        </a>
                    </div>
                </div>
            `;
            console.error('❌ Could not create embed URL for:', sermon.videoUrl);
        }

        // Load related sermons
        loadRelatedSermons(sermonId);

    } catch (error) {
        console.error('❌ Error loading sermon details:', error);
        showErrorMessage();
    }
}

// Function to load related sermons
async function loadRelatedSermons(currentSermonId) {
    try {
        const response = await fetch(`${API_URL}/sermons`);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const allSermons = await response.json();

        // Filter out current sermon and get up to 3 related sermons
        const relatedSermons = allSermons
            .filter(sermon => sermon.id != currentSermonId)
            .slice(0, 3);

        const relatedContainer = document.getElementById('related-sermons');

        if (relatedSermons.length > 0) {
            relatedContainer.innerHTML = '';

            relatedSermons.forEach(sermon => {
                const thumbnailUrl = sermon.thumbnailUrl || getYouTubeThumbnail(sermon.videoUrl);

                const sermonCard = document.createElement('div');
                sermonCard.className = 'related-sermon-card';
                sermonCard.setAttribute('data-sermon-id', sermon.id);

                sermonCard.innerHTML = `
                    <div class="related-sermon-thumbnail">
                        <img src="${thumbnailUrl}" alt="${sermon.title}">
                        <div class="related-sermon-play">
                            <i class="fas fa-play"></i>
                        </div>
                    </div>
                    <div class="related-sermon-details">
                        <h4 class="related-sermon-title">${sermon.title}</h4>
                        <p class="related-sermon-preacher">${sermon.preacher || 'Pastor'}</p>
                        <p class="related-sermon-date">${formatDate(sermon.date)}</p>
                    </div>
                `;

                // Add click event listener
                sermonCard.addEventListener('click', function() {
                    const sermonId = this.getAttribute('data-sermon-id');
                    window.location.href = `sermon-video.html?id=${sermonId}`;
                });

                relatedContainer.appendChild(sermonCard);
            });
        } else {
            relatedContainer.innerHTML = `
                <div class="no-related-sermons">
                    <p>No related sermons available at this time.</p>
                </div>
            `;
        }

    } catch (error) {
        console.error('❌ Error loading related sermons:', error);
    }
}

// Function to show error message
function showErrorMessage() {
    document.getElementById('sermon-title').textContent = 'Sermon Not Found';
    document.getElementById('sermon-description').textContent = 'The requested sermon could not be found. Please check the URL or return to the sermons page.';
    document.getElementById('sermon-preacher').textContent = '';
    document.getElementById('sermon-date').textContent = '';

    // Hide video container
    const videoContainer = document.querySelector('.video-container');
    if (videoContainer) {
        videoContainer.style.display = 'none';
    }

    // Hide related sermons
    const relatedSermons = document.querySelector('.related-sermons');
    if (relatedSermons) {
        relatedSermons.style.display = 'none';
    }
}

// Function to initialize the page
function initializePage() {
    const sermonId = getUrlParameter('id');

    if (!sermonId) {
        console.error('❌ No sermon ID provided in URL');
        showErrorMessage();
        return;
    }

    console.log('🎬 Initializing sermon video page with ID:', sermonId);
    loadSermonDetails(sermonId);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎬 Sermon video page loaded');
    initializePage();
});

// Export functions for external use
window.SermonVideo = {
    loadSermonDetails,
    loadRelatedSermons,
    initializePage
};
