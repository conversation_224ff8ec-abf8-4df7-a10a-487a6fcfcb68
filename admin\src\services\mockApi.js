// Mock API service for testing the admin dashboard

// Demo user
const demoUser = {
  id: 1,
  name: 'Admin User',
  email: '<EMAIL>',
  role: 'admin'
};

// Demo events
const events = [
  {
    id: 1,
    title: 'Community Service Day',
    description: 'Join us as we serve our community through various outreach activities.',
    date: '2023-06-15',
    startTime: '09:00:00',
    endTime: '14:00:00',
    location: 'Church Grounds',
    featured: true,
    status: 'upcoming'
  },
  {
    id: 2,
    title: 'Youth Worship Night',
    description: 'A special evening of praise and worship led by our youth ministry.',
    date: '2023-06-22',
    startTime: '18:00:00',
    endTime: '20:30:00',
    location: 'Fellowship Hall',
    featured: false,
    status: 'upcoming'
  },
  {
    id: 3,
    title: 'Family Potluck',
    description: 'Bring your favorite dish and join us for fellowship after the service.',
    date: '2023-06-29',
    startTime: '13:00:00',
    endTime: '15:00:00',
    location: 'Church Fellowship Hall',
    featured: true,
    status: 'upcoming'
  }
];

// Demo blog posts
const posts = [
  {
    id: 1,
    title: 'Walking in Faith',
    content: 'Exploring how faith guides our daily walk with Christ.',
    excerpt: 'Faith is the substance of things hoped for, the evidence of things not seen.',
    author: 'Pastor <PERSON>',
    category: 'Devotional',
    publishedAt: '2023-06-10T10:00:00.000Z',
    status: 'published'
  },
  {
    id: 2,
    title: 'The Power of Prayer',
    content: 'Understanding the transformative power of prayer in our lives.',
    excerpt: 'Prayer is the key that unlocks all the storehouses of God\'s infinite grace and power.',
    author: 'Pastor Jane Doe',
    category: 'Prayer',
    publishedAt: '2023-06-03T10:00:00.000Z',
    status: 'published'
  }
];

// Demo gallery items
const gallery = [
  {
    id: 1,
    title: 'Sunday Worship',
    description: 'Moments from our Sunday worship service.',
    image: '/images/gallery/worship.jpg',
    category: 'Worship',
    featured: true
  },
  {
    id: 2,
    title: 'Youth Retreat',
    description: 'Our youth enjoying their annual retreat.',
    image: '/images/gallery/youth.jpg',
    category: 'Youth',
    featured: false
  },
  {
    id: 3,
    title: 'Community Outreach',
    description: 'Serving our community with love.',
    image: '/images/gallery/outreach.jpg',
    category: 'Outreach',
    featured: true
  }
];

// Demo settings
const settings = {
  churchName: {
    value: 'Faith Connect Church',
    type: 'text',
    group: 'general',
    description: 'Name of the church'
  },
  churchTagline: {
    value: 'A place where faith connects us to God and to each other',
    type: 'text',
    group: 'general',
    description: 'Church tagline'
  },
  address: {
    value: '123 Faith Avenue, Dallas, TX 75001',
    type: 'text',
    group: 'contact',
    description: 'Church address'
  },
  phone: {
    value: '(*************',
    type: 'text',
    group: 'contact',
    description: 'Church phone number'
  },
  email: {
    value: '<EMAIL>',
    type: 'text',
    group: 'contact',
    description: 'Church email address'
  }
};

// Mock API functions
const mockApi = {
  // Auth
  login: async (email, password) => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (email === '<EMAIL>' && password === 'password') {
          const token = 'mock-token-123456';
          localStorage.setItem('token', token);
          resolve({ token });
        } else {
          reject({ message: 'Invalid credentials' });
        }
      }, 500);
    });
  },
  
  getUser: async () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(demoUser);
      }, 500);
    });
  },
  
  // Events
  getEvents: async () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(events);
      }, 500);
    });
  },
  
  getEvent: async (id) => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const event = events.find(e => e.id === parseInt(id));
        if (event) {
          resolve(event);
        } else {
          reject({ message: 'Event not found' });
        }
      }, 500);
    });
  },
  
  createEvent: async (eventData) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const newEvent = {
          id: events.length + 1,
          ...eventData,
          createdAt: new Date().toISOString()
        };
        events.push(newEvent);
        resolve(newEvent);
      }, 500);
    });
  },
  
  updateEvent: async (id, eventData) => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const index = events.findIndex(e => e.id === parseInt(id));
        if (index !== -1) {
          events[index] = { ...events[index], ...eventData };
          resolve(events[index]);
        } else {
          reject({ message: 'Event not found' });
        }
      }, 500);
    });
  },
  
  deleteEvent: async (id) => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const index = events.findIndex(e => e.id === parseInt(id));
        if (index !== -1) {
          events.splice(index, 1);
          resolve({ message: 'Event deleted' });
        } else {
          reject({ message: 'Event not found' });
        }
      }, 500);
    });
  },
  
  // Posts
  getPosts: async () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(posts);
      }, 500);
    });
  },
  
  // Gallery
  getGallery: async () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(gallery);
      }, 500);
    });
  },
  
  // Settings
  getSettings: async () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(settings);
      }, 500);
    });
  }
};

export default mockApi;
