:root {
  --primary-color: #3b5998;
  --secondary-color: #8b9dc3;
  --accent-color: #dfe3ee;
  --dark-color: #333333;
  --light-color: #f7f7f7;
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --text-color: #333333;
  --text-light: #666666;
  --white: #ffffff;
  --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease-in-out;
}

body {
  margin: 0;
  font-family: 'Poppins', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8f9fa;
  color: var(--text-color);
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Loading Screen */
.loading-screen {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 1.5rem;
  color: var(--primary-color);
}

/* Login Page */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: var(--light-color);
}

.login-form {
  background-color: var(--white);
  padding: 2rem;
  border-radius: 10px;
  box-shadow: var(--box-shadow);
  width: 100%;
  max-width: 400px;
}

.login-logo {
  text-align: center;
  margin-bottom: 2rem;
}

.login-logo img {
  max-width: 150px;
}

/* Dashboard Layout */
.dashboard-container {
  display: flex;
  min-height: 100vh;
}

.sidebar {
  width: 250px;
  background-color: var(--primary-color);
  color: var(--white);
  padding: 1rem;
  position: fixed;
  height: 100vh;
  overflow-y: auto;
  transition: var(--transition);
}

.sidebar-collapsed {
  width: 70px;
}

.main-content {
  flex: 1;
  margin-left: 250px;
  padding: 1rem;
  transition: var(--transition);
}

.main-content-expanded {
  margin-left: 70px;
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 1rem;
}

.sidebar-logo {
  display: flex;
  align-items: center;
}

.sidebar-logo img {
  width: 40px;
  height: 40px;
  margin-right: 10px;
}

.sidebar-logo h2 {
  font-size: 1.2rem;
  margin: 0;
  white-space: nowrap;
}

.sidebar-toggle {
  background: transparent;
  border: none;
  color: var(--white);
  cursor: pointer;
  font-size: 1.2rem;
}

.nav-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  margin-bottom: 0.5rem;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: var(--white);
  text-decoration: none;
  border-radius: 5px;
  transition: var(--transition);
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.nav-link.active {
  background-color: var(--secondary-color);
}

.nav-icon {
  margin-right: 10px;
  font-size: 1.2rem;
  min-width: 20px;
  text-align: center;
}

.nav-text {
  white-space: nowrap;
}

.topbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: var(--white);
  border-bottom: 1px solid #eee;
  margin-bottom: 1rem;
}

.page-title {
  font-size: 1.5rem;
  margin: 0;
}

.user-dropdown {
  position: relative;
}

.user-dropdown-toggle {
  display: flex;
  align-items: center;
  background: transparent;
  border: none;
  cursor: pointer;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
}

.user-name {
  margin-right: 5px;
}

.user-dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: var(--white);
  border-radius: 5px;
  box-shadow: var(--box-shadow);
  padding: 0.5rem 0;
  min-width: 150px;
  z-index: 1000;
}

.dropdown-item {
  display: block;
  padding: 0.5rem 1rem;
  color: var(--text-color);
  text-decoration: none;
}

.dropdown-item:hover {
  background-color: var(--light-color);
}

/* Dashboard Cards */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background-color: var(--white);
  border-radius: 10px;
  padding: 1.5rem;
  box-shadow: var(--box-shadow);
}

.stat-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: var(--primary-color);
}

.stat-title {
  font-size: 1rem;
  color: var(--text-light);
  margin-bottom: 0.5rem;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0;
}

/* Forms */
.form-container {
  background-color: var(--white);
  border-radius: 10px;
  padding: 2rem;
  box-shadow: var(--box-shadow);
}

.form-title {
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-control {
  border-radius: 5px;
  padding: 0.75rem;
  border: 1px solid #ddd;
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(59, 89, 152, 0.25);
}

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

/* Tables */
.table-container {
  background-color: var(--white);
  border-radius: 10px;
  padding: 1.5rem;
  box-shadow: var(--box-shadow);
  overflow-x: auto;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.table-title {
  margin: 0;
}

.table-actions {
  display: flex;
  gap: 0.5rem;
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    z-index: 1000;
  }
  
  .sidebar.show {
    transform: translateX(0);
  }
  
  .main-content {
    margin-left: 0;
  }
  
  .dashboard-stats {
    grid-template-columns: 1fr;
  }
}
