/**
 * Simple test server to check if the API is working correctly
 */

const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');

// Initialize Express app
const app = express();

// Middleware
app.use(cors());
app.use(bodyParser.json());

// Sample data
const leadershipTeam = [
  {
    id: 1,
    name: 'Test Pastor',
    position: 'Senior Pastor',
    bio: 'This is a test pastor.',
    email: '<EMAIL>',
    imageUrl: 'https://via.placeholder.com/300x300?text=Test+Pastor',
    order: 1
  }
];

// Events data
const events = [
  {
    id: 1,
    title: 'Community Service Day',
    description: 'Join us as we serve our community through various outreach activities.',
    date: '2023-06-15',
    startTime: '09:00',
    endTime: '14:00',
    location: 'Church Grounds',
    featured: true,
    status: 'upcoming'
  },
  {
    id: 2,
    title: 'Youth Worship Night',
    description: 'A special evening of praise and worship led by our youth ministry.',
    date: '2023-06-22',
    startTime: '18:00',
    endTime: '20:30',
    location: 'Fellowship Hall',
    featured: false,
    status: 'upcoming'
  },
  {
    id: 3,
    title: 'Family Potluck',
    description: 'Bring your favorite dish and join us for fellowship after the service.',
    date: '2023-06-29',
    startTime: '13:00',
    endTime: '15:00',
    location: 'Church Fellowship Hall',
    featured: true,
    status: 'upcoming'
  }
];

// API Routes for Leadership Team
app.get('/api/leadership', (req, res) => {
  console.log('GET /api/leadership - Returning leadership team:', leadershipTeam);
  res.json(leadershipTeam);
});

app.get('/api/leadership/:id', (req, res) => {
  const leader = leadershipTeam.find(l => l.id === parseInt(req.params.id));
  if (!leader) return res.status(404).json({ message: 'Leader not found' });
  console.log('GET /api/leadership/:id - Returning leader:', leader);
  res.json(leader);
});

app.post('/api/leadership', (req, res) => {
  console.log('POST /api/leadership - Request body:', req.body);

  const newLeader = {
    id: leadershipTeam.length > 0 ? Math.max(...leadershipTeam.map(l => l.id)) + 1 : 1,
    ...req.body,
    createdAt: new Date().toISOString()
  };

  console.log('POST /api/leadership - Created new leader:', newLeader);
  leadershipTeam.push(newLeader);

  res.status(201).json(newLeader);
});

app.put('/api/leadership/:id', (req, res) => {
  const leaderIndex = leadershipTeam.findIndex(l => l.id === parseInt(req.params.id));
  if (leaderIndex === -1) return res.status(404).json({ message: 'Leader not found' });

  leadershipTeam[leaderIndex] = { ...leadershipTeam[leaderIndex], ...req.body };
  console.log('PUT /api/leadership/:id - Updated leader:', leadershipTeam[leaderIndex]);

  res.json(leadershipTeam[leaderIndex]);
});

app.delete('/api/leadership/:id', (req, res) => {
  const leaderIndex = leadershipTeam.findIndex(l => l.id === parseInt(req.params.id));
  if (leaderIndex === -1) return res.status(404).json({ message: 'Leader not found' });

  const deletedLeader = leadershipTeam[leaderIndex];
  leadershipTeam.splice(leaderIndex, 1);
  console.log('DELETE /api/leadership/:id - Deleted leader:', deletedLeader);

  res.json({ message: 'Leader deleted' });
});

// API Routes for Events
app.get('/api/events', (req, res) => {
  console.log('GET /api/events - Returning events:', events);
  res.json(events);
});

app.get('/api/events/:id', (req, res) => {
  const event = events.find(e => e.id === parseInt(req.params.id));
  if (!event) return res.status(404).json({ message: 'Event not found' });
  console.log('GET /api/events/:id - Returning event:', event);
  res.json(event);
});

app.post('/api/events', (req, res) => {
  console.log('POST /api/events - Request body:', req.body);

  const newEvent = {
    id: events.length > 0 ? Math.max(...events.map(e => e.id)) + 1 : 1,
    ...req.body,
    createdAt: new Date().toISOString()
  };

  console.log('POST /api/events - Created new event:', newEvent);
  events.push(newEvent);

  res.status(201).json(newEvent);
});

app.put('/api/events/:id', (req, res) => {
  const eventIndex = events.findIndex(e => e.id === parseInt(req.params.id));
  if (eventIndex === -1) return res.status(404).json({ message: 'Event not found' });

  events[eventIndex] = { ...events[eventIndex], ...req.body };
  console.log('PUT /api/events/:id - Updated event:', events[eventIndex]);

  res.json(events[eventIndex]);
});

app.delete('/api/events/:id', (req, res) => {
  const eventIndex = events.findIndex(e => e.id === parseInt(req.params.id));
  if (eventIndex === -1) return res.status(404).json({ message: 'Event not found' });

  const deletedEvent = events[eventIndex];
  events.splice(eventIndex, 1);
  console.log('DELETE /api/events/:id - Deleted event:', deletedEvent);

  res.json({ message: 'Event deleted' });
});

// Start the server
const PORT = 3001;
app.listen(PORT, () => {
  console.log(`Test server running on port ${PORT}`);
  console.log(`API endpoints:`);
  console.log(`- Leadership: http://localhost:${PORT}/api/leadership`);
  console.log(`- Events: http://localhost:${PORT}/api/events`);
});
