/* Dynamic Content Styles for North Texas SDA Church Website */

/* Leadership Team Styles */
.leadership-team {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 30px;
    max-width: 1320px;
    margin: 0 auto;
    padding: 0 20px;
}

.leadership-team-member {
    background-color: var(--bg-color);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.leadership-team-member:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.leadership-team-member-image {
    width: 100%;
    height: 300px;
    overflow: hidden;
}

.leadership-team-member-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.leadership-team-member:hover .leadership-team-member-image img {
    transform: scale(1.05);
}

.leadership-team-member-info {
    padding: 20px;
    text-align: center;
}

.leadership-team-member-name {
    font-family: var(--cmsmasters-secondary-font-family, "<PERSON>"), sans-serif;
    font-size: 22px;
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: 5px;
}

.leadership-team-member-position {
    font-family: var(--cmsmasters-text-font-family, "Lora"), sans-serif;
    font-size: 16px;
    color: var(--accent-color);
}

/* Events Styles */
.events-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    max-width: 1320px;
    margin: 0 auto;
    padding: 0 20px;
}

.event-item {
    display: flex;
    background-color: var(--bg-color);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.event-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.event-date {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: var(--accent-color);
    color: var(--bg-color);
    padding: 20px;
    min-width: 80px;
}

.event-day {
    font-family: var(--cmsmasters-primary-font-family, "Bebas Neue"), sans-serif;
    font-size: 36px;
    font-weight: 400;
    line-height: 1;
}

.event-month {
    font-family: var(--cmsmasters-accent-font-family, "Barlow Condensed"), sans-serif;
    font-size: 18px;
    font-weight: 600;
    text-transform: uppercase;
}

.event-content {
    padding: 20px;
    flex: 1;
}

.event-title {
    font-family: var(--cmsmasters-secondary-font-family, "Barlow"), sans-serif;
    font-size: 20px;
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: 10px;
}

.event-time, .event-location {
    font-family: var(--cmsmasters-text-font-family, "Lora"), sans-serif;
    font-size: 14px;
    color: var(--text-color);
    margin-bottom: 5px;
}

.event-link {
    display: inline-block;
    font-family: var(--cmsmasters-button-font-family, "Figtree"), sans-serif;
    font-size: 14px;
    font-weight: 700;
    color: var(--accent-color);
    text-decoration: none;
    margin-top: 10px;
    transition: color 0.3s ease;
}

.event-link:hover {
    color: var(--primary-color);
    text-decoration: underline;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .leadership-team {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .events-container {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 576px) {
    .leadership-team {
        grid-template-columns: 1fr;
    }
    
    .leadership-team-member-image {
        height: 250px;
    }
    
    .event-item {
        flex-direction: column;
    }
    
    .event-date {
        flex-direction: row;
        width: 100%;
        padding: 10px;
    }
    
    .event-day {
        font-size: 28px;
        margin-right: 5px;
    }
    
    .event-month {
        font-size: 16px;
    }
}
