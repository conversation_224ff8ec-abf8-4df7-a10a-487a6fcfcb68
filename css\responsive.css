/* 
* Faith Connect Church - Responsive Stylesheet
* Author: Augment Agent
* Version: 1.0
*/

/* Large Devices (Desktops) */
@media screen and (max-width: 1200px) {
  .container {
    padding: 0 30px;
  }
  
  .hero-title {
    font-size: 3rem;
  }
}

/* Medium Devices (Tablets) */
@media screen and (max-width: 992px) {
  .section {
    padding: 60px 0;
  }
  
  .section-title h2 {
    font-size: 2rem;
  }
  
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.2rem;
  }
  
  /* Navigation */
  .nav-menu {
    position: fixed;
    top: 0;
    right: -100%;
    width: 250px;
    height: 100vh;
    background-color: var(--white);
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    padding: 80px 20px 30px;
    transition: 0.4s;
    z-index: 1001;
  }
  
  .nav-menu.active {
    right: 0;
  }
  
  .nav-list {
    flex-direction: column;
    width: 100%;
  }
  
  .nav-item {
    margin: 15px 0;
  }
  
  .mobile-nav-toggle {
    display: block;
    z-index: 1002;
  }
  
  .mobile-nav-toggle.active i::before {
    content: '\f00d';
  }
  
  /* Close button inside mobile menu */
  .nav-close {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 1.5rem;
    cursor: pointer;
  }
}

/* Small Devices (Landscape Phones) */
@media screen and (max-width: 768px) {
  .header-container {
    padding: 15px 0;
  }
  
  .logo h1 {
    font-size: 1.5rem;
  }
  
  .logo img {
    height: 40px;
  }
  
  .hero {
    height: 80vh;
  }
  
  .hero-content {
    text-align: center;
  }
  
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
  }
  
  .hero-btns {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .btn {
    padding: 10px 25px;
  }
  
  .footer-container {
    grid-template-columns: 1fr;
  }
}

/* Extra Small Devices (Portrait Phones) */
@media screen and (max-width: 576px) {
  .section {
    padding: 50px 0;
  }
  
  .section-title h2 {
    font-size: 1.8rem;
  }
  
  .hero-title {
    font-size: 1.8rem;
  }
  
  .hero-subtitle {
    font-size: 0.9rem;
  }
  
  .btn {
    padding: 8px 20px;
    font-size: 0.9rem;
  }
}
