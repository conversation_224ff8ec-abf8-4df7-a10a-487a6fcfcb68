<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Member Add Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .test-section { border: 1px solid #ddd; padding: 15px; margin: 15px 0; border-radius: 8px; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Dashboard Member Add Test</h1>
    <p>This page tests the complete flow of adding a member through the dashboard and verifying it appears in the table.</p>
    
    <div class="test-section">
        <h3>🔧 Step 1: Test API Connection</h3>
        <button onclick="testAPI()">Test Members API</button>
        <div id="api-status"></div>
    </div>

    <div class="test-section">
        <h3>📝 Step 2: Add Test Member via API</h3>
        <button onclick="addTestMember()">Add Test Member</button>
        <div id="add-status"></div>
    </div>

    <div class="test-section">
        <h3>🔍 Step 3: Verify Member in List</h3>
        <button onclick="verifyMember()">Check if Member Exists</button>
        <div id="verify-status"></div>
        <div id="members-list"></div>
    </div>

    <div class="test-section">
        <h3>🎯 Step 4: Test Dashboard Integration</h3>
        <button onclick="openDashboard()">Open Dashboard</button>
        <button onclick="simulateDashboardFlow()">Simulate Dashboard Flow</button>
        <div id="dashboard-status"></div>
    </div>

    <div class="test-section">
        <h3>🧹 Step 5: Cleanup</h3>
        <button onclick="deleteTestMembers()">Delete Test Members</button>
        <div id="cleanup-status"></div>
    </div>

    <script>
        const API_URL = 'http://localhost:3000/api';
        let testMemberId = null;

        async function testAPI() {
            const statusDiv = document.getElementById('api-status');
            statusDiv.innerHTML = '<div class="status info">Testing API connection...</div>';
            
            try {
                const response = await fetch(`${API_URL}/members`);
                if (response.ok) {
                    const members = await response.json();
                    statusDiv.innerHTML = `<div class="status success">✅ API Working! Found ${members.length} members</div>`;
                    return true;
                } else {
                    statusDiv.innerHTML = `<div class="status error">❌ API Error: ${response.status}</div>`;
                    return false;
                }
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ Connection Error: ${error.message}</div>`;
                return false;
            }
        }

        async function addTestMember() {
            const statusDiv = document.getElementById('add-status');
            statusDiv.innerHTML = '<div class="status info">Adding test member...</div>';
            
            const testMember = {
                name: 'Dashboard Test Member ' + Date.now(),
                phone: '(*************',
                address: '456 Dashboard St, Test City, TX 12345',
                birthdate: '1985-05-15',
                memberSince: '2021-01-01',
                ministry: 'Prayer Team',
                status: 'active',
                notes: 'Test member added via dashboard test'
            };
            
            try {
                const response = await fetch(`${API_URL}/members`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testMember)
                });
                
                if (response.ok) {
                    const newMember = await response.json();
                    testMemberId = newMember.id;
                    statusDiv.innerHTML = `<div class="status success">✅ Test member added! ID: ${newMember.id}</div>`;
                    console.log('Test member added:', newMember);
                    return newMember;
                } else {
                    const errorText = await response.text();
                    statusDiv.innerHTML = `<div class="status error">❌ Failed to add member: ${response.status} - ${errorText}</div>`;
                    return null;
                }
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ Error: ${error.message}</div>`;
                return null;
            }
        }

        async function verifyMember() {
            const statusDiv = document.getElementById('verify-status');
            const listDiv = document.getElementById('members-list');
            
            statusDiv.innerHTML = '<div class="status info">Checking members list...</div>';
            
            try {
                const response = await fetch(`${API_URL}/members`);
                if (response.ok) {
                    const members = await response.json();
                    const testMembers = members.filter(m => m.name.includes('Dashboard Test Member') || m.notes?.includes('dashboard test'));
                    
                    if (testMembers.length > 0) {
                        statusDiv.innerHTML = `<div class="status success">✅ Found ${testMembers.length} test member(s) in the list!</div>`;
                        listDiv.innerHTML = `<pre>${JSON.stringify(testMembers, null, 2)}</pre>`;
                    } else {
                        statusDiv.innerHTML = '<div class="status error">❌ No test members found in the list</div>';
                        listDiv.innerHTML = `<pre>All members:\n${JSON.stringify(members, null, 2)}</pre>`;
                    }
                } else {
                    statusDiv.innerHTML = `<div class="status error">❌ Failed to fetch members: ${response.status}</div>`;
                }
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ Error: ${error.message}</div>`;
            }
        }

        function openDashboard() {
            window.open('admin-dashboard.html', '_blank');
        }

        async function simulateDashboardFlow() {
            const statusDiv = document.getElementById('dashboard-status');
            statusDiv.innerHTML = '<div class="status info">Simulating dashboard member add flow...</div>';
            
            // Step 1: Test API
            const apiWorking = await testAPI();
            if (!apiWorking) {
                statusDiv.innerHTML = '<div class="status error">❌ API not working, cannot simulate dashboard flow</div>';
                return;
            }
            
            // Step 2: Add member
            const member = await addTestMember();
            if (!member) {
                statusDiv.innerHTML = '<div class="status error">❌ Failed to add member, cannot complete simulation</div>';
                return;
            }
            
            // Step 3: Wait a moment (simulate dashboard delay)
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Step 4: Verify member appears
            await verifyMember();
            
            statusDiv.innerHTML = '<div class="status success">✅ Dashboard flow simulation complete! Check the results above.</div>';
        }

        async function deleteTestMembers() {
            const statusDiv = document.getElementById('cleanup-status');
            statusDiv.innerHTML = '<div class="status info">Deleting test members...</div>';
            
            try {
                const response = await fetch(`${API_URL}/members`);
                if (!response.ok) {
                    throw new Error('Failed to fetch members');
                }
                
                const members = await response.json();
                const testMembers = members.filter(m => 
                    m.name.includes('Test Member') || 
                    m.notes?.includes('test') ||
                    m.notes?.includes('dashboard test')
                );
                
                if (testMembers.length === 0) {
                    statusDiv.innerHTML = '<div class="status info">No test members found to delete.</div>';
                    return;
                }
                
                let deletedCount = 0;
                for (const member of testMembers) {
                    const deleteResponse = await fetch(`${API_URL}/members/${member.id}`, {
                        method: 'DELETE'
                    });
                    if (deleteResponse.ok) {
                        deletedCount++;
                    }
                }
                
                statusDiv.innerHTML = `<div class="status success">✅ Deleted ${deletedCount} test members</div>`;
                
                // Refresh verification
                setTimeout(verifyMember, 1000);
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ Error: ${error.message}</div>`;
            }
        }

        // Auto-test on page load
        window.addEventListener('load', function() {
            setTimeout(testAPI, 500);
        });
    </script>
</body>
</html>
