/* <PERSON>mon Video Page Styles */

.sermon-video-page {
    background: url('../images/header-bg.png');
    background-size: cover;
    color: #fff;
}

.sermon-video-section {
    padding: 120px 0 80px;
    min-height: 100vh;
}

/* Back Button */
.back-button-container {
    margin-bottom: 40px;
}

.back-button {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    color: #fff;
    text-decoration: none;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 12px 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
}

.back-button:hover {
    color: #ff6b35;
    border-color: #ff6b35;
    background: rgba(255, 107, 53, 0.1);
    transform: translateX(-5px);
}

.back-button i {
    font-size: 14px;
}

/* <PERSON><PERSON> */
.sermon-header {
    text-align: center;
    margin-bottom: 60px;
    max-width: 1300px;
    margin-left: auto;
    margin-right: auto;
}

.sermon-category-badge {
    display: inline-block;
    background: #ff6b35;
    color: #fff;
    padding: 8px 20px;
    border-radius: 25px;
    font-size: 12px;
    font-weight: 700;
    letter-spacing: 1px;
    text-transform: uppercase;
    margin-bottom: 20px;
}

.sermon-title {
    width: 100%;
    font-family: 'Bebas Neue', cursive;
    font-size: clamp(5rem, 10vw, 9rem);
    font-weight: 400;
    line-height: 0.8;
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 2px;
    color: #fff;
}

.sermon-description {
    width: 800px;
    font-size: 18px;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 30px;
    margin-left: auto;
    margin-right: auto;
}

.sermon-meta {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
}

.sermon-meta-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
}

.sermon-meta-item i {
    color: #ff6b35;
    font-size: 16px;
}

/* Video Container */
.video-container {
    margin-bottom: 80px;
}

.video-wrapper {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    background: #1a1a1a;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.video-wrapper iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
}

/* Related Sermons */
.related-sermons {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 60px;
}

.related-title {
    font-family: 'Bebas Neue', cursive;
    font-size: 2rem;
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 40px;
    text-align: center;
}

.related-sermons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.related-sermon-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.related-sermon-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.08);
    border-color: #ff6b35;
    box-shadow: 0 10px 30px rgba(255, 107, 53, 0.2);
}

.related-sermon-thumbnail {
    position: relative;
    width: 100%;
    height: 180px;
    overflow: hidden;
}

.related-sermon-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.related-sermon-card:hover .related-sermon-thumbnail img {
    transform: scale(1.05);
}

.related-sermon-play {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50px;
    height: 50px;
    background: rgba(255, 107, 53, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 18px;
    transition: all 0.3s ease;
}

.related-sermon-card:hover .related-sermon-play {
    background: #ff6b35;
    transform: translate(-50%, -50%) scale(1.1);
}

.related-sermon-details {
    padding: 20px;
}

.related-sermon-title {
    font-size: 16px;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 8px;
    color: #fff;
}

.related-sermon-preacher {
    font-size: 14px;
    color: #ff6b35;
    margin-bottom: 5px;
}

.related-sermon-date {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #ff6b35;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .sermon-video-section {
        padding: 100px 0 60px;
    }
    
    .sermon-header {
        margin-bottom: 40px;
    }
    
    .sermon-meta {
        gap: 20px;
    }
    
    .video-container {
        margin-bottom: 60px;
    }
    
    .related-sermons {
        padding-top: 40px;
    }
    
    .related-sermons-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}

@media (max-width: 480px) {
    .sermon-title {
        font-size: 2rem;
    }
    
    .sermon-description {
        font-size: 16px;
    }
    
    .sermon-meta {
        flex-direction: column;
        gap: 15px;
    }
}
