# North Texas SDA Church Management Backend

This is the backend API for the North Texas SDA Church Management System.

## Features

- **Authentication**: JWT-based authentication
- **Events Management**: CRUD operations for church events
- **Sermons Management**: CRUD operations for video sermons
- **Leadership Team**: CRUD operations for church leadership
- **Members Directory**: CRUD operations for church members
- **Prayer Requests**: CRUD operations for prayer requests

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login

### Events
- `GET /api/events` - Get all events
- `GET /api/events/:id` - Get single event
- `POST /api/events` - Create new event (auth required)
- `PUT /api/events/:id` - Update event (auth required)
- `DELETE /api/events/:id` - Delete event (auth required)

### Sermons
- `GET /api/sermons` - Get all sermons
- `GET /api/sermons/:id` - Get single sermon
- `POST /api/sermons` - Create new sermon (auth required)
- `PUT /api/sermons/:id` - Update sermon (auth required)
- `DELETE /api/sermons/:id` - Delete sermon (auth required)

### Leadership
- `GET /api/leadership` - Get all leaders
- `GET /api/leadership/:id` - Get single leader
- `POST /api/leadership` - Create new leader (auth required)
- `PUT /api/leadership/:id` - Update leader (auth required)
- `DELETE /api/leadership/:id` - Delete leader (auth required)

### Members
- `GET /api/members` - Get all members
- `GET /api/members/:id` - Get single member
- `POST /api/members` - Create new member (auth required)
- `PUT /api/members/:id` - Update member (auth required)
- `DELETE /api/members/:id` - Delete member (auth required)

### Prayer Requests
- `GET /api/prayer-requests` - Get all prayer requests
- `GET /api/prayer-requests/:id` - Get single prayer request
- `POST /api/prayer-requests` - Submit new prayer request (public)
- `PUT /api/prayer-requests/:id` - Update prayer request (auth required)
- `DELETE /api/prayer-requests/:id` - Delete prayer request (auth required)

## Default Admin Credentials

- **Email**: <EMAIL>
- **Password**: admin123

⚠️ **Important**: Change these credentials after first login!

## Environment Variables

Copy `.env.example` to `.env` and fill in your values:

```env
DB_HOST=your_database_host
DB_USER=your_database_user
DB_PASSWORD=your_database_password
DB_NAME=your_database_name
DB_PORT=3306
JWT_SECRET=your_super_secret_jwt_key_here
PORT=3000
NODE_ENV=production
FRONTEND_URL=https://your-netlify-site.netlify.app
```

## Local Development

1. Install dependencies:
```bash
npm install
```

2. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your database credentials
```

3. Start the server:
```bash
npm run dev
```

## Deployment

This backend is designed to be deployed on Railway with a PlanetScale MySQL database.

### Railway Deployment

1. Push this code to GitHub
2. Connect Railway to your GitHub repository
3. Set environment variables in Railway dashboard
4. Deploy automatically

### Database Setup

The server will automatically create all required tables on first run.

## Database Schema

The application uses the following tables:
- `users` - Admin users
- `events` - Church events
- `sermons` - Video sermons
- `leadership` - Church leadership team
- `members` - Church members directory
- `prayer_requests` - Prayer requests from members
