const express = require('express');
const router = express.Router();
const { check, validationResult } = require('express-validator');
const Gallery = require('../models/Gallery');
const { auth, editor } = require('../middleware/auth');
const multer = require('multer');
const path = require('path');
const { Op } = require('sequelize');

// Set up multer storage
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, path.join(__dirname, '../../images/gallery'));
  },
  filename: (req, file, cb) => {
    cb(null, `gallery-${Date.now()}${path.extname(file.originalname)}`);
  }
});

// Initialize upload
const upload = multer({
  storage,
  limits: { fileSize: 2000000 }, // 2MB
  fileFilter: (req, file, cb) => {
    // Check file types
    const filetypes = /jpeg|jpg|png|gif/;
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = filetypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb('Error: Images only (jpeg, jpg, png, gif)!');
    }
  }
});

// @route   GET api/gallery
// @desc    Get all gallery images
// @access  Public
router.get('/', async (req, res) => {
  try {
    const gallery = await Gallery.findAll({
      order: [['order', 'ASC'], ['createdAt', 'DESC']]
    });
    res.json(gallery);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

// @route   GET api/gallery/categories
// @desc    Get all gallery categories
// @access  Public
router.get('/categories', async (req, res) => {
  try {
    const categories = await Gallery.findAll({
      attributes: ['category'],
      group: ['category'],
      where: {
        category: {
          [Op.not]: null
        }
      }
    });

    res.json(categories.map(item => item.category));
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

// @route   GET api/gallery/:id
// @desc    Get gallery image by ID
// @access  Public
router.get('/:id', async (req, res) => {
  try {
    const galleryItem = await Gallery.findByPk(req.params.id);

    if (!galleryItem) {
      return res.status(404).json({ message: 'Gallery item not found' });
    }

    res.json(galleryItem);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

// @route   POST api/gallery
// @desc    Upload a new gallery image
// @access  Private (Editor)
router.post(
  '/',
  [
    auth,
    editor,
    upload.single('image'),
    [
      check('title', 'Title is required').not().isEmpty()
    ]
  ],
  async (req, res) => {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      // Check if image was uploaded
      if (!req.file) {
        return res.status(400).json({ message: 'Please upload an image' });
      }

      // Create new gallery item
      const newGalleryItem = await Gallery.create({
        title: req.body.title,
        description: req.body.description,
        image: `/images/gallery/${req.file.filename}`,
        category: req.body.category,
        featured: req.body.featured === 'true',
        order: req.body.order || 0
      });

      res.json(newGalleryItem);
    } catch (err) {
      console.error(err.message);
      res.status(500).send('Server error');
    }
  }
);

// @route   PUT api/gallery/:id
// @desc    Update a gallery item
// @access  Private (Editor)
router.put(
  '/:id',
  [
    auth,
    editor,
    upload.single('image'),
    [
      check('title', 'Title is required').not().isEmpty()
    ]
  ],
  async (req, res) => {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      // Find gallery item by ID
      let galleryItem = await Gallery.findByPk(req.params.id);

      if (!galleryItem) {
        return res.status(404).json({ message: 'Gallery item not found' });
      }

      // Update gallery item
      galleryItem.title = req.body.title;
      galleryItem.description = req.body.description;
      if (req.file) {
        galleryItem.image = `/images/gallery/${req.file.filename}`;
      }
      galleryItem.category = req.body.category;
      galleryItem.featured = req.body.featured === 'true';
      galleryItem.order = req.body.order || galleryItem.order;

      await galleryItem.save();

      res.json(galleryItem);
    } catch (err) {
      console.error(err.message);
      res.status(500).send('Server error');
    }
  }
);

// @route   DELETE api/gallery/:id
// @desc    Delete a gallery item
// @access  Private (Editor)
router.delete('/:id', [auth, editor], async (req, res) => {
  try {
    // Find gallery item by ID
    const galleryItem = await Gallery.findByPk(req.params.id);

    if (!galleryItem) {
      return res.status(404).json({ message: 'Gallery item not found' });
    }

    // Delete gallery item
    await galleryItem.destroy();

    res.json({ message: 'Gallery item removed' });
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

module.exports = router;
