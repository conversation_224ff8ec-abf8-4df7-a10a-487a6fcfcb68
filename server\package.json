{"name": "faith-connect-server", "version": "1.0.0", "description": "Backend server for Faith Connect Church website", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "Augment Agent", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.1.4", "express": "^4.18.2", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.3.5", "sequelize": "^6.32.0", "sqlite3": "^5.1.7"}, "devDependencies": {"nodemon": "^2.0.22"}}