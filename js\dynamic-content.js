// Dynamic Content Loader for North Texas SDA Church Website

// Function to load team members dynamically
function loadTeamMembers(members) {
    const teamContainer = document.querySelector('.leadership-team');
    
    if (teamContainer) {
        // Clear existing content
        teamContainer.innerHTML = '';
        
        // Add each team member
        members.forEach(member => {
            const memberElement = document.createElement('div');
            memberElement.className = 'leadership-team-member';
            
            memberElement.innerHTML = `
                <div class="leadership-team-member-image">
                    <img src="${member.image}" alt="${member.name}">
                </div>
                <div class="leadership-team-member-info">
                    <h3 class="leadership-team-member-name">${member.name}</h3>
                    <p class="leadership-team-member-position">${member.position}</p>
                </div>
            `;
            
            teamContainer.appendChild(memberElement);
        });
    }
}

// Function to load events dynamically
function loadEvents(events) {
    const eventsContainer = document.querySelector('.events-container');
    
    if (eventsContainer) {
        // Clear existing content
        eventsContainer.innerHTML = '';
        
        // Add each event
        events.forEach(event => {
            const eventElement = document.createElement('div');
            eventElement.className = 'event-item';
            
            eventElement.innerHTML = `
                <div class="event-date">
                    <span class="event-day">${event.day}</span>
                    <span class="event-month">${event.month}</span>
                </div>
                <div class="event-content">
                    <h3 class="event-title">${event.title}</h3>
                    <p class="event-time">${event.time}</p>
                    <p class="event-location">${event.location}</p>
                    <a href="${event.link}" class="event-link">Learn More</a>
                </div>
            `;
            
            eventsContainer.appendChild(eventElement);
        });
    }
}

// Example function to fetch team members from API
async function fetchTeamMembers() {
    try {
        const response = await fetch('/api/team-members');
        if (!response.ok) {
            throw new Error('Failed to fetch team members');
        }
        const data = await response.json();
        loadTeamMembers(data);
    } catch (error) {
        console.error('Error fetching team members:', error);
        // Load sample data if API fails
        loadSampleTeamMembers();
    }
}

// Example function to fetch events from API
async function fetchEvents() {
    try {
        const response = await fetch('/api/events');
        if (!response.ok) {
            throw new Error('Failed to fetch events');
        }
        const data = await response.json();
        loadEvents(data);
    } catch (error) {
        console.error('Error fetching events:', error);
        // Load sample data if API fails
        loadSampleEvents();
    }
}

// Sample team members data for testing
function loadSampleTeamMembers() {
    const sampleMembers = [
        {
            name: 'Pastor John Smith',
            position: 'Senior Pastor',
            image: './assets/images/team/pastor1.jpg'
        },
        {
            name: 'Sarah Johnson',
            position: 'Associate Pastor',
            image: './assets/images/team/pastor2.jpg'
        },
        {
            name: 'Michael Brown',
            position: 'Youth Pastor',
            image: './assets/images/team/pastor3.jpg'
        },
        {
            name: 'Emily Davis',
            position: 'Worship Leader',
            image: './assets/images/team/pastor4.jpg'
        }
    ];
    
    loadTeamMembers(sampleMembers);
}

// Sample events data for testing
function loadSampleEvents() {
    const sampleEvents = [
        {
            day: '15',
            month: 'JUN',
            title: 'Sunday Worship Service',
            time: '9:00 AM - 11:00 AM',
            location: 'Main Sanctuary',
            link: '#'
        },
        {
            day: '18',
            month: 'JUN',
            title: 'Bible Study Group',
            time: '7:00 PM - 8:30 PM',
            location: 'Fellowship Hall',
            link: '#'
        },
        {
            day: '20',
            month: 'JUN',
            title: 'Youth Group Meeting',
            time: '6:00 PM - 8:00 PM',
            location: 'Youth Center',
            link: '#'
        },
        {
            day: '25',
            month: 'JUN',
            title: 'Community Outreach',
            time: '10:00 AM - 2:00 PM',
            location: 'Downtown Area',
            link: '#'
        }
    ];
    
    loadEvents(sampleEvents);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Try to fetch data from API, fallback to sample data
    try {
        fetchTeamMembers();
        fetchEvents();
    } catch (error) {
        console.error('Error initializing dynamic content:', error);
        loadSampleTeamMembers();
        loadSampleEvents();
    }
});
