/*
* North Texas SDA Church - Admin Dashboard Stylesheet
* Author: Augment Agent
* Version: 1.0
*/

:root {
    --primary-color: #f7b731;
    --secondary-color: #ff6b6b;
    --accent-color: #4ecdc4;
    --dark-color: #1d1d1d;
    --light-color: #f9f9f9;
    --white: #ffffffb2;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
    --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    --transition: all 0.3s ease;
    --border-radius: 10px;
    --gradient-primary: linear-gradient(135deg, #e3e5e6 0%, #F95C28 100%);
    --gradient-secondary: linear-gradient(135deg, #f6d365 0%, #fda085 100%);
    --font-primary: 'Inter', sans-serif;
    --font-secondary: 'Poppins', sans-serif;
}

body {
    font-family: var(--font-secondary);
    background: #e3e5e6;
    background-attachment: fixed;
    margin: 0;
    padding: 0;
    color: var(--gray-800);
}

/* Dashboard Layout */
.dashboard-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    width: 250px;
    background-color: var(--white);
    box-shadow: var(--box-shadow);
    padding: 20px 0;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 100;
    transition: var(--transition);
}

.sidebar-header {
    padding: 0 20px 20px;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar-logo {
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-weight: 700;
    font-size: 20px;
    margin-right: 10px;
}

.sidebar-logo img{
    width:100%;
    height:100%;
}

.sidebar-title {
    font-family: 'Oswald';
    font-size: 24px;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
    text-transform: uppercase;
}

.circle{
    width: 250px;
    height: 250px;
    background-color: #f7b731;
    filter: blur(100px);
    position: fixed;
    top: 40%;
    right: 0;

}

.nav-menu {
    list-style: none;
    padding: 20px 0;
    margin: 0;
}

.nav-item {
    margin-bottom: 5px;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--gray-700);
    text-decoration: none;
    transition: var(--transition);
    border-left: 3px solid transparent;
}

.nav-link:hover, .nav-link.active {
    background-color: var(--gray-100);
    color: var(--primary-color);
    border-left-color: var(--primary-color);
}

.nav-link i {
    margin-right: 10px;
    font-size: 18px;
    width: 20px;
    text-align: center;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: 250px;
    padding: 20px;
    transition: var(--transition);
}

/* Top Bar */
.topbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    background-color: var(--white);
    padding: 15px 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.page-title {
    font-family: var(--font-secondary);
    font-size: 24px;
    font-weight: 600;
    margin: 0;
    color: var(--gray-800);
}

.user-info {
    display: flex;
    align-items: center;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-weight: 600;
    margin-right: 10px;
}

/* Dashboard Cards */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: var(--white);
    border-radius: var(--border-radius);
    border: 1px solid #fff;
    padding: 20px;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 24px;
    margin-bottom: 15px;
}

.bg-primary {
    background-color: var(--primary-color);
}

.bg-success {
    background-color: #28a745;
}

.bg-warning {
    background-color: #ffc107;
}

.bg-info {
    background-color: #17a2b8;
}

.bg-danger {
    background-color: #dc3545;
}

.bg-primary-light {
    background-color: rgba(249, 92, 40, 0.2);
}

.bg-success-light {
    background-color: rgba(40, 167, 69, 0.2);
}

.bg-warning-light {
    background-color: rgba(255, 193, 7, 0.2);
}

.bg-info-light {
    background-color: rgba(23, 162, 184, 0.2);
}

.bg-danger-light {
    background-color: rgba(220, 53, 69, 0.2);
}

.text-primary {
    color: var(--primary-color);
}

.text-success {
    color: #28a745;
}

.text-warning {
    color: #ffc107;
}

.text-info {
    color: #17a2b8;
}

.text-danger {
    color: #dc3545;
}

.stat-title {
    font-size: 16px;
    color: var(--gray-600);
    margin: 0 0 5px;
}

.stat-value {
    font-size: 28px;
    font-weight: 700;
    color: var(--gray-800);
    margin: 0 0 10px;
}

.stat-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 5px;
}

.stat-label {
    font-size: 13px;
    color: var(--gray-500);
}

/* Dashboard Cards */
.dashboard-card {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border: none;
    height: 100%;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.dashboard-card .card-header {
    background-color: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 15px 20px;
}

.dashboard-card .card-body {
    padding: 20px;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.event-date {
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-align: center;
}

.event-month {
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.event-day {
    font-size: 18px;
    font-weight: 700;
}

/* Analytics Section */
.analytics-section {
    background-color: var(--white);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--box-shadow);
    margin-bottom: 30px;
}

.analytics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.analytics-title {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
}

.analytics-tabs {
    display: flex;
    gap: 15px;
}

.analytics-tab {
    padding: 8px 15px;
    border-radius: 20px;
    background-color: var(--gray-100);
    color: var(--gray-700);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.analytics-tab.active {
    background-color: var(--primary-color);
    color: var(--white);
}

/* Content Sections */
.content-section {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.content-section.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Tables */
.table-container {
    background-color: var(--white);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--box-shadow);
    margin: 15px 0;
    position: relative;
    z-index: 1;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.table-title {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
}

.table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.table th {
    font-weight: 600;
    color: var(--gray-700);
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--gray-200);
}

.table td {
    padding: 12px 15px;
    border-bottom: 1px solid var(--gray-200);
    color: var(--gray-800);
}

.table tr:last-child td {
    border-bottom: none;
}

.table tr:hover td {
    background-color: var(--gray-100);
}

/* Buttons */
.btn {
    padding: 8px 16px;
    border-radius: 5px;
    font-weight: 500;
    transition: var(--transition);
    border: none;
    cursor: pointer;
}

.btn-primary {
    background-color: var(--primary-color);
    color: #fff;
}

.btn-primary:hover {
    background-color: #e5a82c;
}

.btn-secondary {
    background-color: var(--gray-200);
    color: var(--gray-800);
}

.btn-secondary:hover {
    background-color: var(--gray-300);
}

/* Member Directory Styles */
.members-search {
    margin-bottom: 20px;
}

.member-avatar {
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-weight: 600;
    font-size: 14px;
}

.member-profile-picture {
    display: flex;
    align-items: center;
    justify-content: center;
}

.member-profile-img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--gray-200);
}

.member-address {
    max-width: 200px;
    word-wrap: break-word;
}

.members-pagination {
    margin-top: 20px;
}

.pagination .page-link {
    color: var(--gray-700);
    border-color: var(--gray-300);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
}

.pagination .page-link:hover {
    background-color: var(--gray-200);
    color: var(--gray-800);
}

.scheduled-payments {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.payment-card {
    background-color: var(--gray-100);
    border-radius: var(--border-radius);
    padding: 15px;
    position: relative;
}

.payment-card h4 {
    font-size: 16px;
    margin: 0;
    font-weight: 600;
}

.payment-card p {
    font-size: 14px;
    color: var(--gray-600);
    margin: 5px 0 0;
}

.payment-amount {
    position: absolute;
    top: 15px;
    right: 15px;
    font-weight: 600;
    color: var(--gray-800);
}

.send-money-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.send-money-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.send-money-item .user-avatar {
    width: 40px;
    height: 40px;
    margin-right: 0;
}

.send-money-item .user-name {
    font-weight: 500;
}

.savings-streak {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 15px;
}

.streak-day {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: var(--gray-300);
}

.streak-day.active {
    background-color: var(--secondary-color);
}

.transaction-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-700);
}

.transaction-details h5 {
    font-size: 16px;
    margin: 0;
    font-weight: 500;
}

.transaction-details small {
    color: var(--gray-600);
}

/* Responsive */
@media (max-width: 992px) {
    .sidebar {
        width: 70px;
    }

    .sidebar-title, .nav-link span {
        display: none;
    }

    .nav-link i {
        margin-right: 0;
    }

    .main-content {
        margin-left: 70px;
    }
}

/* Image Upload Styles */
.image-upload-container {
    border: 2px dashed var(--gray-300);
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    transition: var(--transition);
    background-color: var(--gray-50);
}

.image-drop-zone {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
}

.image-drop-zone:hover {
    border-color: var(--primary-color);
    background-color: rgba(249, 92, 40, 0.05);
}

.image-drop-zone.dragover {
    border-color: var(--primary-color);
    background-color: rgba(249, 92, 40, 0.1);
    transform: scale(1.02);
}

.drop-zone-content h5 {
    color: var(--gray-700);
    margin-bottom: 10px;
}

.drop-zone-content p {
    color: var(--gray-500);
    margin-bottom: 15px;
}

.image-preview {
    position: relative;
    max-width: 300px;
    margin: 0 auto;
}

.image-preview img {
    max-height: 200px;
    border: 2px solid var(--gray-200);
}

.remove-image-btn {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}

/* Mobile Toggle Button */
.mobile-toggle {
    background: none;
    border: none;
    color: var(--gray-700);
    font-size: 20px;
    padding: 8px;
    border-radius: 5px;
    cursor: pointer;
    transition: var(--transition);
    margin-right: 15px;
}

.mobile-toggle:hover {
    background-color: var(--gray-100);
    color: var(--primary-color);
}

.mobile-toggle:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(249, 92, 40, 0.2);
}

/* Form Styles */
.form-container {
    background-color: var(--white);
    padding: 20px;
    border-radius: var(--border-radius);
}

.form-label {
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 8px;
}

.form-control {
    border: 1px solid var(--gray-300);
    border-radius: 5px;
    padding: 10px 15px;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(249, 92, 40, 0.2);
    outline: none;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Mobile Responsiveness */
@media (max-width: 1200px) {
    .main-content {
        margin-left: 0;
        padding: 20px;
    }

    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
        z-index: 1000;
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .header {
        padding-left: 20px;
    }

    .mobile-toggle {
        display: block;
    }
}

@media (max-width: 992px) {
    .dashboard-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .table-container {
        overflow-x: auto;
    }

    .table {
        min-width: 600px;
    }

    .form-container .row {
        margin: 0;
    }

    .form-container .col-md-6 {
        padding: 0 0 15px 0;
    }
}

@media (max-width: 768px) {
    .dashboard-stats {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .analytics-tabs {
        flex-wrap: wrap;
        gap: 10px;
    }

    .analytics-tabs .nav-link {
        padding: 8px 12px;
        font-size: 14px;
    }

    .scheduled-payments {
        margin-top: 20px;
    }

    .image-drop-zone {
        min-height: 150px;
        padding: 15px;
    }

    .drop-zone-content h5 {
        font-size: 16px;
    }

    .drop-zone-content p {
        font-size: 14px;
    }

    .table-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .table-title {
        text-align: center;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }

    .header-title {
        font-size: 20px;
    }

    .stat-card {
        padding: 15px;
    }

    .stat-number {
        font-size: 24px;
    }

    .recent-activity-item {
        padding: 10px;
    }

    .form-container {
        padding: 15px;
    }

    .modal-content {
        margin: 10px;
        width: calc(100% - 20px);
    }
}

@media (max-width: 576px) {
    .main-content {
        padding: 15px;
    }

    .dashboard-stats {
        gap: 10px;
    }

    .stat-card {
        padding: 12px;
    }

    .stat-number {
        font-size: 20px;
    }

    .stat-label {
        font-size: 12px;
    }

    .table-container {
        padding: 10px;
    }

    .form-container {
        padding: 10px;
    }

    .form-container .mb-3 {
        margin-bottom: 15px;
    }

    .btn {
        padding: 10px 15px;
        font-size: 14px;
    }

    .header {
        padding: 10px 15px;
    }

    .header-title {
        font-size: 18px;
    }

    .sidebar {
        width: 280px;
    }

    .nav-link {
        padding: 12px 15px;
        font-size: 14px;
    }

    .image-drop-zone {
        min-height: 120px;
        padding: 10px;
    }

    .drop-zone-content h5 {
        font-size: 14px;
    }

    .drop-zone-content p {
        font-size: 12px;
    }

    .member-profile-img {
        width: 40px;
        height: 40px;
    }

    .member-avatar {
        width: 35px;
        height: 35px;
        font-size: 12px;
    }

    .member-address {
        max-width: 150px;
        font-size: 12px;
    }
}
