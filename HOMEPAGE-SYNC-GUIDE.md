# Homepage Sync Implementation Guide

## Overview

The homepage sync feature automatically displays the latest 3 events and 3 sermons from the dashboard on the main website homepage (index.html). It maintains the exact same styling as the original static content while providing real-time updates.

## ✅ What's Implemented

### 1. **Real-time Events Display**
- Shows the latest 3 upcoming events
- Maintains the exact same card styling as the original
- Includes event images, dates, times, and descriptions
- Automatically updates every 60 seconds

### 2. **Real-time Sermons Display**
- Shows the latest 3 sermons from the dashboard
- Maintains the exact same card styling as the original
- Includes sermon thumbnails, dates, titles, and preachers
- Clickable cards that redirect to sermon video pages
- Automatically updates every 60 seconds

### 3. **Graceful Fallback**
- Keeps original static content if API is unavailable
- No visual disruption when server is down
- Seamless transition between static and dynamic content

## 📁 Files Created/Modified

### New Files:
- `js/homepage-sync.js` - Main sync script
- `test-homepage-sync.html` - Testing tool
- `HOMEPAGE-SYNC-GUIDE.md` - This documentation

### Modified Files:
- `index.html` - Added homepage sync script

## 🔧 How It Works

### Events Sync Process:
1. Fetches events from `http://localhost:3000/api/events`
2. Filters for upcoming/ongoing events
3. Sorts by date (earliest first)
4. Takes the first 3 events
5. Replaces content in `.modern-events-container`
6. Maintains exact same HTML structure and CSS classes

### Sermons Sync Process:
1. Fetches sermons from `http://localhost:3000/api/sermons`
2. Sorts by date (newest first)
3. Takes the first 3 sermons
4. Replaces content in `.featured-sermons-container`
5. Maintains exact same HTML structure and CSS classes
6. Adds click handlers for sermon video navigation

## 🎨 Styling Preservation

### Events Cards:
- Uses existing `.modern-event-card` classes
- Preserves image, date, title, description layout
- Maintains responsive design
- Keeps hover effects and animations

### Sermon Cards:
- Uses existing `.featured-sermon-card` classes
- Preserves thumbnail, date, title, author layout
- Maintains responsive design
- Keeps hover effects and animations

## 🧪 Testing

### Quick Test:
1. Start your server: `node server.js`
2. Open `test-homepage-sync.html` in browser
3. Click "Test Server Connection"
4. Click "Test Events API" and "Test Sermons API"
5. Click "Test Full Homepage Integration"

### Manual Test:
1. Open `index.html` in browser
2. Check browser console (F12) for sync messages
3. Verify events and sermons are loading
4. Check that styling matches original design

### Expected Console Messages:
```
🏠 Homepage sync script loaded
📅 Loading latest events for homepage...
📋 Events received: [array of events]
✅ Displayed 3 events on homepage
🎬 Loading latest sermons for homepage...
📺 Sermons received: [array of sermons]
✅ Displayed 3 sermons on homepage
✅ Homepage sync initialized
```

## 🔄 Real-time Updates

### Update Frequency:
- **Homepage**: Every 60 seconds (less frequent for better performance)
- **Events Page**: Every 30 seconds
- **Sermons Page**: Every 30 seconds

### Why Different Frequencies:
- Homepage has more content and visitors
- Dedicated pages need more frequent updates
- Balances real-time feel with performance

## 🛠️ Configuration

### API URL Configuration:
```javascript
// In js/homepage-sync.js
const API_URL = 'http://localhost:3000/api';
```

### Update Frequency Configuration:
```javascript
// Change update interval (currently 60 seconds)
setInterval(() => {
    loadLatestEvents();
    loadLatestSermons();
}, 60000); // Change this value
```

### Limit Configuration:
```javascript
// Change number of items displayed
.slice(0, 3); // Change from 3 to desired number
```

## 🚨 Troubleshooting

### Issue: No Events/Sermons Showing
**Check:**
1. Server is running on port 3000
2. API endpoints are accessible
3. Browser console for error messages
4. Use `test-homepage-sync.html` for diagnosis

### Issue: Styling Looks Different
**Check:**
1. CSS classes match exactly
2. HTML structure is preserved
3. No JavaScript errors in console
4. Compare with original static content

### Issue: Updates Not Working
**Check:**
1. Network connectivity
2. API responses are valid JSON
3. Console for sync messages
4. Server logs for API calls

## 📊 Performance Considerations

### Optimizations Implemented:
1. **Efficient API Calls**: Only fetches when needed
2. **Error Handling**: Graceful fallback to static content
3. **Minimal DOM Manipulation**: Only updates when content changes
4. **Reasonable Update Frequency**: Balances real-time with performance

### Memory Management:
- No memory leaks from intervals
- Proper event listener cleanup
- Efficient DOM updates

## 🔗 Integration with Existing Features

### Works With:
- ✅ Locomotive Scroll animations
- ✅ Responsive design
- ✅ Mobile navigation
- ✅ Prayer request modal
- ✅ All existing JavaScript functionality

### Doesn't Interfere With:
- ✅ Static content when API is down
- ✅ Page load performance
- ✅ SEO optimization
- ✅ Accessibility features

## 🎯 Benefits

### For Users:
- Always see the latest events and sermons
- Consistent visual experience
- Fast loading times
- No broken functionality

### For Administrators:
- Real-time content updates
- No manual website updates needed
- Easy content management through dashboard
- Automatic synchronization

## 🔮 Future Enhancements

### Possible Improvements:
1. **WebSocket Integration**: For instant updates
2. **Caching**: For better performance
3. **Progressive Loading**: For large datasets
4. **Analytics**: Track engagement with dynamic content

### Easy Customizations:
1. **Change Update Frequency**: Modify interval values
2. **Change Item Count**: Modify slice parameters
3. **Add Filters**: Filter by categories or tags
4. **Custom Styling**: Add specific styles for dynamic content

## 📞 Support

If you encounter issues:
1. Use `test-homepage-sync.html` for diagnosis
2. Check browser console for error messages
3. Verify server is running and accessible
4. Compare with working examples in other pages
