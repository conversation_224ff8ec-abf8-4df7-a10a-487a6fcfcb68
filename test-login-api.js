// Test the login API directly
async function testLogin() {
    const loginData = {
        email: '<EMAIL>',
        password: 'admin123'
    };

    console.log('Testing login with:', loginData);

    try {
        const response = await fetch('https://sda-church-production.up.railway.app/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(loginData)
        });

        console.log('Response status:', response.status);
        console.log('Response ok:', response.ok);

        const data = await response.json();
        console.log('Response data:', data);

        if (response.ok) {
            console.log('✅ Login successful!');
            console.log('Token:', data.token);
            console.log('User:', data.user);
        } else {
            console.log('❌ Login failed:', data.error);
        }

    } catch (error) {
        console.error('Error:', error);
    }
}

testLogin();
