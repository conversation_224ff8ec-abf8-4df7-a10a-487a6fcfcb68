/**
 * API Connector Script
 *
 * This script connects the main website with the admin dashboard.
 * It fetches data from the API and updates the website content dynamically.
 */

// API URL - Production backend API URL
const API_URL = 'https://sda-church-production.up.railway.app/api';

// DOM Elements
const eventsContainer = document.getElementById('events-container');
const sermonsContainer = document.getElementById('sermons-container');
const leadershipContainer = document.getElementById('leadership-container');

/**
 * Custom debug logger (uses the one from mock-api.js if available)
 */
function debugLog(message) {
    console.log(message);

    // Check if the debugLog function from mock-api.js is available
    if (window.debugLog && window.debugLog !== debugLog) {
        window.debugLog(message);
        return;
    }

    // Otherwise, log to the debug console on the page
    const debugLogElement = document.getElementById('debug-log');
    if (debugLogElement) {
        const logEntry = document.createElement('div');
        logEntry.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
        debugLogElement.appendChild(logEntry);
        debugLogElement.scrollTop = debugLogElement.scrollHeight;
    }
}

/**
 * Initialize the API connector
 */
function initApiConnector() {
    debugLog('Initializing API connector...');

    // Load data when the page loads
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            debugLog('DOM loaded, fetching data...');
            loadEvents();
            loadSermons();
            loadLeadershipTeam();
        });
    } else {
        // DOM already loaded
        debugLog('DOM already loaded, fetching data...');
        loadEvents();
        loadSermons();
        loadLeadershipTeam();
    }
}

/**
 * Load and display events
 */
async function loadEvents() {
    try {
        debugLog('Fetching events from API...');

        // Check if events container exists
        if (!eventsContainer) {
            debugLog('ERROR: Events container not found in the DOM');
            return;
        }

        // Fetch events from the API
        const response = await fetch(`${API_URL}/events`);

        if (!response.ok) {
            throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
        }

        const events = await response.json();
        debugLog(`Events loaded: ${events.length} events`);

        // Clear container
        eventsContainer.innerHTML = '';

        // If no events, show a message
        if (events.length === 0) {
            eventsContainer.innerHTML = '<p class="text-center">No upcoming events at this time.</p>';
            return;
        }

        // Sort events by date (upcoming first)
        events.sort((a, b) => new Date(a.date) - new Date(b.date));

        // Only show upcoming events
        const upcomingEvents = events.filter(event => new Date(event.date) >= new Date());

        // Limit to 3 events
        const displayEvents = upcomingEvents.slice(0, 3);

        // Display events
        displayEvents.forEach(event => {
            const eventDate = new Date(event.date);
            const month = eventDate.toLocaleString('default', { month: 'short' }).toUpperCase();
            const day = eventDate.getDate();

            const eventHtml = `
                <div class="event-card">
                    <div class="event-date">
                        <span class="event-month">${month}</span>
                        <span class="event-day">${day}</span>
                    </div>
                    <div class="event-details">
                        <h3 class="event-title">${event.title}</h3>
                        <p class="event-time"><i class="far fa-clock"></i> ${event.time}</p>
                        <p class="event-location"><i class="fas fa-map-marker-alt"></i> ${event.location}</p>
                    </div>
                </div>
            `;

            eventsContainer.innerHTML += eventHtml;
        });
    } catch (error) {
        debugLog(`ERROR loading events: ${error.message}`);
        eventsContainer.innerHTML = '<p class="text-center">Error loading events. Please try again later.</p>';
    }
}

/**
 * Load and display sermons
 */
async function loadSermons() {
    try {
        debugLog('Fetching sermons from API...');

        // Check if sermons container exists
        if (!sermonsContainer) {
            debugLog('ERROR: Sermons container not found in the DOM');
            return;
        }

        // Fetch sermons from the API
        const response = await fetch(`${API_URL}/sermons`);

        if (!response.ok) {
            throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
        }

        const sermons = await response.json();
        debugLog(`Sermons loaded: ${sermons.length} sermons`);

        // Clear container
        sermonsContainer.innerHTML = '';

        // If no sermons, show a message
        if (sermons.length === 0) {
            sermonsContainer.innerHTML = '<p class="text-center">No sermons available at this time.</p>';
            return;
        }

        // Sort sermons by date (newest first)
        sermons.sort((a, b) => new Date(b.date) - new Date(a.date));

        // Limit to 3 sermons
        const displaySermons = sermons.slice(0, 3);

        // Display sermons
        displaySermons.forEach(sermon => {
            const sermonDate = new Date(sermon.date);
            const formattedDate = sermonDate.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });

            const sermonHtml = `
                <div class="sermon-card">
                    <div class="sermon-thumbnail">
                        <img src="${sermon.thumbnail || 'images/sermon-placeholder.jpg'}" alt="${sermon.title}">
                        <div class="sermon-play-button" data-video="${sermon.videoUrl}">
                            <i class="fas fa-play"></i>
                        </div>
                    </div>
                    <div class="sermon-details">
                        <h3 class="sermon-title">${sermon.title}</h3>
                        <p class="sermon-speaker">${sermon.speaker}</p>
                        <p class="sermon-date">${formattedDate}</p>
                    </div>
                </div>
            `;

            sermonsContainer.innerHTML += sermonHtml;
        });

        // Add click event listeners to play buttons
        document.querySelectorAll('.sermon-play-button').forEach(button => {
            button.addEventListener('click', function() {
                const videoUrl = this.getAttribute('data-video');
                if (videoUrl) {
                    window.open(videoUrl, '_blank');
                }
            });
        });
    } catch (error) {
        debugLog(`ERROR loading sermons: ${error.message}`);
        sermonsContainer.innerHTML = '<p class="text-center">Error loading sermons. Please try again later.</p>';
    }
}

/**
 * Load and display leadership team
 */
async function loadLeadershipTeam() {
    try {
        debugLog('Fetching leadership team from API...');

        // Check if leadership container exists
        if (!leadershipContainer) {
            debugLog('ERROR: Leadership container not found in the DOM');
            return;
        }

        // Fetch leadership team from the API
        const response = await fetch(`${API_URL}/leadership`);

        if (!response.ok) {
            throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
        }

        const leaders = await response.json();
        debugLog(`Leadership team loaded: ${leaders.length} members`);

        // Get the team container within the leadership container
        const teamContainer = leadershipContainer.querySelector('.team-container');

        if (!teamContainer) {
            debugLog('ERROR: Team container not found within leadership container');
            return;
        }

        // Clear container
        teamContainer.innerHTML = '';

        // If no leaders, show a message
        if (leaders.length === 0) {
            teamContainer.innerHTML = '<p class="text-center">No leadership team members available at this time.</p>';
            return;
        }

        // Sort leaders by order (if specified)
        leaders.sort((a, b) => (a.order || 999) - (b.order || 999));

        // Display leaders
        leaders.forEach(leader => {
            const leaderHtml = `
                <div class="team-member">
                    <div class="team-member-image">
                        <img src="${leader.image || 'images/team-placeholder.jpg'}" alt="${leader.name}">
                    </div>
                    <h3 class="team-member-name">${leader.name}</h3>
                    <p class="team-member-role">${leader.role}</p>
                </div>
            `;

            teamContainer.innerHTML += leaderHtml;
        });
    } catch (error) {
        debugLog(`ERROR loading leadership team: ${error.message}`);
        const teamContainer = leadershipContainer.querySelector('.team-container');
        if (teamContainer) {
            teamContainer.innerHTML = '<p class="text-center">Error loading leadership team. Please try again later.</p>';
        }
    }
}

// Initialize the API connector
initApiConnector();

// Export functions for testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        loadEvents,
        loadSermons,
        loadLeadershipTeam
    };
}
