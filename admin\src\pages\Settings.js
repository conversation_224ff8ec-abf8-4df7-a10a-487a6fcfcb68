import React, { useState, useEffect } from 'react';
import { Card, Form, Button, Row, Col, Alert } from 'react-bootstrap';
import mockApi from '../services/mockApi';

const Settings = () => {
  const [settings, setSettings] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setLoading(true);
        const data = await mockApi.getSettings();
        setSettings(data);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching settings:', err);
        setError('Failed to load settings');
        setLoading(false);
      }
    };

    fetchSettings();
  }, []);

  const handleSubmit = (e) => {
    e.preventDefault();
    // In a real app, this would save the settings to the backend
    setSuccess(true);
    setTimeout(() => setSuccess(false), 3000);
  };

  if (loading) {
    return <div className="text-center py-5">Loading settings...</div>;
  }

  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }

  // Group settings by their group property
  const groupedSettings = {};
  Object.entries(settings).forEach(([key, setting]) => {
    const group = setting.group || 'general';
    if (!groupedSettings[group]) {
      groupedSettings[group] = [];
    }
    groupedSettings[group].push({ key, ...setting });
  });

  return (
    <div>
      <Card className="form-container">
        <Card.Body>
          <h3 className="form-title">Website Settings</h3>

          {success && (
            <Alert variant="success" className="mb-4">
              Settings saved successfully!
            </Alert>
          )}

          <Form onSubmit={handleSubmit}>
            {Object.entries(groupedSettings).map(([group, groupSettings]) => (
              <div key={group} className="mb-4">
                <h5 className="mb-3 text-capitalize">{group} Settings</h5>
                <Row className="mb-4">
                  {groupSettings.map(setting => (
                    <Col md={6} key={setting.key}>
                      <Form.Group className="mb-3">
                        <Form.Label>{setting.description || setting.key}</Form.Label>
                        {setting.type === 'boolean' ? (
                          <Form.Check
                            type="switch"
                            defaultChecked={setting.value}
                            label={setting.value ? 'Enabled' : 'Disabled'}
                          />
                        ) : (
                          <Form.Control
                            type={setting.type === 'number' ? 'number' : 'text'}
                            defaultValue={setting.value}
                          />
                        )}
                      </Form.Group>
                    </Col>
                  ))}
                </Row>
              </div>
            ))}

            <div className="d-flex justify-content-end">
              <Button variant="primary" type="submit">
                Save Settings
              </Button>
            </div>
          </Form>
        </Card.Body>
      </Card>
    </div>
  );
};

export default Settings;
