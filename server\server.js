const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
require('dotenv').config();

// Import database configuration
const { sequelize, testConnection, initDb } = require('./config/db');
const User = require('./models/User');

// Initialize Express app
const app = express();

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// Create directories for uploads if they don't exist
const createUploadDirs = () => {
  const dirs = [
    path.join(__dirname, '../images'),
    path.join(__dirname, '../images/events'),
    path.join(__dirname, '../images/blog'),
    path.join(__dirname, '../images/gallery')
  ];

  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`Created directory: ${dir}`);
    }
  });
};

// Create a demo admin user
const createDemoUser = async () => {
  try {
    const adminExists = await User.findOne({ where: { email: '<EMAIL>' } });

    if (!adminExists) {
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash('password', salt);

      await User.create({
        name: 'Admin User',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'admin',
        active: true
      });

      console.log('Demo admin user created');
    }
  } catch (err) {
    console.error('Error creating demo user:', err);
  }
};

// Serve static files from the React app in production
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, '../admin/build')));
}

// Define routes
app.use('/api/auth', require('./routes/auth'));
app.use('/api/events', require('./routes/events'));
app.use('/api/posts', require('./routes/posts'));
app.use('/api/gallery', require('./routes/gallery'));
app.use('/api/settings', require('./routes/settings'));

// Serve static files for the public website
app.use(express.static(path.join(__dirname, '../')));

// Catch-all route for the React app in production
if (process.env.NODE_ENV === 'production') {
  app.get('/admin*', (req, res) => {
    res.sendFile(path.join(__dirname, '../admin/build/index.html'));
  });
}

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).send({ message: 'Server Error', error: err.message });
});

// Initialize the application
const init = async () => {
  try {
    // Test database connection
    await testConnection();

    // Initialize database
    await initDb();

    // Create upload directories
    createUploadDirs();

    // Create demo user
    await createDemoUser();

    // Set port and start server
    const PORT = process.env.PORT || 5000;
    app.listen(PORT, () => console.log(`Server running on port ${PORT}`));
  } catch (err) {
    console.error('Failed to initialize application:', err);
    process.exit(1);
  }
};

// Start the application
init();
