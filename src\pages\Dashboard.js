import React, { useState, useEffect } from 'react';
import { Row, Col, Card } from 'react-bootstrap';
import axios from 'axios';

const Dashboard = () => {
  const [stats, setStats] = useState({
    events: 0,
    posts: 0,
    gallery: 0,
    users: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    // In a real application, you would fetch this data from your API
    // For now, we'll simulate it with a timeout
    const fetchStats = async () => {
      try {
        setLoading(true);
        
        // Simulate API call
        // In a real app, you would use:
        // const response = await axios.get('/api/dashboard/stats');
        // setStats(response.data);
        
        // Simulated data
        setTimeout(() => {
          setStats({
            events: 12,
            posts: 24,
            gallery: 48,
            users: 5
          });
          setLoading(false);
        }, 1000);
      } catch (err) {
        console.error('Error fetching dashboard stats:', err);
        setError('Failed to load dashboard statistics');
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  if (loading) {
    return <div className="text-center py-5">Loading dashboard data...</div>;
  }

  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }

  return (
    <div>
      <Row className="dashboard-stats">
        <Col md={3} sm={6}>
          <Card className="stat-card">
            <div className="stat-icon">
              <i className="fas fa-calendar-alt"></i>
            </div>
            <h3 className="stat-title">Total Events</h3>
            <p className="stat-value">{stats.events}</p>
          </Card>
        </Col>
        
        <Col md={3} sm={6}>
          <Card className="stat-card">
            <div className="stat-icon">
              <i className="fas fa-blog"></i>
            </div>
            <h3 className="stat-title">Blog Posts</h3>
            <p className="stat-value">{stats.posts}</p>
          </Card>
        </Col>
        
        <Col md={3} sm={6}>
          <Card className="stat-card">
            <div className="stat-icon">
              <i className="fas fa-images"></i>
            </div>
            <h3 className="stat-title">Gallery Items</h3>
            <p className="stat-value">{stats.gallery}</p>
          </Card>
        </Col>
        
        <Col md={3} sm={6}>
          <Card className="stat-card">
            <div className="stat-icon">
              <i className="fas fa-users"></i>
            </div>
            <h3 className="stat-title">Users</h3>
            <p className="stat-value">{stats.users}</p>
          </Card>
        </Col>
      </Row>
      
      <Row>
        <Col lg={8}>
          <Card className="mb-4">
            <Card.Header>
              <h5 className="mb-0">Recent Activity</h5>
            </Card.Header>
            <Card.Body>
              <p>This is where you would display recent activity logs.</p>
              <ul className="list-group">
                <li className="list-group-item">
                  <i className="fas fa-plus-circle text-success me-2"></i>
                  New event "Community Service Day" was created
                  <small className="text-muted d-block">Today, 10:30 AM</small>
                </li>
                <li className="list-group-item">
                  <i className="fas fa-edit text-primary me-2"></i>
                  Blog post "Walking in Faith" was updated
                  <small className="text-muted d-block">Yesterday, 3:45 PM</small>
                </li>
                <li className="list-group-item">
                  <i className="fas fa-upload text-info me-2"></i>
                  5 new images were added to the gallery
                  <small className="text-muted d-block">Jun 10, 2023, 1:15 PM</small>
                </li>
              </ul>
            </Card.Body>
          </Card>
        </Col>
        
        <Col lg={4}>
          <Card className="mb-4">
            <Card.Header>
              <h5 className="mb-0">Upcoming Events</h5>
            </Card.Header>
            <Card.Body>
              <ul className="list-group">
                <li className="list-group-item">
                  <h6>Community Service Day</h6>
                  <p className="mb-0">
                    <i className="far fa-calendar me-2"></i>
                    June 15, 2023
                  </p>
                  <p className="mb-0">
                    <i className="far fa-clock me-2"></i>
                    9:00 AM - 2:00 PM
                  </p>
                </li>
                <li className="list-group-item">
                  <h6>Youth Worship Night</h6>
                  <p className="mb-0">
                    <i className="far fa-calendar me-2"></i>
                    June 22, 2023
                  </p>
                  <p className="mb-0">
                    <i className="far fa-clock me-2"></i>
                    6:00 PM - 8:30 PM
                  </p>
                </li>
                <li className="list-group-item">
                  <h6>Family Potluck</h6>
                  <p className="mb-0">
                    <i className="far fa-calendar me-2"></i>
                    June 29, 2023
                  </p>
                  <p className="mb-0">
                    <i className="far fa-clock me-2"></i>
                    1:00 PM - 3:00 PM
                  </p>
                </li>
              </ul>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
