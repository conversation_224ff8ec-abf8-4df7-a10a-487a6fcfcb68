import React, { useState, useEffect } from 'react';
import { Button, Row, Col, Card } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import mockApi from '../services/mockApi';

const Gallery = () => {
  const [galleryItems, setGalleryItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchGallery = async () => {
      try {
        setLoading(true);
        const data = await mockApi.getGallery();
        setGalleryItems(data);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching gallery:', err);
        setError('Failed to load gallery items');
        setLoading(false);
      }
    };

    fetchGallery();
  }, []);

  if (loading) {
    return <div className="text-center py-5">Loading gallery...</div>;
  }

  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }

  return (
    <div>
      <div className="table-container">
        <div className="table-header">
          <h3 className="table-title">Gallery</h3>
          <div className="table-actions">
            <Button as={Link} to="/gallery/new" variant="primary">
              <i className="fas fa-plus me-2"></i> Add Image
            </Button>
          </div>
        </div>

        {galleryItems.length === 0 ? (
          <p className="text-center py-4">No gallery items found. Add your first image!</p>
        ) : (
          <Row className="g-4 mt-2">
            {galleryItems.map(item => (
              <Col key={item.id} md={4} sm={6}>
                <Card className="h-100">
                  <div className="position-relative">
                    <Card.Img
                      variant="top"
                      src={item.image || 'https://via.placeholder.com/300x200?text=No+Image'}
                      alt={item.title}
                      style={{ height: '200px', objectFit: 'cover' }}
                    />
                    {item.featured && (
                      <span className="position-absolute top-0 end-0 bg-warning text-dark px-2 py-1 m-2 rounded-pill">
                        <i className="fas fa-star me-1"></i> Featured
                      </span>
                    )}
                  </div>
                  <Card.Body>
                    <Card.Title>{item.title}</Card.Title>
                    <Card.Text>{item.description}</Card.Text>
                    <div className="d-flex justify-content-between align-items-center">
                      <small className="text-muted">{item.category}</small>
                      <div>
                        <Button as={Link} to={`/gallery/${item.id}`} variant="outline-primary" size="sm" className="me-2">
                          <i className="fas fa-edit"></i>
                        </Button>
                        <Button variant="outline-danger" size="sm">
                          <i className="fas fa-trash"></i>
                        </Button>
                      </div>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
            ))}
          </Row>
        )}
      </div>
    </div>
  );
};

export default Gallery;
