const express = require('express');
const mysql = require('mysql2/promise');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors({
    origin: [
        'http://localhost:3000',
        'http://localhost:5500',
        'http://127.0.0.1:5500',
        'https://your-netlify-site.netlify.app', // Replace with your actual Netlify URL
        process.env.FRONTEND_URL
    ],
    credentials: true
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Database connection
const dbConfig = {
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    port: process.env.DB_PORT || 3306,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
};

let db;

async function connectDB() {
    try {
        db = await mysql.createConnection(dbConfig);
        console.log('✅ Connected to MySQL database');

        // Create tables if they don't exist
        await createTables();

    } catch (error) {
        console.error('❌ Database connection failed:', error);
        process.exit(1);
    }
}

// Create tables function
async function createTables() {
    try {
        // Users table
        await db.execute(`
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                name VARCHAR(100) NOT NULL,
                role ENUM('admin', 'user') DEFAULT 'user',
                createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        `);

        // Events table
        await db.execute(`
            CREATE TABLE IF NOT EXISTS events (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                description TEXT,
                date DATE NOT NULL,
                startTime TIME NOT NULL,
                endTime TIME NOT NULL,
                location VARCHAR(255) NOT NULL,
                imageUrl VARCHAR(500),
                featured BOOLEAN DEFAULT FALSE,
                status ENUM('upcoming', 'ongoing', 'completed', 'cancelled') DEFAULT 'upcoming',
                createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        `);

        // Sermons table
        await db.execute(`
            CREATE TABLE IF NOT EXISTS sermons (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                description TEXT,
                preacher VARCHAR(100) NOT NULL,
                date DATE NOT NULL,
                videoUrl VARCHAR(500) NOT NULL,
                thumbnailUrl VARCHAR(500) NOT NULL,
                featured BOOLEAN DEFAULT FALSE,
                createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        `);

        // Leadership table
        await db.execute(`
            CREATE TABLE IF NOT EXISTS leadership (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                position VARCHAR(100) NOT NULL,
                bio TEXT,
                email VARCHAR(100),
                imageUrl VARCHAR(500),
                \`order\` INT DEFAULT 1,
                createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        `);

        // Members table
        await db.execute(`
            CREATE TABLE IF NOT EXISTS members (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                phone VARCHAR(20),
                address TEXT,
                birthdate DATE,
                memberSince DATE NOT NULL,
                ministry VARCHAR(100) NOT NULL,
                status ENUM('active', 'inactive') DEFAULT 'active',
                notes TEXT,
                createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        `);

        // Prayer requests table
        await db.execute(`
            CREATE TABLE IF NOT EXISTS prayer_requests (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                email VARCHAR(100) NOT NULL,
                phone VARCHAR(20),
                category ENUM('healing', 'deliverance', 'finance', 'job/business', 'other') NOT NULL,
                request TEXT NOT NULL,
                confidential BOOLEAN DEFAULT FALSE,
                contactMe BOOLEAN DEFAULT FALSE,
                status ENUM('pending', 'praying', 'answered') DEFAULT 'pending',
                notes TEXT,
                createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        `);

        console.log('✅ All tables created successfully');

        // Insert default admin user
        await createDefaultAdmin();

    } catch (error) {
        console.error('❌ Error creating tables:', error);
    }
}

// Create default admin user
async function createDefaultAdmin() {
    try {
        const [existingAdmin] = await db.execute('SELECT id FROM users WHERE role = "admin" LIMIT 1');

        if (existingAdmin.length === 0) {
            const hashedPassword = await bcrypt.hash('admin123', 10);
            await db.execute(`
                INSERT INTO users (username, email, password, name, role)
                VALUES (?, ?, ?, ?, ?)
            `, ['admin', '<EMAIL>', hashedPassword, 'Administrator', 'admin']);

            console.log('✅ Default admin user created');
            console.log('📧 Email: <EMAIL>');
            console.log('🔑 Password: admin123');
        }
    } catch (error) {
        console.error('❌ Error creating default admin:', error);
    }
}

// JWT middleware
const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ error: 'Access token required' });
    }

    jwt.verify(token, process.env.JWT_SECRET || 'fallback_secret', (err, user) => {
        if (err) {
            return res.status(403).json({ error: 'Invalid token' });
        }
        req.user = user;
        next();
    });
};

// Health check endpoint
app.get('/', (req, res) => {
    res.json({
        message: 'North Texas SDA Church Management API',
        status: 'running',
        timestamp: new Date().toISOString()
    });
});

app.get('/api/health', (req, res) => {
    res.json({
        status: 'healthy',
        database: db ? 'connected' : 'disconnected',
        timestamp: new Date().toISOString()
    });
});

// Auth routes
app.post('/api/auth/login', async (req, res) => {
    try {
        const { email, password } = req.body;

        if (!email || !password) {
            return res.status(400).json({ error: 'Email and password are required' });
        }

        const [users] = await db.execute('SELECT * FROM users WHERE email = ?', [email]);

        if (users.length === 0) {
            return res.status(401).json({ error: 'Invalid credentials' });
        }

        const user = users[0];
        const isValidPassword = await bcrypt.compare(password, user.password);

        if (!isValidPassword) {
            return res.status(401).json({ error: 'Invalid credentials' });
        }

        const token = jwt.sign(
            { id: user.id, email: user.email, role: user.role },
            process.env.JWT_SECRET || 'fallback_secret',
            { expiresIn: '24h' }
        );

        res.json({
            token,
            user: {
                id: user.id,
                name: user.name,
                email: user.email,
                role: user.role
            }
        });

    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Events routes
app.get('/api/events', async (req, res) => {
    try {
        const [events] = await db.execute('SELECT * FROM events ORDER BY date DESC');
        res.json(events);
    } catch (error) {
        console.error('Error fetching events:', error);
        res.status(500).json({ error: 'Failed to fetch events' });
    }
});

app.get('/api/events/:id', async (req, res) => {
    try {
        const [events] = await db.execute('SELECT * FROM events WHERE id = ?', [req.params.id]);
        if (events.length === 0) {
            return res.status(404).json({ error: 'Event not found' });
        }
        res.json(events[0]);
    } catch (error) {
        console.error('Error fetching event:', error);
        res.status(500).json({ error: 'Failed to fetch event' });
    }
});

app.post('/api/events', authenticateToken, async (req, res) => {
    try {
        const { title, description, date, startTime, endTime, location, imageUrl, featured, status } = req.body;

        const [result] = await db.execute(`
            INSERT INTO events (title, description, date, startTime, endTime, location, imageUrl, featured, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [title, description, date, startTime, endTime, location, imageUrl || null, featured || false, status || 'upcoming']);

        res.status(201).json({ id: result.insertId, message: 'Event created successfully' });
    } catch (error) {
        console.error('Error creating event:', error);
        res.status(500).json({ error: 'Failed to create event' });
    }
});

app.put('/api/events/:id', authenticateToken, async (req, res) => {
    try {
        const { title, description, date, startTime, endTime, location, imageUrl, featured, status } = req.body;

        await db.execute(`
            UPDATE events SET title = ?, description = ?, date = ?, startTime = ?, endTime = ?,
            location = ?, imageUrl = ?, featured = ?, status = ?, updatedAt = CURRENT_TIMESTAMP
            WHERE id = ?
        `, [title, description, date, startTime, endTime, location, imageUrl, featured, status, req.params.id]);

        res.json({ message: 'Event updated successfully' });
    } catch (error) {
        console.error('Error updating event:', error);
        res.status(500).json({ error: 'Failed to update event' });
    }
});

app.delete('/api/events/:id', authenticateToken, async (req, res) => {
    try {
        await db.execute('DELETE FROM events WHERE id = ?', [req.params.id]);
        res.json({ message: 'Event deleted successfully' });
    } catch (error) {
        console.error('Error deleting event:', error);
        res.status(500).json({ error: 'Failed to delete event' });
    }
});

// Sermons routes
app.get('/api/sermons', async (req, res) => {
    try {
        const [sermons] = await db.execute('SELECT * FROM sermons ORDER BY date DESC');
        res.json(sermons);
    } catch (error) {
        console.error('Error fetching sermons:', error);
        res.status(500).json({ error: 'Failed to fetch sermons' });
    }
});

app.get('/api/sermons/:id', async (req, res) => {
    try {
        const [sermons] = await db.execute('SELECT * FROM sermons WHERE id = ?', [req.params.id]);
        if (sermons.length === 0) {
            return res.status(404).json({ error: 'Sermon not found' });
        }
        res.json(sermons[0]);
    } catch (error) {
        console.error('Error fetching sermon:', error);
        res.status(500).json({ error: 'Failed to fetch sermon' });
    }
});

app.post('/api/sermons', authenticateToken, async (req, res) => {
    try {
        const { title, description, preacher, date, videoUrl, thumbnailUrl, featured } = req.body;

        const [result] = await db.execute(`
            INSERT INTO sermons (title, description, preacher, date, videoUrl, thumbnailUrl, featured)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [title, description, preacher, date, videoUrl, thumbnailUrl, featured || false]);

        res.status(201).json({ id: result.insertId, message: 'Sermon created successfully' });
    } catch (error) {
        console.error('Error creating sermon:', error);
        res.status(500).json({ error: 'Failed to create sermon' });
    }
});

app.put('/api/sermons/:id', authenticateToken, async (req, res) => {
    try {
        const { title, description, preacher, date, videoUrl, thumbnailUrl, featured } = req.body;

        await db.execute(`
            UPDATE sermons SET title = ?, description = ?, preacher = ?, date = ?,
            videoUrl = ?, thumbnailUrl = ?, featured = ?, updatedAt = CURRENT_TIMESTAMP
            WHERE id = ?
        `, [title, description, preacher, date, videoUrl, thumbnailUrl, featured, req.params.id]);

        res.json({ message: 'Sermon updated successfully' });
    } catch (error) {
        console.error('Error updating sermon:', error);
        res.status(500).json({ error: 'Failed to update sermon' });
    }
});

app.delete('/api/sermons/:id', authenticateToken, async (req, res) => {
    try {
        await db.execute('DELETE FROM sermons WHERE id = ?', [req.params.id]);
        res.json({ message: 'Sermon deleted successfully' });
    } catch (error) {
        console.error('Error deleting sermon:', error);
        res.status(500).json({ error: 'Failed to delete sermon' });
    }
});

// Leadership routes
app.get('/api/leadership', async (req, res) => {
    try {
        const [leadership] = await db.execute('SELECT * FROM leadership ORDER BY `order` ASC');
        res.json(leadership);
    } catch (error) {
        console.error('Error fetching leadership:', error);
        res.status(500).json({ error: 'Failed to fetch leadership' });
    }
});

app.get('/api/leadership/:id', async (req, res) => {
    try {
        const [leadership] = await db.execute('SELECT * FROM leadership WHERE id = ?', [req.params.id]);
        if (leadership.length === 0) {
            return res.status(404).json({ error: 'Leader not found' });
        }
        res.json(leadership[0]);
    } catch (error) {
        console.error('Error fetching leader:', error);
        res.status(500).json({ error: 'Failed to fetch leader' });
    }
});

app.post('/api/leadership', authenticateToken, async (req, res) => {
    try {
        const { name, position, bio, email, imageUrl, order } = req.body;

        const [result] = await db.execute(`
            INSERT INTO leadership (name, position, bio, email, imageUrl, \`order\`)
            VALUES (?, ?, ?, ?, ?, ?)
        `, [name, position, bio, email, imageUrl, order || 1]);

        res.status(201).json({ id: result.insertId, message: 'Leader created successfully' });
    } catch (error) {
        console.error('Error creating leader:', error);
        res.status(500).json({ error: 'Failed to create leader' });
    }
});

app.put('/api/leadership/:id', authenticateToken, async (req, res) => {
    try {
        const { name, position, bio, email, imageUrl, order } = req.body;

        await db.execute(`
            UPDATE leadership SET name = ?, position = ?, bio = ?, email = ?,
            imageUrl = ?, \`order\` = ?, updatedAt = CURRENT_TIMESTAMP
            WHERE id = ?
        `, [name, position, bio, email, imageUrl, order, req.params.id]);

        res.json({ message: 'Leader updated successfully' });
    } catch (error) {
        console.error('Error updating leader:', error);
        res.status(500).json({ error: 'Failed to update leader' });
    }
});

app.delete('/api/leadership/:id', authenticateToken, async (req, res) => {
    try {
        await db.execute('DELETE FROM leadership WHERE id = ?', [req.params.id]);
        res.json({ message: 'Leader deleted successfully' });
    } catch (error) {
        console.error('Error deleting leader:', error);
        res.status(500).json({ error: 'Failed to delete leader' });
    }
});

// Members routes
app.get('/api/members', async (req, res) => {
    try {
        const [members] = await db.execute('SELECT * FROM members ORDER BY name ASC');
        res.json(members);
    } catch (error) {
        console.error('Error fetching members:', error);
        res.status(500).json({ error: 'Failed to fetch members' });
    }
});

app.get('/api/members/:id', async (req, res) => {
    try {
        const [members] = await db.execute('SELECT * FROM members WHERE id = ?', [req.params.id]);
        if (members.length === 0) {
            return res.status(404).json({ error: 'Member not found' });
        }
        res.json(members[0]);
    } catch (error) {
        console.error('Error fetching member:', error);
        res.status(500).json({ error: 'Failed to fetch member' });
    }
});

app.post('/api/members', authenticateToken, async (req, res) => {
    try {
        const { name, phone, address, birthdate, memberSince, ministry, status, notes } = req.body;

        const [result] = await db.execute(`
            INSERT INTO members (name, phone, address, birthdate, memberSince, ministry, status, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `, [name, phone, address, birthdate, memberSince, ministry, status || 'active', notes]);

        res.status(201).json({ id: result.insertId, message: 'Member created successfully' });
    } catch (error) {
        console.error('Error creating member:', error);
        res.status(500).json({ error: 'Failed to create member' });
    }
});

app.put('/api/members/:id', authenticateToken, async (req, res) => {
    try {
        const { name, phone, address, birthdate, memberSince, ministry, status, notes } = req.body;

        await db.execute(`
            UPDATE members SET name = ?, phone = ?, address = ?, birthdate = ?,
            memberSince = ?, ministry = ?, status = ?, notes = ?, updatedAt = CURRENT_TIMESTAMP
            WHERE id = ?
        `, [name, phone, address, birthdate, memberSince, ministry, status, notes, req.params.id]);

        res.json({ message: 'Member updated successfully' });
    } catch (error) {
        console.error('Error updating member:', error);
        res.status(500).json({ error: 'Failed to update member' });
    }
});

app.delete('/api/members/:id', authenticateToken, async (req, res) => {
    try {
        await db.execute('DELETE FROM members WHERE id = ?', [req.params.id]);
        res.json({ message: 'Member deleted successfully' });
    } catch (error) {
        console.error('Error deleting member:', error);
        res.status(500).json({ error: 'Failed to delete member' });
    }
});

// Prayer requests routes
app.get('/api/prayer-requests', async (req, res) => {
    try {
        const [requests] = await db.execute('SELECT * FROM prayer_requests ORDER BY createdAt DESC');
        res.json(requests);
    } catch (error) {
        console.error('Error fetching prayer requests:', error);
        res.status(500).json({ error: 'Failed to fetch prayer requests' });
    }
});

app.get('/api/prayer-requests/:id', async (req, res) => {
    try {
        const [requests] = await db.execute('SELECT * FROM prayer_requests WHERE id = ?', [req.params.id]);
        if (requests.length === 0) {
            return res.status(404).json({ error: 'Prayer request not found' });
        }
        res.json(requests[0]);
    } catch (error) {
        console.error('Error fetching prayer request:', error);
        res.status(500).json({ error: 'Failed to fetch prayer request' });
    }
});

app.post('/api/prayer-requests', async (req, res) => {
    try {
        const { name, email, phone, category, request, confidential, contactMe } = req.body;

        const [result] = await db.execute(`
            INSERT INTO prayer_requests (name, email, phone, category, request, confidential, contactMe)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [name, email, phone, category, request, confidential || false, contactMe || false]);

        res.status(201).json({ id: result.insertId, message: 'Prayer request submitted successfully' });
    } catch (error) {
        console.error('Error creating prayer request:', error);
        res.status(500).json({ error: 'Failed to submit prayer request' });
    }
});

app.put('/api/prayer-requests/:id', authenticateToken, async (req, res) => {
    try {
        const { status, notes } = req.body;

        await db.execute(`
            UPDATE prayer_requests SET status = ?, notes = ?, updatedAt = CURRENT_TIMESTAMP
            WHERE id = ?
        `, [status, notes, req.params.id]);

        res.json({ message: 'Prayer request updated successfully' });
    } catch (error) {
        console.error('Error updating prayer request:', error);
        res.status(500).json({ error: 'Failed to update prayer request' });
    }
});

app.delete('/api/prayer-requests/:id', authenticateToken, async (req, res) => {
    try {
        await db.execute('DELETE FROM prayer_requests WHERE id = ?', [req.params.id]);
        res.json({ message: 'Prayer request deleted successfully' });
    } catch (error) {
        console.error('Error deleting prayer request:', error);
        res.status(500).json({ error: 'Failed to delete prayer request' });
    }
});

// Start server and connect to database
async function startServer() {
    await connectDB();

    app.listen(PORT, () => {
        console.log(`🚀 Server running on port ${PORT}`);
        console.log(`🌐 API URL: http://localhost:${PORT}`);
    });
}

startServer();
