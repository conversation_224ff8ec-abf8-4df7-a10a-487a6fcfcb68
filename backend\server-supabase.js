const express = require('express');
const { createClient } = require('@supabase/supabase-js');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Middleware
app.use(cors({
    origin: [
        'http://localhost:3000',
        'http://localhost:5500',
        'http://127.0.0.1:5500',
        'https://sdachurch.netlify.app',
        'https://sdachurch1.netlify.app',
        process.env.FRONTEND_URL
    ],
    credentials: true
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// JWT middleware
const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ error: 'Access token required' });
    }

    jwt.verify(token, process.env.JWT_SECRET || 'fallback_secret', (err, user) => {
        if (err) {
            return res.status(403).json({ error: 'Invalid token' });
        }
        req.user = user;
        next();
    });
};

// Health check endpoint
app.get('/', (req, res) => {
    res.json({
        message: 'North Texas SDA Church Management API (Supabase)',
        status: 'running',
        timestamp: new Date().toISOString()
    });
});

app.get('/api/health', async (req, res) => {
    try {
        // Test Supabase connection
        const { data, error } = await supabase.from('users').select('count').limit(1);

        res.json({
            status: 'healthy',
            database: error ? 'disconnected' : 'connected',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.json({
            status: 'healthy',
            database: 'unknown',
            timestamp: new Date().toISOString()
        });
    }
});

// Auth routes
app.post('/api/auth/login', async (req, res) => {
    try {
        const { email, password } = req.body;

        if (!email || !password) {
            return res.status(400).json({ error: 'Email and password are required' });
        }

        const { data: users, error } = await supabase
            .from('users')
            .select('*')
            .eq('email', email)
            .limit(1);

        if (error || !users || users.length === 0) {
            return res.status(401).json({ error: 'Invalid credentials' });
        }

        const user = users[0];
        const isValidPassword = await bcrypt.compare(password, user.password);

        if (!isValidPassword) {
            return res.status(401).json({ error: 'Invalid credentials' });
        }

        const token = jwt.sign(
            { id: user.id, email: user.email, role: user.role },
            process.env.JWT_SECRET || 'fallback_secret',
            { expiresIn: '24h' }
        );

        res.json({
            token,
            user: {
                id: user.id,
                name: user.name,
                email: user.email,
                role: user.role
            }
        });

    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Events routes
app.get('/api/events', async (req, res) => {
    try {
        const { data: events, error } = await supabase
            .from('events')
            .select('*')
            .order('date', { ascending: false });

        if (error) throw error;
        res.json(events || []);
    } catch (error) {
        console.error('Error fetching events:', error);
        res.status(500).json({ error: 'Failed to fetch events' });
    }
});

app.get('/api/events/:id', async (req, res) => {
    try {
        const { data: events, error } = await supabase
            .from('events')
            .select('*')
            .eq('id', req.params.id)
            .limit(1);

        if (error) throw error;
        if (!events || events.length === 0) {
            return res.status(404).json({ error: 'Event not found' });
        }
        res.json(events[0]);
    } catch (error) {
        console.error('Error fetching event:', error);
        res.status(500).json({ error: 'Failed to fetch event' });
    }
});

app.post('/api/events', authenticateToken, async (req, res) => {
    try {
        const { title, description, date, startTime, endTime, location, imageUrl, featured, status } = req.body;

        const { data, error } = await supabase
            .from('events')
            .insert([{
                title,
                description,
                date,
                start_time: startTime,
                end_time: endTime,
                location,
                image_url: imageUrl,
                featured: featured || false,
                status: status || 'upcoming'
            }])
            .select();

        if (error) throw error;
        res.status(201).json({ id: data[0].id, message: 'Event created successfully' });
    } catch (error) {
        console.error('Error creating event:', error);
        res.status(500).json({ error: 'Failed to create event' });
    }
});

app.put('/api/events/:id', authenticateToken, async (req, res) => {
    try {
        const { title, description, date, startTime, endTime, location, imageUrl, featured, status } = req.body;

        const { error } = await supabase
            .from('events')
            .update({
                title,
                description,
                date,
                start_time: startTime,
                end_time: endTime,
                location,
                image_url: imageUrl,
                featured,
                status,
                updated_at: new Date().toISOString()
            })
            .eq('id', req.params.id);

        if (error) throw error;
        res.json({ message: 'Event updated successfully' });
    } catch (error) {
        console.error('Error updating event:', error);
        res.status(500).json({ error: 'Failed to update event' });
    }
});

app.delete('/api/events/:id', authenticateToken, async (req, res) => {
    try {
        const { error } = await supabase
            .from('events')
            .delete()
            .eq('id', req.params.id);

        if (error) throw error;
        res.json({ message: 'Event deleted successfully' });
    } catch (error) {
        console.error('Error deleting event:', error);
        res.status(500).json({ error: 'Failed to delete event' });
    }
});

// Sermons routes
app.get('/api/sermons', async (req, res) => {
    try {
        const { data: sermons, error } = await supabase
            .from('sermons')
            .select('*')
            .order('date', { ascending: false });

        if (error) throw error;
        res.json(sermons || []);
    } catch (error) {
        console.error('Error fetching sermons:', error);
        res.status(500).json({ error: 'Failed to fetch sermons' });
    }
});

app.get('/api/sermons/:id', async (req, res) => {
    try {
        const { data: sermons, error } = await supabase
            .from('sermons')
            .select('*')
            .eq('id', req.params.id)
            .limit(1);

        if (error) throw error;
        if (!sermons || sermons.length === 0) {
            return res.status(404).json({ error: 'Sermon not found' });
        }
        res.json(sermons[0]);
    } catch (error) {
        console.error('Error fetching sermon:', error);
        res.status(500).json({ error: 'Failed to fetch sermon' });
    }
});

app.post('/api/sermons', authenticateToken, async (req, res) => {
    try {
        const { title, description, preacher, date, videoUrl, thumbnailUrl, featured } = req.body;

        const { data, error } = await supabase
            .from('sermons')
            .insert([{
                title,
                description,
                preacher,
                date,
                video_url: videoUrl,
                thumbnail_url: thumbnailUrl,
                featured: featured || false
            }])
            .select();

        if (error) throw error;
        res.status(201).json({ id: data[0].id, message: 'Sermon created successfully' });
    } catch (error) {
        console.error('Error creating sermon:', error);
        res.status(500).json({ error: 'Failed to create sermon' });
    }
});

app.put('/api/sermons/:id', authenticateToken, async (req, res) => {
    try {
        const { title, description, preacher, date, videoUrl, thumbnailUrl, featured } = req.body;

        const { error } = await supabase
            .from('sermons')
            .update({
                title,
                description,
                preacher,
                date,
                video_url: videoUrl,
                thumbnail_url: thumbnailUrl,
                featured,
                updated_at: new Date().toISOString()
            })
            .eq('id', req.params.id);

        if (error) throw error;
        res.json({ message: 'Sermon updated successfully' });
    } catch (error) {
        console.error('Error updating sermon:', error);
        res.status(500).json({ error: 'Failed to update sermon' });
    }
});

app.delete('/api/sermons/:id', authenticateToken, async (req, res) => {
    try {
        const { error } = await supabase
            .from('sermons')
            .delete()
            .eq('id', req.params.id);

        if (error) throw error;
        res.json({ message: 'Sermon deleted successfully' });
    } catch (error) {
        console.error('Error deleting sermon:', error);
        res.status(500).json({ error: 'Failed to delete sermon' });
    }
});

// Leadership routes
app.get('/api/leadership', async (req, res) => {
    try {
        const { data: leadership, error } = await supabase
            .from('leadership')
            .select('*')
            .order('display_order', { ascending: true });

        if (error) throw error;
        res.json(leadership || []);
    } catch (error) {
        console.error('Error fetching leadership:', error);
        res.status(500).json({ error: 'Failed to fetch leadership' });
    }
});

app.get('/api/leadership/:id', async (req, res) => {
    try {
        const { data: leadership, error } = await supabase
            .from('leadership')
            .select('*')
            .eq('id', req.params.id)
            .limit(1);

        if (error) throw error;
        if (!leadership || leadership.length === 0) {
            return res.status(404).json({ error: 'Leader not found' });
        }
        res.json(leadership[0]);
    } catch (error) {
        console.error('Error fetching leader:', error);
        res.status(500).json({ error: 'Failed to fetch leader' });
    }
});

app.post('/api/leadership', authenticateToken, async (req, res) => {
    try {
        const { name, position, bio, email, imageUrl, order } = req.body;

        const { data, error } = await supabase
            .from('leadership')
            .insert([{
                name,
                position,
                bio,
                email,
                image_url: imageUrl,
                display_order: order || 1
            }])
            .select();

        if (error) throw error;
        res.status(201).json({ id: data[0].id, message: 'Leader created successfully' });
    } catch (error) {
        console.error('Error creating leader:', error);
        res.status(500).json({ error: 'Failed to create leader' });
    }
});

app.put('/api/leadership/:id', authenticateToken, async (req, res) => {
    try {
        const { name, position, bio, email, imageUrl, order } = req.body;

        const { error } = await supabase
            .from('leadership')
            .update({
                name,
                position,
                bio,
                email,
                image_url: imageUrl,
                display_order: order,
                updated_at: new Date().toISOString()
            })
            .eq('id', req.params.id);

        if (error) throw error;
        res.json({ message: 'Leader updated successfully' });
    } catch (error) {
        console.error('Error updating leader:', error);
        res.status(500).json({ error: 'Failed to update leader' });
    }
});

app.delete('/api/leadership/:id', authenticateToken, async (req, res) => {
    try {
        const { error } = await supabase
            .from('leadership')
            .delete()
            .eq('id', req.params.id);

        if (error) throw error;
        res.json({ message: 'Leader deleted successfully' });
    } catch (error) {
        console.error('Error deleting leader:', error);
        res.status(500).json({ error: 'Failed to delete leader' });
    }
});

// Members routes
app.get('/api/members', async (req, res) => {
    try {
        const { data: members, error } = await supabase
            .from('members')
            .select('*')
            .order('name', { ascending: true });

        if (error) throw error;
        res.json(members || []);
    } catch (error) {
        console.error('Error fetching members:', error);
        res.status(500).json({ error: 'Failed to fetch members' });
    }
});

app.get('/api/members/:id', async (req, res) => {
    try {
        const { data: members, error } = await supabase
            .from('members')
            .select('*')
            .eq('id', req.params.id)
            .limit(1);

        if (error) throw error;
        if (!members || members.length === 0) {
            return res.status(404).json({ error: 'Member not found' });
        }
        res.json(members[0]);
    } catch (error) {
        console.error('Error fetching member:', error);
        res.status(500).json({ error: 'Failed to fetch member' });
    }
});

app.post('/api/members', authenticateToken, async (req, res) => {
    try {
        const { name, phone, address, birthdate, memberSince, ministry, status, notes } = req.body;

        const { data, error } = await supabase
            .from('members')
            .insert([{
                name,
                phone,
                address,
                birthdate,
                member_since: memberSince,
                ministry,
                status: status || 'active',
                notes
            }])
            .select();

        if (error) throw error;
        res.status(201).json({ id: data[0].id, message: 'Member created successfully' });
    } catch (error) {
        console.error('Error creating member:', error);
        res.status(500).json({ error: 'Failed to create member' });
    }
});

app.put('/api/members/:id', authenticateToken, async (req, res) => {
    try {
        const { name, phone, address, birthdate, memberSince, ministry, status, notes } = req.body;

        const { error } = await supabase
            .from('members')
            .update({
                name,
                phone,
                address,
                birthdate,
                member_since: memberSince,
                ministry,
                status,
                notes,
                updated_at: new Date().toISOString()
            })
            .eq('id', req.params.id);

        if (error) throw error;
        res.json({ message: 'Member updated successfully' });
    } catch (error) {
        console.error('Error updating member:', error);
        res.status(500).json({ error: 'Failed to update member' });
    }
});

app.delete('/api/members/:id', authenticateToken, async (req, res) => {
    try {
        const { error } = await supabase
            .from('members')
            .delete()
            .eq('id', req.params.id);

        if (error) throw error;
        res.json({ message: 'Member deleted successfully' });
    } catch (error) {
        console.error('Error deleting member:', error);
        res.status(500).json({ error: 'Failed to delete member' });
    }
});

// Prayer requests routes
app.get('/api/prayer-requests', async (req, res) => {
    try {
        const { data: requests, error } = await supabase
            .from('prayer_requests')
            .select('*')
            .order('created_at', { ascending: false });

        if (error) throw error;
        res.json(requests || []);
    } catch (error) {
        console.error('Error fetching prayer requests:', error);
        res.status(500).json({ error: 'Failed to fetch prayer requests' });
    }
});

app.get('/api/prayer-requests/:id', async (req, res) => {
    try {
        const { data: requests, error } = await supabase
            .from('prayer_requests')
            .select('*')
            .eq('id', req.params.id)
            .limit(1);

        if (error) throw error;
        if (!requests || requests.length === 0) {
            return res.status(404).json({ error: 'Prayer request not found' });
        }
        res.json(requests[0]);
    } catch (error) {
        console.error('Error fetching prayer request:', error);
        res.status(500).json({ error: 'Failed to fetch prayer request' });
    }
});

app.post('/api/prayer-requests', async (req, res) => {
    try {
        const { name, email, phone, category, request, confidential, contactMe } = req.body;

        const { data, error } = await supabase
            .from('prayer_requests')
            .insert([{
                name,
                email,
                phone,
                category,
                request,
                confidential: confidential || false,
                contact_me: contactMe || false
            }])
            .select();

        if (error) throw error;
        res.status(201).json({ id: data[0].id, message: 'Prayer request submitted successfully' });
    } catch (error) {
        console.error('Error creating prayer request:', error);
        res.status(500).json({ error: 'Failed to submit prayer request' });
    }
});

app.put('/api/prayer-requests/:id', authenticateToken, async (req, res) => {
    try {
        const { status, notes } = req.body;

        const { error } = await supabase
            .from('prayer_requests')
            .update({
                status,
                notes,
                updated_at: new Date().toISOString()
            })
            .eq('id', req.params.id);

        if (error) throw error;
        res.json({ message: 'Prayer request updated successfully' });
    } catch (error) {
        console.error('Error updating prayer request:', error);
        res.status(500).json({ error: 'Failed to update prayer request' });
    }
});

app.delete('/api/prayer-requests/:id', authenticateToken, async (req, res) => {
    try {
        const { error } = await supabase
            .from('prayer_requests')
            .delete()
            .eq('id', req.params.id);

        if (error) throw error;
        res.json({ message: 'Prayer request deleted successfully' });
    } catch (error) {
        console.error('Error deleting prayer request:', error);
        res.status(500).json({ error: 'Failed to delete prayer request' });
    }
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 Server running on port ${PORT}`);
    console.log(`🌐 API URL: http://localhost:${PORT}`);
    console.log(`🔗 Frontend URL: https://sdachurch.netlify.app`);
});
