/**
 * Prayer Request Form Handling
 * North Texas SDA Church
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get the prayer request form
    const prayerRequestForm = document.getElementById('prayer-request-form');
    const formMessage = document.getElementById('form-message');
    
    // API URL
    const API_URL = 'http://localhost:3000/api';
    
    // Add submit event listener to the form
    if (prayerRequestForm) {
        prayerRequestForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            // Get form values
            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const phone = document.getElementById('phone').value;
            const category = document.getElementById('category').value;
            const request = document.getElementById('request').value;
            const confidential = document.getElementById('confidential').checked;
            const contactMe = document.getElementById('contact-me').checked;
            
            // Validate form
            if (!name || !email || !category || !request) {
                showFormMessage('Please fill in all required fields.', 'error');
                return;
            }
            
            // Create prayer request object
            const prayerRequestData = {
                name,
                email,
                phone,
                category,
                request,
                confidential,
                contactMe,
                status: 'pending', // Default status
                createdAt: new Date().toISOString()
            };
            
            try {
                // Show loading message
                showFormMessage('Submitting your prayer request...', 'info');
                
                // Submit prayer request to API
                const response = await fetch(`${API_URL}/prayer-requests`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(prayerRequestData)
                });
                
                if (!response.ok) {
                    throw new Error('Failed to submit prayer request');
                }
                
                // Show success message
                showFormMessage('Your prayer request has been submitted successfully. Our prayer team will be praying for you.', 'success');
                
                // Reset form
                prayerRequestForm.reset();
                
            } catch (err) {
                console.error('Error submitting prayer request:', err);
                showFormMessage('There was an error submitting your prayer request. Please try again later.', 'error');
            }
        });
    }
    
    // Function to show form messages
    function showFormMessage(message, type) {
        formMessage.textContent = message;
        formMessage.className = 'form-message';
        
        if (type === 'success') {
            formMessage.classList.add('success');
        } else if (type === 'error') {
            formMessage.classList.add('error');
        } else if (type === 'info') {
            formMessage.classList.add('info');
            formMessage.style.backgroundColor = '#d1ecf1';
            formMessage.style.color = '#0c5460';
            formMessage.style.border = '1px solid #bee5eb';
            formMessage.style.display = 'block';
        }
        
        // Scroll to the message
        formMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
});
