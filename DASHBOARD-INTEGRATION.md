# Dashboard Integration Guide

This guide explains how to connect the North Texas SDA Church website with the admin dashboard.

## Overview

The website is designed to display dynamic content that can be managed through the admin dashboard. The following sections of the website are connected to the dashboard:

1. **Events** - Displays upcoming events
2. **Sermons** - Displays recent sermon videos
3. **Leadership Team** - Displays church leadership team members

## How It Works

The connection between the website and dashboard is handled by the `api-connector.js` file, which:

1. Fetches data from the backend API
2. Formats the data for display
3. Updates the website content dynamically

## Setup Instructions

### 1. Backend API Setup

Make sure your backend API is running and accessible. By default, the API URL is set to `http://localhost:3001/api`. If your API is hosted at a different URL, update the `API_URL` constant in `js/api-connector.js`.

```javascript
// Change this to match your backend API URL
const API_URL = 'http://localhost:3001/api';
```

### 2. API Endpoints

The website expects the following API endpoints to be available:

- `GET /api/events` - Returns a list of events
- `GET /api/sermons` - Returns a list of sermons
- `GET /api/leadership` - Returns a list of leadership team members

### 3. Data Formats

#### Events

Events should be returned in the following format:

```json
[
  {
    "id": 1,
    "title": "Community Service Day",
    "date": "2024-06-15",
    "time": "9:00 AM - 2:00 PM",
    "location": "Church Grounds",
    "description": "Join us for a day of service to our community."
  }
]
```

#### Sermons

Sermons should be returned in the following format:

```json
[
  {
    "id": 1,
    "title": "Walking in Faith",
    "speaker": "Pastor John Smith",
    "date": "2024-06-10",
    "thumbnail": "images/sermon1.jpg",
    "videoUrl": "https://www.youtube.com/watch?v=example"
  }
]
```

#### Leadership Team

Leadership team members should be returned in the following format:

```json
[
  {
    "id": 1,
    "name": "Pastor John Smith",
    "role": "Senior Pastor",
    "image": "images/pastor.jpg",
    "order": 1
  }
]
```

## Testing

To test if the integration is working:

1. Start your backend API server
2. Open the website in a browser
3. Check the browser console for API connection messages
4. Verify that the events, sermons, and leadership team sections are populated with data

## Troubleshooting

If you encounter issues with the integration:

1. Check the browser console for error messages
2. Verify that the API server is running and accessible
3. Confirm that the API endpoints are returning data in the expected format
4. Check that the HTML containers have the correct IDs:
   - `events-container` for events
   - `sermons-container` for sermons
   - `leadership-container` for the leadership team

## Advanced Configuration

### Custom Styling

The API connector generates HTML with the same classes as the static HTML, so the styling should be consistent. If you need to customize the styling of dynamically loaded content, update the CSS classes in `style.css`.

### Adding More Dynamic Sections

To add more dynamic sections:

1. Create a new container in the HTML with a unique ID
2. Add a new function in `api-connector.js` to fetch and display the data
3. Call the function in the `initApiConnector` function

## Support

If you need assistance with the dashboard integration, please contact the website administrator.
