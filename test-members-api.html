<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Members API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; }
        button { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            border-radius: 4px; 
            cursor: pointer; 
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .result {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 4px;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Members API Test</h1>
        <p>Test the members API endpoints to ensure they're working correctly.</p>
        
        <div class="container">
            <h3>🔧 Server Connection Test</h3>
            <button onclick="testServerConnection()">Test Server Connection</button>
            <div id="server-status"></div>
        </div>

        <div class="container">
            <h3>📋 Get All Members</h3>
            <button onclick="getAllMembers()">Get All Members</button>
            <div id="get-members-status"></div>
            <div id="get-members-result" class="result" style="display: none;"></div>
        </div>

        <div class="container">
            <h3>➕ Add New Member</h3>
            <form id="member-form">
                <div class="form-group">
                    <label for="test-name">Full Name*</label>
                    <input type="text" id="test-name" value="Test Member" required>
                </div>
                <div class="form-group">
                    <label for="test-phone">Phone Number*</label>
                    <input type="tel" id="test-phone" value="(*************" required>
                </div>
                <div class="form-group">
                    <label for="test-address">Address*</label>
                    <input type="text" id="test-address" value="123 Test St, Test City, TX 12345" required>
                </div>
                <div class="form-group">
                    <label for="test-birthdate">Birthdate*</label>
                    <input type="date" id="test-birthdate" value="1990-01-01" required>
                </div>
                <div class="form-group">
                    <label for="test-member-since">Member Since*</label>
                    <input type="date" id="test-member-since" value="2020-01-01" required>
                </div>
                <div class="form-group">
                    <label for="test-ministry">Ministry*</label>
                    <select id="test-ministry" required>
                        <option value="Worship Team">Worship Team</option>
                        <option value="Children's Ministry">Children's Ministry</option>
                        <option value="Outreach">Outreach</option>
                        <option value="Prayer Team">Prayer Team</option>
                        <option value="Hospitality">Hospitality</option>
                        <option value="Media">Media</option>
                        <option value="Other">Other</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="test-status">Status*</label>
                    <select id="test-status" required>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="test-notes">Notes</label>
                    <textarea id="test-notes" rows="3">This is a test member added via API test.</textarea>
                </div>
                <button type="button" onclick="addTestMember()">Add Test Member</button>
            </form>
            <div id="add-member-status"></div>
            <div id="add-member-result" class="result" style="display: none;"></div>
        </div>

        <div class="container">
            <h3>🗑️ Delete Test Members</h3>
            <button onclick="deleteTestMembers()">Delete All Test Members</button>
            <div id="delete-status"></div>
        </div>
    </div>

    <script>
        const API_URL = 'http://localhost:3000/api';

        // Test server connection
        async function testServerConnection() {
            const statusDiv = document.getElementById('server-status');
            statusDiv.innerHTML = '<div class="status info">Testing server connection...</div>';

            try {
                const response = await fetch(`${API_URL}/members`);
                if (response.ok) {
                    statusDiv.innerHTML = '<div class="status success">✅ Server is running and accessible</div>';
                } else {
                    statusDiv.innerHTML = `<div class="status error">❌ Server responded with status: ${response.status}</div>`;
                }
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ Server connection failed: ${error.message}</div>`;
            }
        }

        // Get all members
        async function getAllMembers() {
            const statusDiv = document.getElementById('get-members-status');
            const resultDiv = document.getElementById('get-members-result');
            
            statusDiv.innerHTML = '<div class="status info">Fetching all members...</div>';
            resultDiv.style.display = 'none';

            try {
                const response = await fetch(`${API_URL}/members`);
                if (response.ok) {
                    const members = await response.json();
                    statusDiv.innerHTML = `<div class="status success">✅ Found ${members.length} members</div>`;
                    resultDiv.innerHTML = `<pre>${JSON.stringify(members, null, 2)}</pre>`;
                    resultDiv.style.display = 'block';
                } else {
                    statusDiv.innerHTML = `<div class="status error">❌ Failed to fetch members: ${response.status}</div>`;
                }
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ Error fetching members: ${error.message}</div>`;
            }
        }

        // Add test member
        async function addTestMember() {
            const statusDiv = document.getElementById('add-member-status');
            const resultDiv = document.getElementById('add-member-result');
            
            statusDiv.innerHTML = '<div class="status info">Adding test member...</div>';
            resultDiv.style.display = 'none';

            try {
                // Get form values
                const memberData = {
                    name: document.getElementById('test-name').value,
                    phone: document.getElementById('test-phone').value,
                    address: document.getElementById('test-address').value,
                    birthdate: document.getElementById('test-birthdate').value,
                    memberSince: document.getElementById('test-member-since').value,
                    ministry: document.getElementById('test-ministry').value,
                    status: document.getElementById('test-status').value,
                    notes: document.getElementById('test-notes').value
                };

                console.log('Sending member data:', memberData);

                const response = await fetch(`${API_URL}/members`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(memberData)
                });

                console.log('Response status:', response.status);

                if (response.ok) {
                    const newMember = await response.json();
                    statusDiv.innerHTML = '<div class="status success">✅ Member added successfully!</div>';
                    resultDiv.innerHTML = `<pre>${JSON.stringify(newMember, null, 2)}</pre>`;
                    resultDiv.style.display = 'block';
                    
                    // Refresh the members list
                    setTimeout(getAllMembers, 1000);
                } else {
                    const errorText = await response.text();
                    statusDiv.innerHTML = `<div class="status error">❌ Failed to add member: ${response.status} - ${errorText}</div>`;
                }
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ Error adding member: ${error.message}</div>`;
            }
        }

        // Delete test members
        async function deleteTestMembers() {
            const statusDiv = document.getElementById('delete-status');
            statusDiv.innerHTML = '<div class="status info">Deleting test members...</div>';

            try {
                // First get all members
                const response = await fetch(`${API_URL}/members`);
                if (!response.ok) {
                    throw new Error('Failed to fetch members');
                }

                const members = await response.json();
                const testMembers = members.filter(member => 
                    member.name.includes('Test') || member.notes?.includes('test')
                );

                if (testMembers.length === 0) {
                    statusDiv.innerHTML = '<div class="status info">No test members found to delete.</div>';
                    return;
                }

                // Delete each test member
                let deletedCount = 0;
                for (const member of testMembers) {
                    const deleteResponse = await fetch(`${API_URL}/members/${member.id}`, {
                        method: 'DELETE'
                    });
                    if (deleteResponse.ok) {
                        deletedCount++;
                    }
                }

                statusDiv.innerHTML = `<div class="status success">✅ Deleted ${deletedCount} test members</div>`;
                
                // Refresh the members list
                setTimeout(getAllMembers, 1000);
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ Error deleting test members: ${error.message}</div>`;
            }
        }

        // Auto-test on page load
        window.addEventListener('load', function() {
            setTimeout(() => {
                testServerConnection();
                getAllMembers();
            }, 1000);
        });
    </script>
</body>
</html>
