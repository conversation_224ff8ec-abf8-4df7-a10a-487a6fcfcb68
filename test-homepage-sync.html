<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Homepage Sync Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; }
        button { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            border-radius: 4px; 
            cursor: pointer; 
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .test-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .api-result {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 4px;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Homepage Sync Test</h1>
        <p>This page tests the homepage sync functionality to ensure events and sermons are loading correctly.</p>
        
        <div class="test-section">
            <h3>🔧 Server Status</h3>
            <button onclick="testServerConnection()">Test Server Connection</button>
            <div id="server-status"></div>
        </div>

        <div class="test-section">
            <h3>📅 Events API Test</h3>
            <button onclick="testEventsAPI()">Test Events API</button>
            <button onclick="testEventsSync()">Test Events Sync Function</button>
            <div id="events-status"></div>
            <div id="events-result" class="api-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🎬 Sermons API Test</h3>
            <button onclick="testSermonsAPI()">Test Sermons API</button>
            <button onclick="testSermonsSync()">Test Sermons Sync Function</button>
            <div id="sermons-status"></div>
            <div id="sermons-result" class="api-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🏠 Homepage Integration Test</h3>
            <button onclick="testHomepageIntegration()">Test Full Homepage Integration</button>
            <button onclick="openHomepage()">Open Homepage</button>
            <div id="homepage-status"></div>
        </div>

        <div class="test-section">
            <h3>📊 Real-time Monitoring</h3>
            <button onclick="startMonitoring()" id="monitor-btn">Start Monitoring</button>
            <button onclick="stopMonitoring()">Stop Monitoring</button>
            <div id="monitoring-status"></div>
        </div>
    </div>

    <script>
        const API_URL = 'http://localhost:3000/api';
        let monitoringInterval = null;

        // Test server connection
        async function testServerConnection() {
            const statusDiv = document.getElementById('server-status');
            statusDiv.innerHTML = '<div class="status info">Testing server connection...</div>';

            try {
                const response = await fetch(`${API_URL}/events`);
                if (response.ok) {
                    statusDiv.innerHTML = '<div class="status success">✅ Server is running and accessible</div>';
                } else {
                    statusDiv.innerHTML = `<div class="status error">❌ Server responded with status: ${response.status}</div>`;
                }
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ Server connection failed: ${error.message}</div>`;
            }
        }

        // Test Events API
        async function testEventsAPI() {
            const statusDiv = document.getElementById('events-status');
            const resultDiv = document.getElementById('events-result');
            
            statusDiv.innerHTML = '<div class="status info">Testing Events API...</div>';
            resultDiv.style.display = 'none';

            try {
                const response = await fetch(`${API_URL}/events`);
                if (response.ok) {
                    const events = await response.json();
                    statusDiv.innerHTML = `<div class="status success">✅ Events API working - Found ${events.length} events</div>`;
                    resultDiv.innerHTML = `<pre>${JSON.stringify(events, null, 2)}</pre>`;
                    resultDiv.style.display = 'block';
                } else {
                    statusDiv.innerHTML = `<div class="status error">❌ Events API failed with status: ${response.status}</div>`;
                }
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ Events API error: ${error.message}</div>`;
            }
        }

        // Test Sermons API
        async function testSermonsAPI() {
            const statusDiv = document.getElementById('sermons-status');
            const resultDiv = document.getElementById('sermons-result');
            
            statusDiv.innerHTML = '<div class="status info">Testing Sermons API...</div>';
            resultDiv.style.display = 'none';

            try {
                const response = await fetch(`${API_URL}/sermons`);
                if (response.ok) {
                    const sermons = await response.json();
                    statusDiv.innerHTML = `<div class="status success">✅ Sermons API working - Found ${sermons.length} sermons</div>`;
                    resultDiv.innerHTML = `<pre>${JSON.stringify(sermons, null, 2)}</pre>`;
                    resultDiv.style.display = 'block';
                } else {
                    statusDiv.innerHTML = `<div class="status error">❌ Sermons API failed with status: ${response.status}</div>`;
                }
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ Sermons API error: ${error.message}</div>`;
            }
        }

        // Test Events Sync Function
        function testEventsSync() {
            const statusDiv = document.getElementById('events-status');
            
            if (typeof window.HomepageSync !== 'undefined') {
                statusDiv.innerHTML = '<div class="status info">Testing Events Sync Function...</div>';
                
                window.HomepageSync.loadLatestEvents()
                    .then(() => {
                        statusDiv.innerHTML = '<div class="status success">✅ Events sync function executed successfully</div>';
                    })
                    .catch(error => {
                        statusDiv.innerHTML = `<div class="status error">❌ Events sync function failed: ${error.message}</div>`;
                    });
            } else {
                statusDiv.innerHTML = '<div class="status error">❌ Homepage sync script not loaded</div>';
            }
        }

        // Test Sermons Sync Function
        function testSermonsSync() {
            const statusDiv = document.getElementById('sermons-status');
            
            if (typeof window.HomepageSync !== 'undefined') {
                statusDiv.innerHTML = '<div class="status info">Testing Sermons Sync Function...</div>';
                
                window.HomepageSync.loadLatestSermons()
                    .then(() => {
                        statusDiv.innerHTML = '<div class="status success">✅ Sermons sync function executed successfully</div>';
                    })
                    .catch(error => {
                        statusDiv.innerHTML = `<div class="status error">❌ Sermons sync function failed: ${error.message}</div>`;
                    });
            } else {
                statusDiv.innerHTML = '<div class="status error">❌ Homepage sync script not loaded</div>';
            }
        }

        // Test Homepage Integration
        function testHomepageIntegration() {
            const statusDiv = document.getElementById('homepage-status');
            
            statusDiv.innerHTML = '<div class="status info">Testing homepage integration...</div>';
            
            // Check if we can access the homepage elements
            const checks = [
                { name: 'Events Container', selector: '.modern-events-container' },
                { name: 'Sermons Container', selector: '.featured-sermons-container' },
                { name: 'Homepage Sync Script', condition: typeof window.HomepageSync !== 'undefined' }
            ];
            
            let results = [];
            
            checks.forEach(check => {
                if (check.selector) {
                    const element = document.querySelector(check.selector);
                    results.push(`${check.name}: ${element ? '✅ Found' : '❌ Not Found'}`);
                } else if (check.condition !== undefined) {
                    results.push(`${check.name}: ${check.condition ? '✅ Available' : '❌ Not Available'}`);
                }
            });
            
            const allPassed = results.every(result => result.includes('✅'));
            const statusClass = allPassed ? 'success' : 'warning';
            
            statusDiv.innerHTML = `
                <div class="status ${statusClass}">
                    <strong>Integration Test Results:</strong><br>
                    ${results.join('<br>')}
                </div>
            `;
        }

        // Open Homepage
        function openHomepage() {
            window.open('index.html', '_blank');
        }

        // Start Monitoring
        function startMonitoring() {
            const statusDiv = document.getElementById('monitoring-status');
            const btn = document.getElementById('monitor-btn');
            
            if (monitoringInterval) {
                stopMonitoring();
            }
            
            statusDiv.innerHTML = '<div class="status info">🔄 Starting real-time monitoring...</div>';
            btn.textContent = 'Monitoring Active';
            btn.disabled = true;
            
            let checkCount = 0;
            monitoringInterval = setInterval(async () => {
                checkCount++;
                
                try {
                    const [eventsResponse, sermonsResponse] = await Promise.all([
                        fetch(`${API_URL}/events`),
                        fetch(`${API_URL}/sermons`)
                    ]);
                    
                    const eventsOk = eventsResponse.ok;
                    const sermonsOk = sermonsResponse.ok;
                    
                    const timestamp = new Date().toLocaleTimeString();
                    
                    if (eventsOk && sermonsOk) {
                        const events = await eventsResponse.json();
                        const sermons = await sermonsResponse.json();
                        
                        statusDiv.innerHTML = `
                            <div class="status success">
                                ✅ Check #${checkCount} at ${timestamp}<br>
                                Events: ${events.length} | Sermons: ${sermons.length}
                            </div>
                        `;
                    } else {
                        statusDiv.innerHTML = `
                            <div class="status error">
                                ❌ Check #${checkCount} at ${timestamp}<br>
                                Events API: ${eventsOk ? 'OK' : 'Failed'} | Sermons API: ${sermonsOk ? 'OK' : 'Failed'}
                            </div>
                        `;
                    }
                } catch (error) {
                    statusDiv.innerHTML = `
                        <div class="status error">
                            ❌ Check #${checkCount} at ${new Date().toLocaleTimeString()}<br>
                            Error: ${error.message}
                        </div>
                    `;
                }
            }, 5000); // Check every 5 seconds
        }

        // Stop Monitoring
        function stopMonitoring() {
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
                monitoringInterval = null;
                
                const btn = document.getElementById('monitor-btn');
                btn.textContent = 'Start Monitoring';
                btn.disabled = false;
                
                const statusDiv = document.getElementById('monitoring-status');
                statusDiv.innerHTML = '<div class="status info">⏹️ Monitoring stopped</div>';
            }
        }

        // Auto-test on page load
        window.addEventListener('load', function() {
            setTimeout(() => {
                testServerConnection();
            }, 1000);
        });
    </script>
</body>
</html>
