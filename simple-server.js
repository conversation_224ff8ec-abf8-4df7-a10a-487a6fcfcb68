const express = require('express');
const path = require('path');
const fs = require('fs');

// Initialize Express app
const app = express();

// Middleware
app.use(express.json());
app.use(express.static(__dirname));

// In-memory data store
let events = [
  {
    id: 1,
    title: 'Community Service Day',
    description: 'Join us as we serve our community through various outreach activities.',
    date: '2023-06-15',
    startTime: '09:00',
    endTime: '14:00',
    location: 'Church Grounds',
    featured: true,
    status: 'upcoming'
  },
  {
    id: 2,
    title: 'Youth Worship Night',
    description: 'A special evening of praise and worship led by our youth ministry.',
    date: '2023-06-22',
    startTime: '18:00',
    endTime: '20:30',
    location: 'Fellowship Hall',
    featured: false,
    status: 'upcoming'
  },
  {
    id: 3,
    title: 'Family Potluck',
    description: 'Bring your favorite dish and join us for fellowship after the service.',
    date: '2023-06-29',
    startTime: '13:00',
    endTime: '15:00',
    location: 'Church Fellowship Hall',
    featured: true,
    status: 'upcoming'
  }
];

// API Routes

// Get all events
app.get('/api/events', (req, res) => {
  res.json(events);
});

// Get a single event
app.get('/api/events/:id', (req, res) => {
  const event = events.find(e => e.id === parseInt(req.params.id));
  if (!event) return res.status(404).json({ message: 'Event not found' });
  res.json(event);
});

// Create a new event
app.post('/api/events', (req, res) => {
  const newEvent = {
    id: events.length + 1,
    ...req.body,
    createdAt: new Date().toISOString()
  };
  events.push(newEvent);
  
  // Update the index.html file to reflect the new event
  updateMainWebsite();
  
  res.status(201).json(newEvent);
});

// Update an event
app.put('/api/events/:id', (req, res) => {
  const eventIndex = events.findIndex(e => e.id === parseInt(req.params.id));
  if (eventIndex === -1) return res.status(404).json({ message: 'Event not found' });
  
  events[eventIndex] = { ...events[eventIndex], ...req.body };
  
  // Update the index.html file to reflect the updated event
  updateMainWebsite();
  
  res.json(events[eventIndex]);
});

// Delete an event
app.delete('/api/events/:id', (req, res) => {
  const eventIndex = events.findIndex(e => e.id === parseInt(req.params.id));
  if (eventIndex === -1) return res.status(404).json({ message: 'Event not found' });
  
  events.splice(eventIndex, 1);
  
  // Update the index.html file to reflect the deleted event
  updateMainWebsite();
  
  res.json({ message: 'Event deleted' });
});

// Function to update the main website
function updateMainWebsite() {
  try {
    // Read the index.html file
    const indexPath = path.join(__dirname, 'index.html');
    let indexContent = fs.readFileSync(indexPath, 'utf8');
    
    // Find the events section
    const eventsStartTag = '<!-- Upcoming Events Section -->';
    const eventsEndTag = '<!-- Latest Sermons Section -->';
    
    const startIndex = indexContent.indexOf(eventsStartTag);
    const endIndex = indexContent.indexOf(eventsEndTag);
    
    if (startIndex !== -1 && endIndex !== -1) {
      // Generate new events HTML
      let eventsHtml = eventsStartTag + `
    <section class="section events" style="background-color: var(--light-color);">
        <div class="container">
            <div class="section-title" data-aos="fade-up">
                <h2>Upcoming Events</h2>
                <p>Join us for these special occasions</p>
            </div>
            
            <div class="events-container" data-aos="fade-up" data-aos-delay="100">`;
      
      // Add each event
      events.forEach(event => {
        const eventDate = new Date(event.date);
        const day = eventDate.getDate();
        const month = eventDate.toLocaleString('default', { month: 'short' });
        
        eventsHtml += `
                <div class="event-card">
                    <div class="event-date">
                        <span class="day">${day}</span>
                        <span class="month">${month}</span>
                    </div>
                    <div class="event-details">
                        <h3>${event.title}</h3>
                        <p class="event-time"><i class="far fa-clock"></i> ${event.startTime} - ${event.endTime}</p>
                        <p class="event-location"><i class="fas fa-map-marker-alt"></i> ${event.location}</p>
                        <p class="event-description">${event.description}</p>
                        <a href="events.html" class="btn secondary-btn">Learn More</a>
                    </div>
                </div>`;
      });
      
      eventsHtml += `
            </div>
            
            <div class="text-center mt-5" data-aos="fade-up" data-aos-delay="200">
                <a href="events.html" class="btn primary-btn">View All Events</a>
            </div>
        </div>
    </section>`;
      
      // Replace the events section in the index.html file
      const newIndexContent = indexContent.substring(0, startIndex) + eventsHtml + indexContent.substring(endIndex);
      
      // Write the updated content back to the file
      fs.writeFileSync(indexPath, newIndexContent, 'utf8');
      
      console.log('Main website updated successfully');
    } else {
      console.error('Could not find events section in index.html');
    }
  } catch (err) {
    console.error('Error updating main website:', err);
  }
}

// Simple authentication for demo purposes
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;
  
  if (email === '<EMAIL>' && password === 'password') {
    res.json({
      token: 'demo-token-123456',
      user: {
        id: 1,
        name: 'Admin User',
        email: '<EMAIL>',
        role: 'admin'
      }
    });
  } else {
    res.status(401).json({ message: 'Invalid credentials' });
  }
});

// Start the server
const PORT = 3000;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Main website: http://localhost:${PORT}/index.html`);
  console.log(`Admin dashboard: http://localhost:${PORT}/admin-dashboard.html`);
});
