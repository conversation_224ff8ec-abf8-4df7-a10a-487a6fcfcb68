<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Placeholder Image Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
        }
        .canvas-container {
            margin: 20px 0;
            text-align: center;
        }
        canvas {
            border: 1px solid #ddd;
            max-width: 100%;
        }
        .controls {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        .download-btn {
            background-color: #2196F3;
        }
        .download-btn:hover {
            background-color: #0b7dda;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Placeholder Image Generator</h1>
        
        <div class="controls">
            <label for="width">Width (px):</label>
            <input type="number" id="width" value="300" min="50" max="1000">
            
            <label for="height">Height (px):</label>
            <input type="number" id="height" value="300" min="50" max="1000">
            
            <label for="bgColor">Background Color:</label>
            <input type="color" id="bgColor" value="#f95c28">
            
            <label for="textColor">Text Color:</label>
            <input type="color" id="textColor" value="#ffffff">
            
            <label for="text">Text:</label>
            <input type="text" id="text" value="Event Image">
            
            <button onclick="generateImage()">Generate Image</button>
        </div>
        
        <div class="canvas-container">
            <canvas id="canvas" width="300" height="300"></canvas>
        </div>
        
        <div style="text-align: center;">
            <button class="download-btn" onclick="downloadImage()">Download Image</button>
        </div>
    </div>

    <script>
        // Generate image when page loads
        window.onload = generateImage;
        
        function generateImage() {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            // Get values from inputs
            const width = document.getElementById('width').value;
            const height = document.getElementById('height').value;
            const bgColor = document.getElementById('bgColor').value;
            const textColor = document.getElementById('textColor').value;
            const text = document.getElementById('text').value;
            
            // Update canvas size
            canvas.width = width;
            canvas.height = height;
            
            // Fill background
            ctx.fillStyle = bgColor;
            ctx.fillRect(0, 0, width, height);
            
            // Add text
            ctx.fillStyle = textColor;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            // Calculate font size based on canvas dimensions
            const fontSize = Math.min(width, height) / 10;
            ctx.font = `bold ${fontSize}px Arial`;
            
            // Draw text in the center
            ctx.fillText(text, width / 2, height / 2);
        }
        
        function downloadImage() {
            const canvas = document.getElementById('canvas');
            const link = document.createElement('a');
            link.download = 'placeholder-image.jpg';
            link.href = canvas.toDataURL('image/jpeg', 0.8);
            link.click();
        }
    </script>
</body>
</html>
