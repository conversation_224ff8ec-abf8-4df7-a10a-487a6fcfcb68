import React, { useState, useEffect } from 'react';
import { Card, Button, Table, Badge } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import mockApi from '../services/mockApi';

const Events = () => {
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchEvents = async () => {
      try {
        setLoading(true);
        const data = await mockApi.getEvents();
        setEvents(data);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching events:', err);
        setError('Failed to load events');
        setLoading(false);
      }
    };

    fetchEvents();
  }, []);

  const getStatusBadge = (status) => {
    switch (status) {
      case 'upcoming':
        return <Badge bg="primary">Upcoming</Badge>;
      case 'ongoing':
        return <Badge bg="success">Ongoing</Badge>;
      case 'completed':
        return <Badge bg="secondary">Completed</Badge>;
      case 'cancelled':
        return <Badge bg="danger">Cancelled</Badge>;
      default:
        return <Badge bg="light">Unknown</Badge>;
    }
  };

  if (loading) {
    return <div className="text-center py-5">Loading events...</div>;
  }

  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }

  return (
    <div>
      <div className="table-container">
        <div className="table-header">
          <h3 className="table-title">Events</h3>
          <div className="table-actions">
            <Button as={Link} to="/events/new" variant="primary">
              <i className="fas fa-plus me-2"></i> Add Event
            </Button>
          </div>
        </div>

        {events.length === 0 ? (
          <p className="text-center py-4">No events found. Create your first event!</p>
        ) : (
          <Table responsive hover>
            <thead>
              <tr>
                <th>Title</th>
                <th>Date</th>
                <th>Time</th>
                <th>Location</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {events.map(event => (
                <tr key={event.id}>
                  <td>{event.title}</td>
                  <td>{new Date(event.date).toLocaleDateString()}</td>
                  <td>{event.startTime} - {event.endTime}</td>
                  <td>{event.location}</td>
                  <td>{getStatusBadge(event.status)}</td>
                  <td>
                    <Button as={Link} to={`/events/${event.id}`} variant="outline-primary" size="sm" className="me-2">
                      <i className="fas fa-edit"></i>
                    </Button>
                    <Button variant="outline-danger" size="sm">
                      <i className="fas fa-trash"></i>
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </Table>
        )}
      </div>
    </div>
  );
};

export default Events;
