/**
 * Template Connector Script
 *
 * This script connects the WordPress template with our admin dashboard.
 * It fetches data from our API and updates the template dynamically.
 * It preserves the original template styling.
 */

// API URLs - Try multiple possible URLs
const API_URLS = [
    'https://sda-church-production.up.railway.app/api',  // Production API URL
    'http://localhost:3000/api',  // Local development fallback
    'http://localhost:3001/api',  // Alternative port
    'http://127.0.0.1:3000/api',  // Alternative host
    'http://127.0.0.1:3001/api'   // Alternative host and port
];

// We'll try to find a working API
let API_URL = API_URLS[0]; // Default to the first URL

// Function to check if an API is available
async function findWorkingAPI() {
    console.log('Checking for available APIs...');

    for (const url of API_URLS) {
        try {
            console.log(`Trying API URL: ${url}`);

            // Try to fetch with a short timeout
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 1000); // 1 second timeout

            const response = await fetch(`${url}/health`, {
                signal: controller.signal
            }).catch(() => ({ ok: false }));

            clearTimeout(timeoutId);

            if (response.ok) {
                console.log(`Found working API at: ${url}`);
                return url;
            }
        } catch (err) {
            console.log(`API at ${url} is not available:`, err.message);
        }
    }

    console.warn('No working API found, using default URL');
    return API_URLS[0];
}

// Function to log to the console (simplified)
function debugLog(message) {
    console.log(message);
}

// Initialize the connector
document.addEventListener('DOMContentLoaded', async () => {
    debugLog('Template connector initialized');

    try {
        // Try to find a working API
        API_URL = await findWorkingAPI();
    } catch (err) {
        debugLog('Error finding working API: ' + err.message);
        debugLog('Using default API URL: ' + API_URL);
    }

    debugLog('Using API URL: ' + API_URL);

    // Find the events container
    const eventsContainer = document.getElementById('events-container');
    debugLog('Events container: ' + (eventsContainer ? 'Found' : 'Not found'));
    debugLog('Events container ID: ' + (eventsContainer ? eventsContainer.id : 'N/A'));
    debugLog('Events container class: ' + (eventsContainer ? eventsContainer.className : 'N/A'));
    debugLog('Events container parent: ' + (eventsContainer && eventsContainer.parentElement ? eventsContainer.parentElement.className : 'N/A'));

    // Load events if the container exists
    if (eventsContainer) {
        debugLog('Events container found, loading events...');
        loadEvents(eventsContainer);
    } else {
        debugLog('ERROR: Events container not found');
        debugLog('Make sure to add an element with ID "events-container" to the HTML file');
    }

    // Find the leadership team container
    const leadershipContainer = document.getElementById('leadership-container');
    debugLog('Leadership container: ' + (leadershipContainer ? 'Found' : 'Not found'));
    debugLog('Leadership container ID: ' + (leadershipContainer ? leadershipContainer.id : 'N/A'));
    debugLog('Leadership container class: ' + (leadershipContainer ? leadershipContainer.className : 'N/A'));
    debugLog('Leadership container parent: ' + (leadershipContainer && leadershipContainer.parentElement ? leadershipContainer.parentElement.className : 'N/A'));

    // Load leadership team if the container exists
    if (leadershipContainer) {
        debugLog('Leadership container found, loading team members...');
        loadLeadershipTeam(leadershipContainer);
    } else {
        debugLog('ERROR: Leadership container not found');
        debugLog('Make sure to add an element with ID "leadership-container" to the HTML file');
    }
});

/**
 * Load and display events
 * @param {HTMLElement} container - The container to display events in
 */
async function loadEvents(container) {
    try {
        debugLog('Fetching events from API...');
        debugLog('API URL for events: ' + `${API_URL}/events`);

        // Store the original content before showing loading message
        const originalContent = container.innerHTML;

        // Show loading message
        container.innerHTML = '<div class="loading-spinner">Loading events...</div>';

        try {
            // Fetch events from the API with a timeout
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

            const response = await fetch(`${API_URL}/events`, {
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            debugLog('Events API response status: ' + response.status);

            if (!response.ok) {
                throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
            }

            const events = await response.json();
            debugLog('Events loaded: ' + events.length + ' events');

            // Sort events by date (upcoming first)
            events.sort((a, b) => new Date(a.date) - new Date(b.date));
            debugLog('Events sorted by date');

            // For testing purposes, show all events instead of filtering by date
            const upcomingEvents = events; // Don't filter by date for now
            debugLog('Events to display: ' + upcomingEvents.length + ' events');

            // Clear container - remove the loading spinner
            debugLog('Clearing events container...');
            container.innerHTML = '';
            debugLog('Events container cleared');

            // Display events
            if (upcomingEvents.length === 0) {
                container.innerHTML += '<div class="tribe-events-empty" style="text-align: center; padding: 20px;">No upcoming events</div>';
                console.log('No upcoming events found');
                return;
            }
        } catch (fetchError) {
            console.error('Error fetching events from API:', fetchError);
            console.log('Falling back to static content');

            // Restore the original content if there's an error
            container.innerHTML = originalContent;

            // Add a small indicator that we're using static content
            const staticIndicator = document.createElement('div');
            staticIndicator.style.textAlign = 'center';
            staticIndicator.style.fontSize = '12px';
            staticIndicator.style.color = '#999';
            staticIndicator.style.padding = '5px';
            staticIndicator.textContent = '(Using static content - API unavailable)';

            // Insert the indicator after the section header
            const sectionHeader = container.querySelector('.section-header');
            if (sectionHeader && sectionHeader.nextSibling) {
                container.insertBefore(staticIndicator, sectionHeader.nextSibling);
            } else {
                container.insertBefore(staticIndicator, container.firstChild);
            }

            return;
        }

        // Create events HTML directly
        debugLog('Creating events HTML directly');

        // Create HTML for all events
        let eventsHTML = '';

        upcomingEvents.slice(0, 3).forEach(event => {
            debugLog('Creating HTML for event: ' + event.title);

            const eventDate = new Date(event.date);
            const day = eventDate.getDate();
            const month = eventDate.toLocaleString('en-US', { month: 'short' }).toUpperCase();

            // Format the time for display
            const startTime = event.startTime ?
                new Date(`2000-01-01T${event.startTime}`).toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' }) :
                '9:00 AM';

            const endTime = event.endTime ?
                new Date(`2000-01-01T${event.endTime}`).toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' }) :
                '2:00 PM';

            debugLog(`Event times: ${startTime} - ${endTime}`);

            // Add HTML for this event
            eventsHTML += `
                <div class="event-card">
                    <div class="event-date">
                        <span class="event-month">${month}</span>
                        <span class="event-day">${day}</span>
                    </div>
                    <div class="event-details">
                        <h3 class="event-title">${event.title}</h3>
                        <p class="event-time"><i class="far fa-clock"></i> ${startTime} - ${endTime}</p>
                        <p class="event-location"><i class="fas fa-map-marker-alt"></i> ${event.location}</p>
                    </div>
                </div>
            `;

            debugLog(`Added HTML for event: ${event.title}`);
        });

        // Set the container's HTML
        debugLog(`Setting container HTML with ${upcomingEvents.length} events`);
        debugLog(`Container before: ${container.outerHTML.substring(0, 100)}...`);
        container.innerHTML = eventsHTML;
        debugLog(`Container after: ${container.outerHTML.substring(0, 100)}...`);
        debugLog(`Set container HTML with ${upcomingEvents.length} events`);

        debugLog('Events added to container');
        debugLog('Final container HTML: ' + container.children.length + ' events');

        console.log('Events displayed successfully');
    } catch (err) {
        console.error('Error in loadEvents function:', err);
        console.error('Error details:', err.message, err.stack);

        // Restore the original content
        container.innerHTML = originalContent;

        // Add a small indicator that we're using static content
        const staticIndicator = document.createElement('div');
        staticIndicator.style.textAlign = 'center';
        staticIndicator.style.fontSize = '12px';
        staticIndicator.style.color = '#999';
        staticIndicator.style.padding = '5px';
        staticIndicator.textContent = '(Using static content - API unavailable)';

        // Insert the indicator after the section header
        const sectionHeader = container.querySelector('.section-header');
        if (sectionHeader && sectionHeader.nextSibling) {
            container.insertBefore(staticIndicator, sectionHeader.nextSibling);
        } else {
            container.insertBefore(staticIndicator, container.firstChild);
        }
    }
}

/**
 * Load and display leadership team members
 * @param {HTMLElement} container - The container to display team members in
 */
async function loadLeadershipTeam(container) {
    try {
        console.log('Fetching leadership team from API...');

        // Store the original content before showing loading message
        const originalContent = container.innerHTML;

        // Show loading message
        const loadingMessage = document.createElement('div');
        loadingMessage.className = 'loading-spinner';
        loadingMessage.textContent = 'Loading leadership team...';
        loadingMessage.style.textAlign = 'center';
        loadingMessage.style.padding = '20px';

        // Find the team container
        const teamContainer = container.querySelector('.team-container');
        if (teamContainer) {
            teamContainer.innerHTML = '';
            teamContainer.appendChild(loadingMessage);
        } else {
            container.innerHTML = '';
            container.appendChild(loadingMessage);
        }

        try {
            // Fetch leadership team from the API with a timeout
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

            const response = await fetch(`${API_URL}/leadership`, {
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
            }

            const leaders = await response.json();
            console.log('Leadership team loaded:', leaders);

            // Sort leaders by order (if specified)
            leaders.sort((a, b) => (a.order || 999) - (b.order || 999));

            // Keep the section header but clear the team container
            if (teamContainer) {
                debugLog('Clearing team container...');
                teamContainer.innerHTML = '';
                debugLog('Team container cleared');
            } else {
                debugLog('Clearing leadership container...');
                container.innerHTML = '';
                debugLog('Leadership container cleared');
            }

            // Display leadership team
            if (leaders.length === 0) {
                if (teamContainer) {
                    teamContainer.innerHTML = '<div class="leadership-empty" style="text-align: center; padding: 20px;">No team members found</div>';
                } else {
                    container.innerHTML += '<div class="leadership-empty" style="text-align: center; padding: 20px;">No team members found</div>';
                }
                return;
            }
        } catch (fetchError) {
            console.error('Error fetching leadership team from API:', fetchError);
            console.log('Falling back to static content');

            // Restore the original content if there's an error
            container.innerHTML = originalContent;

            // Add a small indicator that we're using static content
            const staticIndicator = document.createElement('div');
            staticIndicator.style.textAlign = 'center';
            staticIndicator.style.fontSize = '12px';
            staticIndicator.style.color = '#999';
            staticIndicator.style.padding = '5px';
            staticIndicator.textContent = '(Using static content - API unavailable)';

            // Insert the indicator after the section header
            const sectionHeader = container.querySelector('.section-header');
            if (sectionHeader && sectionHeader.nextSibling) {
                container.insertBefore(staticIndicator, sectionHeader.nextSibling);
            } else {
                container.insertBefore(staticIndicator, container.firstChild);
            }

            return;
        }

        // Create leadership team HTML directly
        debugLog('Creating leadership team HTML directly');

        // Create HTML for all leaders
        let leadersHTML = '';

        leaders.forEach(leader => {
            debugLog('Creating HTML for leader: ' + leader.name);

            // Add HTML for this leader
            leadersHTML += `
                <div class="team-member">
                    <div class="team-member-image">
                        <img src="${leader.imageUrl || 'https://via.placeholder.com/250x250'}" alt="${leader.name}">
                    </div>
                    <h3 class="team-member-name">${leader.name}</h3>
                    <p class="team-member-role">${leader.position}</p>
                </div>
            `;

            debugLog(`Added HTML for leader: ${leader.name}`);
        });

        // Set the container's HTML
        if (teamContainer) {
            debugLog(`Setting team container HTML with ${leaders.length} leaders`);
            debugLog(`Team container before: ${teamContainer.outerHTML.substring(0, 100)}...`);
            teamContainer.innerHTML = leadersHTML;
            debugLog(`Team container after: ${teamContainer.outerHTML.substring(0, 100)}...`);
            debugLog(`Set team container HTML with ${leaders.length} leaders`);
        } else {
            debugLog(`Setting leadership container HTML with ${leaders.length} leaders`);
            debugLog(`Leadership container before: ${container.outerHTML.substring(0, 100)}...`);

            // Try to find the container element inside the leadership container
            const containerElement = container.querySelector('.container');
            if (containerElement) {
                // Try to find the team-container inside the container element
                const innerTeamContainer = containerElement.querySelector('.team-container');
                if (innerTeamContainer) {
                    debugLog(`Found inner team container`);
                    innerTeamContainer.innerHTML = leadersHTML;
                    debugLog(`Set inner team container HTML with ${leaders.length} leaders`);
                } else {
                    debugLog(`Inner team container not found`);
                    container.innerHTML = leadersHTML;
                }
            } else {
                debugLog(`Container element not found`);
                container.innerHTML = leadersHTML;
            }

            debugLog(`Leadership container after: ${container.outerHTML.substring(0, 100)}...`);
            debugLog(`Set leadership container HTML with ${leaders.length} leaders`);
        }

        debugLog('Leadership team displayed successfully: ' + leaders.length + ' members');
    } catch (err) {
        console.error('Error in loadLeadershipTeam function:', err);
        console.error('Error details:', err.message, err.stack);

        // Restore the original content
        container.innerHTML = originalContent;

        // Add a small indicator that we're using static content
        const staticIndicator = document.createElement('div');
        staticIndicator.style.textAlign = 'center';
        staticIndicator.style.fontSize = '12px';
        staticIndicator.style.color = '#999';
        staticIndicator.style.padding = '5px';
        staticIndicator.textContent = '(Using static content - API unavailable)';

        // Insert the indicator after the section header
        const sectionHeader = container.querySelector('.section-header');
        if (sectionHeader && sectionHeader.nextSibling) {
            container.insertBefore(staticIndicator, sectionHeader.nextSibling);
        } else {
            container.insertBefore(staticIndicator, container.firstChild);
        }
    }
}

// We'll add more functions as we add more container IDs to the template
