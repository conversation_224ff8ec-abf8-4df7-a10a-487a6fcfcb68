/* 
* Faith Connect Church - Components Stylesheet
* Author: Augment Agent
* Version: 1.0
*/

/* Service Times */
.service-times-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  margin-top: 30px;
}

.service-time-card {
  background-color: var(--white);
  border-radius: 10px;
  padding: 30px;
  text-align: center;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}

.service-time-card:hover {
  transform: translateY(-10px);
}

.service-time-icon {
  width: 70px;
  height: 70px;
  background-color: var(--accent-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
}

.service-time-icon i {
  font-size: 30px;
  color: var(--primary-color);
}

.service-time-card h3 {
  font-size: 1.5rem;
  margin-bottom: 10px;
  color: var(--primary-color);
}

/* Events */
.events-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 30px;
}

.event-card {
  background-color: var(--white);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: var(--box-shadow);
  display: flex;
  transition: var(--transition);
}

.event-card:hover {
  transform: translateY(-10px);
}

.event-date {
  background-color: var(--primary-color);
  color: var(--white);
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 80px;
}

.event-date .day {
  font-size: 1.8rem;
  font-weight: 700;
  line-height: 1;
}

.event-date .month {
  font-size: 1rem;
  text-transform: uppercase;
}

.event-details {
  padding: 20px;
  flex: 1;
}

.event-details h3 {
  font-size: 1.3rem;
  margin-bottom: 10px;
  color: var(--primary-color);
}

.event-time, .event-location {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: 5px;
}

.event-time i, .event-location i {
  margin-right: 5px;
  color: var(--primary-color);
}

.event-description {
  margin: 15px 0;
}

/* Sermons */
.sermons-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 30px;
}

.sermon-card {
  background-color: var(--white);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}

.sermon-card:hover {
  transform: translateY(-10px);
}

.sermon-thumbnail {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.sermon-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.sermon-card:hover .sermon-thumbnail img {
  transform: scale(1.1);
}

.play-btn {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-size: 1.5rem;
  transition: var(--transition);
}

.play-btn:hover {
  background-color: var(--primary-color);
  color: var(--white);
}

.sermon-details {
  padding: 20px;
}

.sermon-details h3 {
  font-size: 1.3rem;
  margin-bottom: 10px;
  color: var(--primary-color);
}

.sermon-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: 15px;
}

.sermon-meta i {
  margin-right: 5px;
  color: var(--primary-color);
}

.sermon-description {
  margin-bottom: 15px;
}

.sermon-links {
  display: flex;
  gap: 15px;
}

.sermon-links a {
  font-size: 0.9rem;
  color: var(--primary-color);
}

.sermon-links a i {
  margin-right: 5px;
}

/* Call to Action */
.cta {
  position: relative;
  background-size: cover;
  background-position: center;
  padding: 100px 0;
}

.cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
}

.cta-content {
  position: relative;
  z-index: 1;
  text-align: center;
  color: var(--white);
  max-width: 700px;
  margin: 0 auto;
}

.cta-content h2 {
  font-size: 2.5rem;
  margin-bottom: 20px;
}

.cta-content p {
  margin-bottom: 30px;
  font-size: 1.1rem;
}

/* Back to Top Button */
.back-to-top {
  position: fixed;
  right: 20px;
  bottom: 20px;
  width: 40px;
  height: 40px;
  background-color: var(--primary-color);
  color: var(--white);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
}

.back-to-top.active {
  opacity: 1;
  visibility: visible;
}

.back-to-top:hover {
  background-color: var(--secondary-color);
  color: var(--white);
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.mt-5 {
  margin-top: 50px;
}

/* Form Styles */
.form-group {
  margin-bottom: 20px;
  position: relative;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-family: 'Poppins', sans-serif;
  font-size: 1rem;
  transition: var(--transition);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  border-color: var(--primary-color);
  outline: none;
}

.form-group.error input,
.form-group.error textarea,
.form-group.error select {
  border-color: var(--danger-color);
}

.error-message {
  color: var(--danger-color);
  font-size: 0.9rem;
  margin-top: 5px;
}

/* Alert Messages */
.alert {
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 20px;
}

.alert-success {
  background-color: rgba(40, 167, 69, 0.1);
  border: 1px solid var(--success-color);
  color: var(--success-color);
}

.alert-danger {
  background-color: rgba(220, 53, 69, 0.1);
  border: 1px solid var(--danger-color);
  color: var(--danger-color);
}

/* Newsletter Form */
.newsletter-form {
  display: flex;
  margin-top: 20px;
}

.newsletter-form input {
  flex: 1;
  padding: 12px 15px;
  border: none;
  border-radius: 5px 0 0 5px;
  font-family: 'Poppins', sans-serif;
}

.newsletter-form button {
  border-radius: 0 5px 5px 0;
}
