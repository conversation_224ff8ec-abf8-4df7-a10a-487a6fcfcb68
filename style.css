/* Main Styles for North Texas SDA Church */

/* Locomotive Scroll Styles */
html.has-scroll-smooth {
    overflow: hidden;
}

html.has-scroll-dragging {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.has-scroll-smooth body {
    overflow: hidden;
}

/* Scroll Container */
.scroll-container {
    position: relative;
    width: 100%;
    overflow: hidden;
}

/* Scroll animations */
[data-scroll] {
    transition: opacity 1s, transform 1s;
}

[data-scroll="in"] {
    opacity: 1;
    transform: translateY(0);
}

[data-scroll="out"] {
    opacity: 0;
    transform: translateY(100px);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Montserrat', sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

.container {
    width: 100%;
    max-width: 1300px;
    margin: 0 auto;
    padding: 0 15px;
}

a {
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Bebas Neue', sans-serif;
    font-weight: 500;
    line-height: 1.2;
    margin-bottom: 20px;
}

h3{
    font-family: 'Barlow';
}

.section-title {
    font-size: 48px;
    text-transform: uppercase;
    color: #333;
}

.section-subtitle {
    font-size: 18px;
    margin-bottom: 40px;
    color: #666;
}

.highlight {
    color: #f7b731;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 8px 20px;
    font-weight: 600;
    font-size: 18px;
    text-transform: uppercase;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Barlow Condensed', sans-serif;
}

.btn-primary {
    background-color: #F95C28;
    color: #fff;
}

.btn-primary:hover {
    background-color: #e5a82a;
    border-color: #e5a82a;
}

.btn-secondary {
    background-color: transparent;
    color: #fff;
    border: 2px solid #fff;
}

.btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.btn-outline {
    background-color: transparent;
    color: #fff;
    border: 2px solid #fff;
}

.btn-outline:hover {
    background-color: #fff;
    color: #333;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    padding: 10px 0;
    transition: background-color 0.3s ease, padding 0.3s ease;
}

.header.scrolled {
    background-color: #1d1d1dd7;
    backdrop-filter: blur(15px);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo a{
    width: max-content !important;
    display: flex !important;
    align-items: center;
    gap: 10px;
}

.logo img {
    height: 50px;
}

.logo h1 {
    font-size: 18px;
    margin: 0;
    color: #fff;
    font-family: Oswald;
    text-transform: uppercase;
}

.nav-list {
    display: flex;
}

.nav-item {
    margin: 0 15px;
    position: relative;
}

.nav-link {
    color: #fff;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 14px;
    padding: 5px 0;
    position: relative;
}

.nav-link:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: #F95C28;
    transition: width 0.3s ease;
}

.nav-link:hover:after,
.nav-link.active:after {
    width: 100%;
}

/* Dropdown Menu */
.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    width: 200px;
    background-color: #1d1d1d;
    border-radius: 8px;
    padding: 10px 0;
    margin-top: 10px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    z-index: 1000;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: block;
    padding: 10px 20px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.05);
}



.header-buttons {
    display: flex;
    gap: 10px;
}

.mobile-toggle,
.mobile-close {
    display: none;
    font-size: 24px;
    color: #fff;
    cursor: pointer;
}

/* Hero Section */
.hero-section {
    height: 120vh;
    position: relative;
    display: flex;
    align-items: center;
    text-align: left;
    color: #fff;
    overflow: hidden;
}

.video-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.video-background video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
}

.hero-content {
    max-width: 600px;
    padding: 0 0px;
    margin-top: 80px;
}

.hero-title {
    font-size: 120px;
    font-weight: 400;
    line-height: 0.8;
    margin-bottom: 20px;
    text-transform: uppercase;
}

.hero-subtitle {
    font-size: 24px;
    margin-bottom: 40px;
    line-height: 1;
}

.hero-buttons {
    display: flex;
    gap: 15px;
}

/* Countdown Band */
.countdown-band{
    width: 100%;
    background-color: #ffc646;
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
}
.countdown-band p{
    font-family: 'lora';
    font-weight: 500;
}
.countdown-band .countdown-item{
    flex-direction: row;
    align-items: center;
    gap: 5px;
}
.countdown-band .countdown-label{
    margin-top: 0px;
    font-family: 'barlow';
    font-weight: 500;
    color: #1d1d1d;
    opacity: 100%;

}
.countdown-band .countdown-number{
    font-size: 20px;
    color: #1d1d1d;
}
.countdown-band .countdown-separator{
    font-size: 20px;
    padding: 0;
    margin: 0 10px;
    opacity: 100%;
    color: #1d1d1d;
}


/* Welcome Section */
.welcome-section {
    padding: 80px 0;
    background-color: #F8F1E6;
}

.welcome-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 50px;
    position: relative;
}

.welcome-image {
    display: none;
    width: 250px;
    height: 150px;
    object-fit: cover;
    border-radius: 12px;
    z-index: 1;
}

.welcome-content {
    width: 60%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.welcome-content h2{
    font-size: 120px;
    text-align: center;
    line-height: 1;
    margin-bottom: 20px;
    background: linear-gradient(170deg, #F95C28 6%, #1d1d1d 25%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.welcome-text {
    color: #1d1d1d;
    width: 80%;
    font-family: lora;
    text-align: center;
    line-height: 1.3;
    margin-bottom: 30px;
}

/* About Section */
.about-section {
    padding: 80px 0;
    background-color: #fff;
}

/* Mission, Vision, Values Section */
.mvv-section {
    max-width: 1300px;
    padding: 20px;
    margin: 5px auto;
    margin-bottom: 40px;
    border: 1px solid #e0e0e0;
    border-radius: 25px;
}

.mvv-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 20px;
    margin-bottom: 30px;
}

.mvv-box {
    flex: 1;
    min-width: 300px;
    border-radius: 10px;
    padding: 30px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
}

.mvv-box:not(:last-child)::after {
    content: "";
    position: absolute;
    top: 20%;
    right: 0;
    height: 60%;
    width: 1px;
    background-color: #e0e0e0;
}

.mvv-title {
    font-size: 35px;
    font-weight: 500;
    color: #222;
    margin-bottom: 15px;
}

.mvv-text {
    font-size: 18px;
    line-height: 1.2;
    color: #666;
    font-family: 'Lora';
}

.mvv-welcome {
    text-align: center;
    padding: 20px 0;
}

.mvv-welcome-text {
    font-size: 28px;
    font-weight: 400;
    font-family: 'Lora', serif;
    color: #333;
    font-style: italic;
}

.about-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 50px;
}

.about-image {
    flex: 1;
    max-width: 45%;
    height: 100vh;
    object-fit: cover;
    border-radius: 15px;
}

.about-image img {
    width: 100%;
    height: auto;
    object-fit: cover;
    border-radius: 8px;
}

.about-content {
    flex: 1;
    max-width: 45%;
}

.about-header {
    margin-bottom: 30px;
}

.about-subtitle {
    display: inline-block;
    background-color: #222;
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    padding: 8px 15px;
    margin-bottom: 20px;
    text-transform: uppercase;
    font-family: 'Oswald', sans-serif;
}

.about-title {
    font-size: 70px;
    line-height: 1;
    font-weight: 400;
    text-transform: uppercase;
    color: #222;
    font-family: 'Bebas Neue', sans-serif;
}

.about-text p {
    font-size: 16px;
    line-height: 1.8;
    color: #555;
    margin-bottom: 30px;
    font-family: 'Lora', serif;
}

.about-button {
    margin-top: 20px;
}

/* Services Section */
.services-section {
    padding: 80px 0;
    background-color: #fff;
}

.section-header {
    text-align: center;
    margin-bottom: 50px;
}

.services-container {
    display: flex;
    justify-content: space-between;
    gap: 30px;
}

.service-card {
    flex: 1;
    text-align: center;
    padding: 30px;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.service-icon {
    font-size: 40px;
    color: #f7b731;
    margin-bottom: 20px;
}

.service-title {
    font-size: 24px;
    margin-bottom: 10px;
}

.service-time {
    font-weight: 600;
    margin-bottom: 10px;
}

.service-description {
    color: #666;
}

/* Events Section */
.events-section {
    padding: 80px 0;
    background-color: #f9f9f9;
}

.events-container {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin-bottom: 40px;
}

.event-card {
    flex: 1;
    min-width: 300px;
    display: flex;
    background-color: #fff;
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.event-date {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    background-color: #f7b731;
    color: #fff;
    min-width: 80px;
}

.event-month {
    font-size: 16px;
    font-weight: 600;
}

.event-day {
    font-size: 28px;
    font-weight: 700;
}

.event-details {
    padding: 20px;
    flex: 1;
}

.event-title {
    font-size: 20px;
    margin-bottom: 10px;
}

.event-time, .event-location {
    color: #666;
    margin-bottom: 5px;
}

.event-time i, .event-location i {
    margin-right: 5px;
    color: #f7b731;
}

.section-button {
    text-align: center;
}

/* Sermons Section */
.sermons-section {
    padding: 80px 0;
    background-color: #fff;
}

.sermons-container {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin-bottom: 40px;
}

.sermon-card {
    flex: 1;
    min-width: 300px;
    background-color: #fff;
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.sermon-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.sermon-thumbnail {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.sermon-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.sermon-play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background-color: rgba(247, 183, 49, 0.8);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.sermon-play-button:hover {
    background-color: #f7b731;
}

.sermon-details {
    padding: 20px;
}

.sermon-title {
    font-size: 20px;
    margin-bottom: 10px;
}

.sermon-speaker, .sermon-date {
    color: #666;
    margin-bottom: 5px;
}

/* Featured Sermons Section */
.featured-sermons-section {
    padding: 80px 0;
    background-color: #3e4e3e;
    color: #fff;
}

.featured-sermons-label {
    display: inline-block;
    background-color: #fff;
    color: #333;
    font-size: 14px;
    font-weight: 600;
    padding: 8px 15px;
    margin-bottom: 20px;
    text-transform: uppercase;
    font-family: 'Oswald', sans-serif;
}

.featured-sermons-title {
    font-size: 80px;
    line-height: 1;
    text-transform: uppercase;
    margin-bottom: 50px;
    color: #fff;
    font-family: 'Bebas Neue', sans-serif;
}

.featured-sermons-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
}

.featured-sermon-card {
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.featured-sermon-card:hover {
    transform: translateY(-10px);
}

.featured-sermon-thumbnail {
    position: relative;
    height: 280px;
    overflow: hidden;
    border-radius: 10px;
}

.featured-sermon-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.featured-sermon-date {
    font-size: 12px;
    text-transform: uppercase;
    color: #ccc;
    margin: 15px 0 10px;
    font-weight: 600;
    letter-spacing: 1px;
}

.featured-sermon-title {
    font-size: 22px;
    margin-bottom: 15px;
    color: #fff;
    font-weight: 500;
    line-height: 1.3;
}

.featured-sermon-author {
    display: flex;
    align-items: center;
    margin-top: 15px;
}

.featured-sermon-author-image {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 10px;
    background-color: #F95C28;
}

.featured-sermon-author-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.featured-sermon-author-name {
    font-size: 14px;
    color: #ccc;
}

/* Countdown Timer Section */
.countdown-section {
    position: relative;
    padding: 100px 0;
    background: url('./images/timeBG.png');
    color: #fff;
    text-align: center;
    overflow: hidden;
}

.countdown-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('images/countdown-bg.jpg');
    background-size: cover;
    background-position: center;
    opacity: 0.6;
    z-index: 1;
}

.countdown-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.7));
    z-index: 2;
}

.countdown-container {
    position: relative;
    z-index: 3;
    max-width: 800px;
    margin: 0 auto;
}

.countdown-title {
    font-size: 32px;
    margin-bottom: 50px;
    font-weight: 400;
}

.countdown-timer {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 50px;
}

.countdown-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.countdown-number {
    font-size: 120px;
    font-weight: 700;
    line-height: 1;
    font-family: 'Bebas Neue', sans-serif;
}

.countdown-separator {
    font-size: 120px;
    line-height: 1;
    margin: 0 5px;
    opacity: 0.7;
    font-weight: 300;
    align-self: flex-start;
    padding-top: 10px;
}

.countdown-label {
    font-size: 14px;
    text-transform: uppercase;
    margin-top: 10px;
    letter-spacing: 1px;
    opacity: 0.8;
}

.countdown-button {
    display: inline-block;
    background-color: #ed5a2f;
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    text-transform: uppercase;
    padding: 15px 30px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.countdown-button:hover {
    background-color: #e04f30;
}

/* Leadership Team Section - New Style */
.leadership-section {
    padding: 80px 0;
    background-color: #f8f8f8;
}

.leadership-label {
    display: inline-block;
    background-color: #222;
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    padding: 8px 15px;
    margin-bottom: 20px;
    text-transform: uppercase;
    font-family: 'Oswald', sans-serif;
}

.leadership-title {
    font-size: 80px;
    line-height: 1;
    text-transform: uppercase;
    margin-bottom: 30px;
    font-family: 'Bebas Neue', sans-serif;
    background: linear-gradient(to right, #222 0%, #8B4513 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
}

.meet-team-link {
    display: inline-flex;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    color: #222;
    margin-bottom: 50px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.meet-team-link:hover {
    color: #8B4513;
}

.meet-team-link svg {
    margin-left: 5px;
    transition: all 0.3s ease;
}

.meet-team-link:hover svg {
    transform: translateX(5px);
}

.leadership-container {
    display: flex;
    gap: 30px;
}

.leadership-container .about-container{
/* width: 70%; */
justify-content: left;
}

.leader-card {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    height: 550px;
    transition: all 0.3s ease;
    flex: 0.5;
}

.leader-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.leader-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.leader-info {
    position: absolute;
    bottom: 0;
    left: 0;
    padding: 20px;
    width: 100%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
}

.leader-name {
    font-size: 24px;
    font-weight: 600;
    color: #fff;
    margin-bottom: 5px;
    font-family: 'Barlow';
}

.leader-role {
    font-size: 14px;
    color: #ccc;
    text-transform: uppercase;
}

/* Donation Section */
.donation-section {
    padding: 100px 0;
    position: relative;
    color: #fff;
    overflow: hidden;
}

.donation-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://images.unsplash.com/photo-1542810634-71277d95dcbb?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D');
    background-size: cover;
    background-position: center;
    filter: brightness(0.7);
    z-index: -1;
}

.donation-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 50px;
}

.donation-left {
    flex: 1;
    max-width: 550px;
}

.donation-title {
    font-size: 80px;
    font-weight: 700;
    margin-bottom: 30px;
    line-height: 1;
    font-family: 'Bebas Neue', sans-serif;
}

.donation-description {
    font-size: 18px;
    line-height: 1.6;
    margin-bottom: 40px;
    opacity: 0.9;
}

.donation-more-link {
    display: inline-flex;
    align-items: center;
    color: #fff;
    font-weight: 600;
    font-size: 16px;
    text-transform: uppercase;
    transition: all 0.3s ease;
}

.donation-more-link svg {
    margin-left: 10px;
    transition: transform 0.3s ease;
}

.donation-more-link:hover {
    color: #F95C28;
}

.donation-more-link:hover svg {
    transform: translateX(5px);
}

.donation-right {
    flex: 1;
    max-width: 450px;
}

.donation-card {
    background-color: #fff;
    border-radius: 10px;
    padding: 30px;
    color: #333;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.donation-card-title {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 15px;
    color: #333;
}

.donation-card-description {
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 25px;
    color: #666;
}

.donation-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    text-align: center;
}

.donation-stat {
    display: flex;
    flex-direction: column;
}

.donation-amount {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
}

.donation-label {
    font-size: 14px;
    color: #666;
}

.donation-progress {
    margin-bottom: 25px;
}

.donation-progress-bar {
    height: 10px;
    background-color: #f0f0f0;
    border-radius: 5px;
    position: relative;
    margin-bottom: 10px;
}

.donation-progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 79.5%; /* $795 of $1000 = 79.5% */
    background-color: #F95C28;
    border-radius: 5px;
}

.donation-progress-labels {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #666;
}

.donation-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 15px;
    background-color: #F95C28;
    color: #fff;
    border-radius: 5px;
    font-weight: 600;
    font-size: 16px;
    text-transform: uppercase;
    margin-bottom: 20px;
    transition: background-color 0.3s ease;
}

.donation-button svg {
    margin-left: 10px;
    transition: transform 0.3s ease;
}

.donation-button:hover {
    background-color: #e5482a;
}

.donation-button:hover svg {
    transform: translateX(5px);
}

.donation-secure {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #666;
}

.donation-secure svg {
    margin-right: 8px;
    color: #333;
}

/* Get Involved Section - New Style */
.get-involved-section {
    padding: 80px 0;
    background-color: #fff;
    text-align: center;
}

.get-involved-title {
    font-size: 100px;
    line-height: 0.9;
    text-transform: uppercase;
    margin-bottom: 30px;
    font-family: 'Bebas Neue', sans-serif;
    background: linear-gradient(to right, #222 0%, #8B4513 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.get-involved-description {
    font-size: 18px;
    line-height: 1.6;
    color: #1d1d1d;
    max-width: 700px;
    margin: 0 auto 60px;
}

.involvement-options {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.involvement-card {
    background-color: #F8F1E6;
    padding: 40px 30px;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.involvement-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.involvement-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 30px;
    color: #222;
}

.involvement-image-container {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto 30px;
}

.involvement-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.involvement-description {
    font-size: 16px;
    line-height: 1.6;
    color: #555;
}

/* FAQ Section - New Style */
.faq-section {
    padding: 80px 0;
    background-color: #fff;
}

.faq-container {
    display: flex;
    flex-wrap: wrap;
}

.faq-left {
    flex: 0 0 40%;
    padding-right: 40px;
}

.faq-right {
    flex: 0 0 60%;
}

.faq-label {
    display: inline-block;
    background-color: #222;
    color: #fff;
    padding: 8px 15px;
    font-size: 14px;
    text-transform: uppercase;
    font-weight: 600;
    margin-bottom: 20px;
    letter-spacing: 1px;
}

.faq-title {
    font-size: 60px;
    line-height: 1;
    text-transform: uppercase;
    margin-bottom: 30px;
    font-family: 'Bebas Neue', sans-serif;
    color: #222;
}

.faq-contact {
    display: inline-flex;
    align-items: center;
    color: #222;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 14px;
    margin-top: 20px;
    transition: all 0.3s ease;
}

.faq-contact svg {
    margin-left: 8px;
    transition: all 0.3s ease;
}

.faq-contact:hover {
    color: #ed5a2f;
}

.faq-contact:hover svg {
    transform: translateX(5px);
}

.faq-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.faq-item {
    border-bottom: 1px solid #eee;
}

.faq-question {
    position: relative;
    padding: 25px 40px 25px 0;
    cursor: pointer;
    font-size: 20px;
    font-weight: 600;
    color: #222;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
    font-family: 'barlow condensed';
}

.faq-question:hover  {
    color: #ed5a2f;
    padding-left: 10px;
}

.faq-question-text {
    flex: 1;
}

.faq-icon {
    width: 20px;
    height: 20px;
    position: relative;
}

.faq-icon::before,
.faq-icon::after {
    content: '';
    position: absolute;
    background-color: #222;
    transition: all 0.3s ease;
}

.faq-icon::before {
    width: 100%;
    height: 2px;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
}

.faq-icon::after {
    width: 2px;
    height: 100%;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
}

.faq-item.active .faq-icon::after {
    transform: translateX(-50%) rotate(90deg);
    opacity: 0;
    /* background-color: #ed5a2f; */
}

.faq-item.active .faq-question {
    color: #ed5a2f;
    font-size: 20px;
}

.faq-item.active .faq-icon::before {
    background-color: #ed5a2f;
    font-size: 20px;
}

.faq-answer {
    padding: 0 0 25px;
    font-size: 16px;
    line-height: 1.6;
    color: #555;
    display: none;
}

.faq-item.active .faq-answer {
    display: block;
}

/* Ministries Section */
.ministries-section {
    padding: 80px 0;
    background: url('./images/ministryBg.png');
    color: #fff;
}

.ministries-section .section-title {
    color: #fff;
    font-size: 100px;
}

.ministries-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 40px;
    justify-content: center;
}

.ministry-card {
    flex: 1;
    min-width: 250px;
    max-width: 300px;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.ministry-card:hover {
    transform: translateY(-10px);
}

.ministry-image {
    position: relative;
    height: 400px;
    overflow: hidden;
    border-radius: 10px;
}

.ministry-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.ministry-card:hover .ministry-image img {
    transform: scale(1.05);
}

.ministry-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0.7) 100%);
}

.ministry-title {
    position: absolute;
    bottom: 60px;
    left: 20px;
    color: #fff;
    font-size: 24px;
    font-weight: 600;
    margin: 0;
    z-index: 2;
}

.ministry-link {
    position: absolute;
    bottom: 20px;
    left: 20px;
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    z-index: 2;
    display: flex;
    align-items: center;
    gap: 5px;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.ministry-link:hover {
    opacity: 1;
}

.ministry-link i {
    font-size: 12px;
    transition: transform 0.3s ease;
}

.ministry-link:hover i {
    transform: translateX(5px);
}

.ministries-section .section-button {
    text-align: center;
}

.ministries-section .btn-primary {
    padding: 12px 30px;
}

.ministries-section .btn-primary:hover {
    background-color: #fff;
    color: #222;
    border-color: #fff;
}

/* Team Section */
.team-section {
    padding: 80px 0;
    background-color: #f9f9f9;
}

.team-container {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin-bottom: 40px;
}

.team-member {
    flex: 1;
    min-width: 250px;
    text-align: center;
    background-color: #fff;
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.team-member:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.team-member-image {
    height: 250px;
    overflow: hidden;
}

.team-member-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.team-member-name {
    font-size: 20px;
    margin: 20px 0 5px;
}

.team-member-role {
    color: #666;
    margin-bottom: 20px;
}

/* Footer */
.footer {
    background-color: #1a1e23;
    color: #fff;
    padding: 80px 0 30px;
    position: relative;
    z-index: 10;
}

.footer-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 40px;
    margin-bottom: 60px;
}

.footer-column {
    display: flex;
    flex-direction: column;
}

.footer-title {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 25px;
    color: #fff;
}

.footer-text {
    font-size: 16px;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 25px;
}

.footer-subscribe {
    display: flex;
    margin-top: 10px;
}

.footer-input {
    flex: 1;
    padding: 12px 15px;
    background-color: transparent;
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #fff;
    font-size: 16px;
}

.footer-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.footer-button {
    background-color: #ffc107;
    color: #1a1e23;
    border: none;
    padding: 0 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    font-size: 14px;
}

.footer-button:hover {
    background-color: #e0aa00;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-link-item {
    margin-bottom: 15px;
}

.footer-link {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 16px;
}

.footer-link:hover {
    color: #fff;
}

.footer-address {
    font-size: 16px;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 5px;
}

.footer-contact-info {
    margin-bottom: 25px;
}

.footer-contact-item {
    font-size: 16px;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 10px;
}

.footer-social {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.footer-social-link {
    color: #fff;
    font-size: 18px;
    transition: all 0.3s ease;
}

.footer-social-link:hover {
    color: #ffc107;
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-logo {
    display: flex;
    align-items: center;
}

.footer-logo img {
    height: 50px;
    margin-right: 15px;
}

.footer-logo-text {
    display: flex;
    flex-direction: column;
    text-transform: uppercase;
    line-height: 1.2;

}

.footer-logo-title {
    font-weight: 600;
    font-size: 18px;
    font-family: 'Oswald';
}

.footer-logo-subtitle {
    font-weight: 600;
    font-size: 18px;
    font-family: 'Oswald';
}

.footer-copyright {
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.5);
    font-size: 14px;
}

.footer-copyright span {
    margin: 0 10px;
}

.footer-developer {
    color: rgba(255, 255, 255, 0.7);
}

.footer-developer a {
    color: #ffc107;
    text-decoration: none;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 40px;
    height: 40px;
    background-color: #ed5a2f;
    color: #fff;
    border-radius: 5%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 999;
}

.back-to-top.active {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background-color: #e5a82a;
}

/* Modern Events Section */
.modern-events-section {
    padding: 80px 0;
    background-color: #fff;
}

.modern-events-header {
    text-align: center;
    margin-bottom: 50px;
}

.modern-events-title {
    font-size: 120px;
    line-height: 0.9;
    text-transform: uppercase;
    margin-bottom: 30px;
    background: linear-gradient(to right, #222 0%, #222 70%, #F95C28 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    display: inline-block;
}

.modern-events-subtitle {
    font-size: 18px;
    color: #252628;
    max-width: 800px;
    margin: 0 auto;
    font-family: 'Lora', serif;
    line-height: 1.6;
}

.modern-events-container {
    display: flex;
    flex-direction: column;
    margin-bottom: 40px;
}

.modern-event-card {
    display: flex;
    align-items: center;
    justify-content: space-between !important;
    background-color: #fff;
    border-radius: 15px;
    padding: 20px 40px;
    transition: all 0.3s ease;
    margin-bottom: 5px;
    border: 1px solid #dedede;
}

.modern-event-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.modern-event-left {
    display: flex;
    align-items: center;
    margin-right: 30px;
    gap: 20px;
}

.modern-event-date {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    margin-bottom: 10px;
}

.modern-event-day-name {
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    color: #1d1d1d;
    margin-bottom: 5px;
    font-family: 'Barlow', sans-serif;
}

.modern-event-day {
    font-size: 60px;
    font-weight: 700;
    line-height: 1;
    color: #1d1d1d;
}

.modern-event-month {
    font-size: 14px;
    font-weight: 500;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-top: 2px;
}

.modern-event-image {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
}

.modern-event-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.modern-event-content {
    width: 50%;
    padding: 0 20px;
}

.modern-event-title {
    font-size: 24px;
    margin-bottom: 10px;
    color: #252628;
    font-weight: 400;
    font-family: 'bebas neue';
}

.modern-event-description {
    font-size: 18px;
    color: #252628;
    margin-bottom: 15px;
    font-family: 'Lora', serif;
    line-height: 1.5;
}

.modern-event-details {
    margin-top: 10px;
}

.modern-event-time {
    font-size: 14px;
    color: #1d1d1d;
    font-weight: 700;
}

.modern-event-action {
    display: flex;
    align-items: center;
}

.modern-event-button {
    display: inline-block;
    padding: 8px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    font-weight: 600;
    color: #333;
    text-transform: uppercase;
    transition: all 0.3s ease;
    background-color: transparent;
}

.modern-event-button:hover {
    background-color: #f5f5f5;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .hero-title {
        font-size: 60px;
    }

    .welcome-container {
        flex-direction: column;
    }

    .welcome-image, .welcome-content {
        flex: none;
        width: 100%;
    }

    .services-container {
        flex-wrap: wrap;
    }

    .service-card {
        min-width: 250px;
    }

    .modern-events-title {
        font-size: 80px;
    }
}

@media (max-width: 768px) {
  .header-container {
    flex-direction: row;
    align-items: center;
  }
  .logo h1 {
    font-size: 14px;
  }
  .nav-menu {
    position: fixed;
    top: 0;
    right: -100%;
    width: 80%;
    max-width: 300px;
    height: 100vh;
    background-color: #404F40;
    padding: 50px 20px;
    transition: all 0.3s ease;
    z-index: 1001;
  }
  .nav-menu.active {
    right: 0;
  }
  .nav-list {
    flex-direction: column;
    padding: 4rem 0;
  }
  .nav-item {
    margin: 10px 0;
  }
  .nav-item a{
    font-size: 30px;
    font-family: 'barlow condensed';
    line-height: 1;
  }
  .mobile-toggle {
    display: block;
    background-color: #e04f30;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 5px;
  }
  .mobile-close {
    display: block;
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 24px;
    cursor: pointer;
  }
  .header-buttons {
    display: none;
  }
  .hero-section {
    height: 70vh ;
    min-height: 70vh;
    padding: 40px 0 0 0;
    text-align: center;
  }
  .hero-title {
    font-size: 50px;
    line-height: 1;
  }
  .hero-content {
    margin-top: 40px;
    padding: 0 10px;
  }
  .hero-buttons {
    flex-direction: column;
    gap: 10px;
    align-items: center;
  }
  .video-background video {
    height: 70vh;
    object-fit: cover;
  }
  .countdown-band{
    display: none;
  }
  .welcome-section {
    padding: 40px 0;
  }
  .welcome-container {
    flex-direction: column;
    gap: 20px;
  }
  .welcome-image {
    display: block;
    width: 100% !important;
    height: 120px !important;
    margin-bottom: 20px;
  }
  .welcome-content h2.section-title {
    font-size: 40px;
  }
  .welcome-text {
    width: 100%;
    font-size: 15px;
  }
  .about-container {
    flex-direction: column;
    gap: 20px;
  }
  .about-image, .about-content {
    max-width: 100%;
    height: auto;
  }
  .about-title {
    font-size: 50px;
  }
  .mvv-section{
    max-width: 90%;
  }
  .mvv-container {
    flex-direction: column;
    gap: 20px;
  }
  .mvv-box {
    min-width: 100%;
    padding: 20px 10px;
  }
  .mvv-title {
    font-size: 18px;
  }
  .mvv-box:not(:last-child)::after {
    content: "";
    position: absolute;
    top: 110%;
    right: 20%;
    width: 60%;
    height: 1px;
    background-color: #e0e0e0;
}
  .ministries-container {
    flex-direction: column;
    gap: 20px;
  }
  .ministries-section .section-title {
    color: #fff;
    font-size: 70px;
}
  .ministry-card {
    max-width: 100%;
  }
  .ministry-title {
    font-size: 35px;
  }
  .modern-events-title {
    font-size: 70px;
  }
  .modern-events-container {
    gap: 10px;
  }
  .modern-event-card {
    flex-direction: column;
    align-items: center;
    padding: 20px;
    gap: 10px;

  }
  .modern-event-left {
    flex-direction: row;
    width: 100%;
    margin-right: 0;
    margin-bottom: 10px;
    display: none;
  }
  .modern-event-date {
    margin-right: 10px;
    margin-bottom: 0;
  }
  .modern-event-day {
    font-size: 24px;
  }
  .modern-event-image {
    width: 200px;
    height: 200px;
  }
  .modern-event-content {
    padding: 0;
    margin-bottom: 10px;
    text-align: center;
    width: 100%;
  }
  .modern-event-action {
    margin-left: 0;
    /* align-self: flex-end; */
  }
  .featured-sermons-title {
    font-size: 50px;
  }
  .featured-sermons-container {
    grid-template-columns: 1fr;
  }
  .countdown-title {
    font-size: 18px;
    margin-bottom: 20px;
  }
  .countdown-timer {
    gap: 5px;
  }
  .countdown-number {
    font-size: 32px;
  }
  .countdown-separator {
    font-size: 32px;
  }
  .countdown-label {
    font-size: 10px;
  }
  .leadership-title {
    font-size: 50px;
  }
  .leadership-container {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .leader-card {
    height: 380px;
    flex: 1;
  }
  .leader-name {
    font-size: 18px;
  }
  .donation-container {
    flex-direction: column;
    gap: 10px;
  }
  .donation-left, .donation-right {
    max-width: 100%;
  }
  .donation-title {
    font-size: 50px;
  }
  .donation-description {
    font-size: 13px;
  }
  .get-involved-title {
    font-size: 50px;
  }
  .involvement-options {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  .involvement-card {
    padding: 20px 30px;
  }
  .involvement-image-container {
    width: 150px;
    height: 150px;
  }
  .faq-container {
    flex-direction: column;
    align-items: center;
  }
  .faq-left, .faq-right {
    flex: 0 0 100%;
    padding-right: 0;
  }
  .faq-left {
    margin-bottom: 10px;
  }
  .faq-title {
    font-size: 50px;
  }
  .faq-question {
    font-size: 18px;
    padding: 10px 5px 10px 0;
  }
  .footer {
    padding: 40px 10px;
  }
  .footer-container {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  .footer-column {
    padding: 0 2px;
  }
  .footer-title {
    font-size: 25px;
    margin-bottom: 5px;
  }
  .footer-subscribe {
    flex-direction: column;
    padding: 10px;
  }
  .footer-input {
    margin-bottom: 5px;
    width: 100%;
  }
  .footer-button {
    width: 100%;
    padding: 15px 0;
    margin-bottom: 20px;
  }
  .footer-bottom {
    flex-direction: column;
    text-align: center;
    gap: 5px;
  }
  .footer-link-item, .footer-contact-item{
    margin-bottom: 0px;
  }
  .footer-logo {
    justify-content: center;
    margin-bottom: 5px;
  }
  .footer-logo-subtitle{
    font-size: 20px;
  }
  .footer-copyright {
    flex-direction: column;
    gap: 2px;
  }
  .footer-copyright span {
    margin: 1px 0;
  }
}


/* Youth Ministry Page Styles */
.page-header .gradient-title,
.youth-hero-section .section-title.gradient-title,
.youth-invite-section .section-title.gradient-title {
  background: linear-gradient(-65deg, #F95C28 16%, #1d1d1d 30%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.youth-hero-section {
  padding: 60px 0 40px 0;
  background: #f8fafc;
}
.youth-hero-content {
  display: flex;
  flex-wrap: wrap;
  /* align-items: center; */
  gap: 40px;
}
.youth-hero-text {
  flex: 1 1 350px;
}

.youth-hero-text h2{
    font-size: 100px;
    line-height: 1;
}
.section-description{
    font-size: 1.1rem;
    font-family: 'lora';
}
.youth-hero-image {
  flex: 1 2 300px;
  text-align: center;
}
.youth-hero-image img {
  max-width: 550px;
  border-radius: 18px;
}

.youth-programs-section {
  background: #fff;
  padding: 60px 0 40px 0;
}
.youth-programs-header {
  text-align: center;
  margin-bottom: 3rem;
  /* font-size: 80px; */
}

.youth-programs-header .section-title{
    font-size: 80px;
    color: #1d1d1d;
    line-height: 1;
    margin-bottom: 0px;

}
.youth-programs-container {
  display: flex;
  flex-wrap: wrap;
  gap: 32px;
  justify-content: center;
}
.youth-program-card {
  /* background: #f8fafc; */
  border-radius: 20px;
  /* box-shadow: 0 4px 24px rgba(30,144,255,0.07); */
  padding: 20px;
  max-width: 340px;
  flex: 1 1 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: box-shadow 0.2s;
}
.youth-program-card:hover {
  width: 38%;
}
.youth-program-img {
  width: 100%;
  border-radius: 12px;
  margin-bottom: 18px;
  /* box-shadow: 0 2px 12px rgba(0,0,0,0.07); */
}
.youth-program-title {
  font-family: 'Bebas neue', Arial, sans-serif;
  font-size: 1.5rem;
  margin: 10px 0 8px 0;
  color: #1d1d1d;
  font-weight: 500;
}
.youth-program-title a {
  color: inherit;
  text-decoration: none;
}
.youth-program-desc {
  font-size: 1rem;
  color: #1d1d1d;
  margin-bottom: 10px;
  font-family: 'barlow';
}
.adventurer-law {
  margin: 10px;
  width: 100%;
  /* padding-left: 18px; */
  font-size: 0.98rem;
  color: #1d1d1d;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}
.adventurer-law h3{
    width: 100%;
    font-size: 20px;
    color: #1d1d1d;
    font-weight: 600;
    margin-bottom: 10px;
    font-family: 'Barlow condensed';
}
.adventurer-law li{
    padding: 5px 10px;
    background-color: #f8f1e6;
    border-radius: 25px;
    font-size: 16px;
    font-family: 'barlow';
}
.adventurer-pledge {
  font-size: 0.95rem;
  color: #1d1d1d;
  margin-bottom: 0;
}

.youth-invite-section {
  background: #f8f1e6;
  padding: 4em;
}
.youth-invite-content {
  text-align: center;
  max-width: 700px;
  margin: 0 auto;
}

.youth-invite-content .section-title{
    font-size: 80px;
    color: #1d1d1d;
}
.youth-invite-imgs {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-top: 24px;
}
.youth-invite-imgs img {
  width: 180px;
  height: 120px;
  object-fit: cover;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(30,144,255,0.10);
}

@media (max-width: 900px) {
.ministries-page .section-title{
    width: 100%;
    font-size: 50px;
    line-height: 1;
}
.youth-hero-text{
    width: 100%;
}
.youth-hero-image img{
    width: 100%;
    max-width: 100%;
}
  .youth-hero-content {
    flex-direction: column;
    gap: 24px;
  }
  .youth-programs-container {
    flex-direction: column;
    gap: 24px;
    align-items: center;
  }
  .youth-program-card{
    width: 100%;
    max-width: 100%;
    padding: 5px;
    line-height: 1;
    text-align: left !important;
  }
  .youth-program-title{
    text-align: left;
  }
  .youth-invite-section{
    padding: 4rem 2rem;
  }
  .youth-invite-imgs {
    flex-direction: column;
    gap: 16px;
    align-items: center;
  }
}

/* Prayer Request Modal */
.prayer-request-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    display: none;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.prayer-request-modal.active {
    display: flex;
    opacity: 1;
}

.prayer-modal-container {
    width: 95%;
    height: 95vh;
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
    display: flex;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.prayer-modal-close {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 20px;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s ease;
}

.prayer-modal-close:hover {
    background-color: rgba(255, 255, 255, 0.4);
    transform: rotate(90deg);
}

.prayer-modal-content {
    display: flex;
    width: 100%;
    height: 100%;
}

.prayer-modal-image {
    flex: 1;
    position: relative;
    overflow: hidden;
}

.prayer-modal-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.prayer-image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.4));
}

.prayer-image-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #fff;
    width: 80%;
}

.prayer-image-text h2 {
    font-size: 50px;
    font-weight: 500;
    margin-bottom: 20px;
    font-family: 'Bebas neue', serif;
}

.prayer-image-text p {
    font-size: 18px;
    line-height: 1.6;
    font-style: italic;
}

.prayer-modal-form {
    flex: 1;
    padding: 40px;
    overflow-y: auto;
}

.prayer-form-title {
    font-size: 35px;
    font-weight: 700;
    margin-bottom: 10px;
    color: #333;
}

.prayer-form-subtitle {
    font-size: 16px;
    color: #666;
    margin-bottom: 30px;
}

.prayer-form .form-group {
    margin-bottom: 20px;
}

.prayer-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.prayer-form input,
.prayer-form select,
.prayer-form textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
    transition: all 0.3s ease;
}

.prayer-form input:focus,
.prayer-form select:focus,
.prayer-form textarea:focus {
    border-color: #F95C28;
    outline: none;
    box-shadow: 0 0 0 2px rgba(249, 92, 40, 0.2);
}

.prayer-form .checkbox-group {
    display: flex;
    align-items: center;
}

.prayer-form .checkbox-group input {
    width: auto;
    margin-right: 10px;
}

.prayer-form .checkbox-group label {
    margin-bottom: 0;
}

.prayer-form .form-submit {
    margin-top: 30px;
}

.prayer-form .form-message {
    margin-top: 20px;
    padding: 15px;
    border-radius: 5px;
    display: none;
}

.prayer-form .form-message.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    display: block;
}

.prayer-form .form-message.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    display: block;
}

/* Responsive styles for prayer modal */
@media (max-width: 992px) {
    .prayer-modal-content {
        flex-direction: column;
    }

    .prayer-modal-image {
        height: 300px;
    }

    .prayer-image-text h2 {
        font-size: 28px;
    }

    .prayer-image-text p {
        font-size: 16px;
    }

    .prayer-modal-form {
        padding: 20px;
    }
}
