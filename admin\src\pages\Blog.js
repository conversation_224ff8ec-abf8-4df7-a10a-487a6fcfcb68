import React, { useState, useEffect } from 'react';
import { Button, Table, Badge } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import mockApi from '../services/mockApi';

const Blog = () => {
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchPosts = async () => {
      try {
        setLoading(true);
        const data = await mockApi.getPosts();
        setPosts(data);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching posts:', err);
        setError('Failed to load blog posts');
        setLoading(false);
      }
    };

    fetchPosts();
  }, []);

  const getStatusBadge = (status) => {
    switch (status) {
      case 'published':
        return <Badge bg="success">Published</Badge>;
      case 'draft':
        return <Badge bg="secondary">Draft</Badge>;
      case 'archived':
        return <Badge bg="warning">Archived</Badge>;
      default:
        return <Badge bg="light">Unknown</Badge>;
    }
  };

  if (loading) {
    return <div className="text-center py-5">Loading blog posts...</div>;
  }

  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }

  return (
    <div>
      <div className="table-container">
        <div className="table-header">
          <h3 className="table-title">Blog Posts</h3>
          <div className="table-actions">
            <Button as={Link} to="/blog/new" variant="primary">
              <i className="fas fa-plus me-2"></i> Add Post
            </Button>
          </div>
        </div>

        {posts.length === 0 ? (
          <p className="text-center py-4">No blog posts found. Create your first post!</p>
        ) : (
          <Table responsive hover>
            <thead>
              <tr>
                <th>Title</th>
                <th>Author</th>
                <th>Category</th>
                <th>Published Date</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {posts.map(post => (
                <tr key={post.id}>
                  <td>{post.title}</td>
                  <td>{post.author}</td>
                  <td>{post.category}</td>
                  <td>{post.publishedAt ? new Date(post.publishedAt).toLocaleDateString() : 'Not published'}</td>
                  <td>{getStatusBadge(post.status)}</td>
                  <td>
                    <Button as={Link} to={`/blog/${post.id}`} variant="outline-primary" size="sm" className="me-2">
                      <i className="fas fa-edit"></i>
                    </Button>
                    <Button variant="outline-danger" size="sm">
                      <i className="fas fa-trash"></i>
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </Table>
        )}
      </div>
    </div>
  );
};

export default Blog;
