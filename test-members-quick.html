<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Members Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Quick Members API Test</h1>
    
    <button onclick="testAPI()">Test Members API</button>
    <button onclick="addTestMember()">Add Test Member</button>
    <button onclick="openDashboard()">Open Dashboard</button>
    
    <div id="status"></div>
    <div id="result"></div>

    <script>
        const API_URL = 'http://localhost:3000/api';

        async function testAPI() {
            const statusDiv = document.getElementById('status');
            const resultDiv = document.getElementById('result');
            
            statusDiv.innerHTML = '<div class="status info">Testing Members API...</div>';
            
            try {
                const response = await fetch(`${API_URL}/members`);
                console.log('Response status:', response.status);
                console.log('Response ok:', response.ok);
                
                if (response.ok) {
                    const members = await response.json();
                    console.log('Members data:', members);
                    
                    statusDiv.innerHTML = `<div class="status success">✅ API Working! Found ${members.length} members</div>`;
                    resultDiv.innerHTML = `<pre>${JSON.stringify(members, null, 2)}</pre>`;
                } else {
                    const errorText = await response.text();
                    statusDiv.innerHTML = `<div class="status error">❌ API Error: ${response.status} - ${errorText}</div>`;
                }
            } catch (error) {
                console.error('Error:', error);
                statusDiv.innerHTML = `<div class="status error">❌ Network Error: ${error.message}</div>`;
            }
        }

        async function addTestMember() {
            const statusDiv = document.getElementById('status');
            
            statusDiv.innerHTML = '<div class="status info">Adding test member...</div>';
            
            const testMember = {
                name: 'Test Member ' + Date.now(),
                phone: '(*************',
                address: '123 Test St, Test City, TX 12345',
                birthdate: '1990-01-01',
                memberSince: '2020-01-01',
                ministry: 'Worship Team',
                status: 'active',
                notes: 'Test member added via quick test'
            };
            
            try {
                const response = await fetch(`${API_URL}/members`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testMember)
                });
                
                if (response.ok) {
                    const newMember = await response.json();
                    statusDiv.innerHTML = '<div class="status success">✅ Test member added successfully!</div>';
                    console.log('New member:', newMember);
                    
                    // Refresh the list
                    setTimeout(testAPI, 1000);
                } else {
                    const errorText = await response.text();
                    statusDiv.innerHTML = `<div class="status error">❌ Failed to add member: ${response.status} - ${errorText}</div>`;
                }
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ Error adding member: ${error.message}</div>`;
            }
        }

        function openDashboard() {
            window.open('admin-dashboard.html', '_blank');
        }

        // Auto-test on page load
        window.addEventListener('load', function() {
            setTimeout(testAPI, 500);
        });
    </script>
</body>
</html>
