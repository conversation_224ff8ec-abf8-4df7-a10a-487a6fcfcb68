/*
* North Texas SDA Church - Main JavaScript
* Author: Augment Agent
* Version: 1.0
*/

document.addEventListener('DOMContentLoaded', function() {
    'use strict';

    // Header scroll class
    const header = document.querySelector('.header');
    const backToTop = document.querySelector('.back-to-top');

    // Prayer Request Modal
    const prayerRequestBtn = document.getElementById('prayer-request-btn');
    const prayerRequestModal = document.getElementById('prayer-request-modal');
    const prayerModalClose = document.getElementById('prayer-modal-close');
    const modalPrayerForm = document.getElementById('modal-prayer-form');
    const modalFormMessage = document.getElementById('modal-form-message');

    // Lenis scroll event for header background color change
    if (window.Lenis) {
        // Use the global lenis instance if available
        let lastScrollY = 0;
        function onLenisScroll() {
            const scrollY = window.scrollY || window.pageYOffset || document.documentElement.scrollTop;
            if (header) {
                if (scrollY > 50) {
                    header.classList.add('scrolled');
                } else {
                    header.classList.remove('scrolled');
                }
            }
            if (backToTop) {
                if (scrollY > 300) {
                    backToTop.classList.add('active');
                } else {
                    backToTop.classList.remove('active');
                }
            }
            lastScrollY = scrollY;
        }
        // Listen to Lenis scroll events
        window.addEventListener('scroll', onLenisScroll);
        // Initial check
        onLenisScroll();
    } else {
        // Fallback for browsers without Lenis
        window.addEventListener('scroll', function() {
            if (header) {
                if (window.scrollY > 50) {
                    header.classList.add('scrolled');
                } else {
                    header.classList.remove('scrolled');
                }
            }
            if (backToTop) {
                if (window.scrollY > 300) {
                    backToTop.classList.add('active');
                } else {
                    backToTop.classList.remove('active');
                }
            }
        });
    }

    // Back to top button click event
    if (backToTop) {
        backToTop.addEventListener('click', function(e) {
            e.preventDefault();
            if (scroll) {
                // Use Locomotive Scroll if it's initialized
                scroll.scrollTo(0);
            } else {
                // Fallback to standard scrolling
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            }
        });
    }

    // Mobile Navigation Toggle
    const mobileToggle = document.querySelector('.mobile-toggle');
    const mobileClose = document.querySelector('.mobile-close');
    const navMenu = document.querySelector('.nav-menu');

    if (mobileToggle && navMenu) {
        mobileToggle.addEventListener('click', function() {
            navMenu.classList.add('active');
        });
    }

    if (mobileClose && navMenu) {
        mobileClose.addEventListener('click', function() {
            navMenu.classList.remove('active');
        });
    }

    // Close mobile nav when clicking outside
    document.addEventListener('click', function(e) {
        if (navMenu && !navMenu.contains(e.target) &&
            mobileToggle && !mobileToggle.contains(e.target) &&
            navMenu.classList.contains('active')) {
            navMenu.classList.remove('active');
        }
    });

    // Handle dropdown menus on mobile
    const dropdownItems = document.querySelectorAll('.dropdown');

    if (window.innerWidth <= 768) {
        dropdownItems.forEach(item => {
            const link = item.querySelector('.nav-link');
            const menu = item.querySelector('.dropdown-menu');

            if (link && menu) {
                link.addEventListener('click', function(e) {
                    // Only prevent default if we're on mobile
                    if (window.innerWidth <= 768) {
                        e.preventDefault();
                        menu.style.opacity = menu.style.opacity === '1' ? '0' : '1';
                        menu.style.visibility = menu.style.visibility === 'visible' ? 'hidden' : 'visible';
                        menu.style.transform = menu.style.transform === 'translateY(0px)' ? 'translateY(10px)' : 'translateY(0px)';
                    }
                });
            }
        });
    }

    // Close mobile nav when clicking on nav links (except dropdown toggles on mobile)
    const navLinks = document.querySelectorAll('.nav-link:not(.dropdown > .nav-link), .dropdown-item');
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            if (navMenu) {
                navMenu.classList.remove('active');
            }
        });
    });

    // Active nav link based on current page
    const currentLocation = location.pathname.split('/').slice(-1)[0];
    navLinks.forEach(link => {
        const linkHref = link.getAttribute('href');
        if (linkHref === currentLocation || (currentLocation === '' && linkHref === 'index.html')) {
            link.classList.add('active');
        }
    });

    // Prayer Request Modal functionality
    if (prayerRequestBtn) {
        prayerRequestBtn.addEventListener('click', function(e) {
            e.preventDefault();
            prayerRequestModal.classList.add('active');
            document.body.style.overflow = 'hidden'; // Prevent scrolling when modal is open
        });
    }

    if (prayerModalClose) {
        prayerModalClose.addEventListener('click', function() {
            prayerRequestModal.classList.remove('active');
            document.body.style.overflow = ''; // Re-enable scrolling
        });
    }

    // Close modal when clicking outside the content
    if (prayerRequestModal) {
        prayerRequestModal.addEventListener('click', function(e) {
            if (e.target === prayerRequestModal) {
                prayerRequestModal.classList.remove('active');
                document.body.style.overflow = '';
            }
        });
    }

    // Handle prayer request form submission
    if (modalPrayerForm) {
        modalPrayerForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            // Get form values
            const name = document.getElementById('modal-name').value;
            const email = document.getElementById('modal-email').value;
            const phone = document.getElementById('modal-phone').value;
            const category = document.getElementById('modal-category').value;
            const request = document.getElementById('modal-request').value;
            const confidential = document.getElementById('modal-confidential').checked;
            const contactMe = document.getElementById('modal-contact-me').checked;

            // Validate form
            if (!name || !email || !category || !request) {
                showModalFormMessage('Please fill in all required fields.', 'error');
                return;
            }

            // Create prayer request object
            const prayerRequestData = {
                name,
                email,
                phone,
                category,
                request,
                confidential,
                contactMe,
                status: 'pending', // Default status
                createdAt: new Date().toISOString()
            };

            try {
                // Show loading message
                showModalFormMessage('Submitting your prayer request...', 'info');

                // Submit prayer request to API
                const response = await fetch('http://localhost:3000/api/prayer-requests', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(prayerRequestData)
                });

                if (!response.ok) {
                    throw new Error('Failed to submit prayer request');
                }

                // Show success message
                showModalFormMessage('Your prayer request has been submitted successfully. Our prayer team will be praying for you.', 'success');

                // Reset form
                modalPrayerForm.reset();

                // Close modal after a delay
                setTimeout(() => {
                    prayerRequestModal.classList.remove('active');
                    document.body.style.overflow = '';
                }, 3000);

            } catch (err) {
                console.error('Error submitting prayer request:', err);
                showModalFormMessage('There was an error submitting your prayer request. Please try again later.', 'error');
            }
        });
    }

    // Function to show form messages in the modal
    function showModalFormMessage(message, type) {
        if (modalFormMessage) {
            modalFormMessage.textContent = message;
            modalFormMessage.className = 'form-message';

            if (type === 'success') {
                modalFormMessage.classList.add('success');
            } else if (type === 'error') {
                modalFormMessage.classList.add('error');
            } else if (type === 'info') {
                modalFormMessage.style.backgroundColor = '#d1ecf1';
                modalFormMessage.style.color = '#0c5460';
                modalFormMessage.style.border = '1px solid #bee5eb';
                modalFormMessage.style.display = 'block';
            }
        }
    }

    // Countdown Timer Functionality
    function updateCountdown() {
        // Set the target date to the next Sunday at 11:00 AM
        const now = new Date();
        const dayOfWeek = now.getDay(); // 0 is Sunday, 1 is Monday, etc.
        const daysUntilNextSunday = dayOfWeek === 0 ? 7 : 7 - dayOfWeek;

        const nextSunday = new Date(now);
        nextSunday.setDate(now.getDate() + daysUntilNextSunday);
        nextSunday.setHours(11, 0, 0, 0); // 11:00 AM

        // Calculate the time difference
        const timeRemaining = nextSunday - now;

        // Calculate days, hours, minutes, and seconds
        const days = Math.floor(timeRemaining / (1000 * 60 * 60 * 24));
        const hours = Math.floor((timeRemaining % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((timeRemaining % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((timeRemaining % (1000 * 60)) / 1000);

        // Update the countdown elements
        const daysElement = document.getElementById('countdown-days');
        const hoursElement = document.getElementById('countdown-hours');
        const minutesElement = document.getElementById('countdown-minutes');
        const secondsElement = document.getElementById('countdown-seconds');

        if (daysElement) daysElement.textContent = days.toString().padStart(2, '0');
        if (hoursElement) hoursElement.textContent = hours.toString().padStart(2, '0');
        if (minutesElement) minutesElement.textContent = minutes.toString().padStart(2, '0');
        if (secondsElement) secondsElement.textContent = seconds.toString().padStart(2, '0');
    }

    // Update the countdown every second
    if (document.querySelector('.countdown-timer')) {
        updateCountdown(); // Initial update
        setInterval(updateCountdown, 1000); // Update every second
    }

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            if (this.getAttribute('href') !== '#') {
                e.preventDefault();

                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);

                if (targetElement) {
                    // Check if the target is inside the scroll container
                    const scrollContainer = document.querySelector('[data-scroll-container]');
                    const isTargetInScrollContainer = scrollContainer && scrollContainer.contains(targetElement);

                    if (scroll && isTargetInScrollContainer) {
                        // Use Locomotive Scroll to scroll to the target element
                        scroll.scrollTo(targetElement, {
                            offset: -80, // Adjust for header height
                            duration: 1000,
                            easing: [0.25, 0.0, 0.35, 1.0] // Smooth easing function
                        });
                    } else {
                        // Fallback to standard scrolling
                        window.scrollTo({
                            top: targetElement.offsetTop - 80, // Adjust for header height
                            behavior: 'smooth'
                        });
                    }
                }
            }
        });
    });

    // Contact form validation
    const contactForm = document.querySelector('#contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Basic validation
            let isValid = true;
            const name = document.querySelector('#name');
            const email = document.querySelector('#email');
            const subject = document.querySelector('#subject');
            const message = document.querySelector('#message');

            if (name && name.value.trim() === '') {
                showError(name, 'Name is required');
                isValid = false;
            } else if (name) {
                removeError(name);
            }

            if (email && email.value.trim() === '') {
                showError(email, 'Email is required');
                isValid = false;
            } else if (email && !isValidEmail(email.value)) {
                showError(email, 'Please enter a valid email');
                isValid = false;
            } else if (email) {
                removeError(email);
            }

            if (subject && subject.value.trim() === '') {
                showError(subject, 'Subject is required');
                isValid = false;
            } else if (subject) {
                removeError(subject);
            }

            if (message && message.value.trim() === '') {
                showError(message, 'Message is required');
                isValid = false;
            } else if (message) {
                removeError(message);
            }

            if (isValid) {
                // Here you would typically send the form data to a server
                // For now, we'll just show a success message
                const successMessage = document.createElement('div');
                successMessage.className = 'alert alert-success';
                successMessage.textContent = 'Your message has been sent. Thank you!';

                contactForm.reset();
                contactForm.appendChild(successMessage);

                // Remove success message after 5 seconds
                setTimeout(() => {
                    successMessage.remove();
                }, 5000);
            }
        });
    }

    // Helper functions for form validation
    function showError(input, message) {
        const formControl = input.parentElement;
        const errorMessage = formControl.querySelector('.error-message') || document.createElement('div');

        errorMessage.className = 'error-message';
        errorMessage.textContent = message;

        if (!formControl.querySelector('.error-message')) {
            formControl.appendChild(errorMessage);
        }

        formControl.className = 'form-group error';
    }

    function removeError(input) {
        const formControl = input.parentElement;
        const errorMessage = formControl.querySelector('.error-message');

        if (errorMessage) {
            errorMessage.remove();
        }

        formControl.className = 'form-group';
    }

    function isValidEmail(email) {
        const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        return re.test(String(email).toLowerCase());
    }

    // FAQ Accordion Functionality
    const faqItems = document.querySelectorAll('.faq-item');

    if (faqItems.length > 0) {
        faqItems.forEach(item => {
            const question = item.querySelector('.faq-question');

            if (question) {
                question.addEventListener('click', () => {
                    // Check if this item is already active
                    const isActive = item.classList.contains('active');

                    // Close all items
                    faqItems.forEach(faqItem => {
                        faqItem.classList.remove('active');
                    });

                    // If the clicked item wasn't active, make it active
                    if (!isActive) {
                        item.classList.add('active');
                    }

                    // Update Locomotive Scroll with a longer delay to ensure content is fully expanded
                    if (scroll) {
                        setTimeout(() => {
                            scroll.update();

                            // If an item was opened (not closed), scroll to it
                            if (!isActive) {
                                const faqSection = document.querySelector('.faq-section');
                                const offset = item.offsetTop - faqSection.offsetTop + 100;

                                scroll.scrollTo(item, {
                                    offset: -150,
                                    duration: 800,
                                    easing: [0.25, 0.0, 0.35, 1.0]
                                });
                            }
                        }, 400);
                    }
                });
            }
        });
    }
});
