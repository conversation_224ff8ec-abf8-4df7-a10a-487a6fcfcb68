/* Responsive Styles for North Texas SDA Church Website */

/* Large Screens (1200px and above) */
@media (min-width: 1200px) {
    .cmsmasters-main__inner,
    .cmsmasters-header__inner,
    .cmsmasters-footer__inner {
        max-width: 1320px;
        margin: 0 auto;
    }
}

/* Medium Screens (992px to 1199px) */
@media (max-width: 1199px) {
    .hero-heading .elementor-heading-title {
        font-size: 120px;
    }
    
    .cmsmasters-widget-title__heading {
        font-size: 60px;
    }
    
    .mission-welcome h2 {
        font-size: 50px;
    }
    
    .service-times {
        max-width: 250px;
    }
}

/* Small Screens (768px to 991px) */
@media (max-width: 991px) {
    .cmsmasters-header__navigation {
        position: fixed;
        top: 100px;
        left: 0;
        width: 100%;
        background-color: var(--bg-color);
        padding: 20px;
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 999;
    }
    
    .cmsmasters-header__navigation.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }
    
    .cmsmasters-header__menu {
        flex-direction: column;
        gap: 15px;
    }
    
    .cmsmasters-header__toggle {
        display: block;
    }
    
    .hero-heading .elementor-heading-title {
        font-size: 100px;
    }
    
    .welcome-content {
        flex-direction: column;
    }
    
    .welcome-decoration {
        margin-bottom: 30px;
    }
    
    .welcome-text {
        padding: 0;
    }
    
    .mission-grid {
        grid-template-columns: repeat(1, 1fr);
        gap: 20px;
    }
    
    .cmsmasters-footer__widgets {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
    }
}

/* Extra Small Screens (576px to 767px) */
@media (max-width: 767px) {
    .cmsmasters-header__content {
        height: 80px;
    }
    
    .cmsmasters-header__logo-image {
        max-height: 40px;
    }
    
    .cmsmasters-header__logo-text {
        font-size: 20px;
    }
    
    .cmsmasters-header__actions {
        display: none;
    }
    
    .hero-heading .elementor-heading-title {
        font-size: 80px;
    }
    
    .hero-buttons {
        flex-direction: column;
        gap: 10px;
    }
    
    .service-times {
        display: none;
    }
    
    .cmsmasters-widget-title__heading {
        font-size: 40px;
    }
    
    .ministries-slider {
        flex-direction: column;
        gap: 20px;
    }
    
    .ministry-item {
        margin-bottom: 20px;
    }
    
    .cmsmasters-footer__widgets {
        grid-template-columns: 1fr;
        gap: 30px;
    }
}

/* Mobile Screens (Up to 575px) */
@media (max-width: 575px) {
    .cmsmasters-header__content {
        height: 70px;
    }
    
    .cmsmasters-header__logo-image {
        max-height: 30px;
        margin-right: 10px;
    }
    
    .cmsmasters-header__logo-text {
        font-size: 18px;
    }
    
    .hero-heading .elementor-heading-title {
        font-size: 60px;
    }
    
    .hero-subtitle .elementor-heading-title {
        font-size: 18px;
    }
    
    .cmsmasters-widget-title__heading {
        font-size: 36px;
    }
    
    .who-we-are-box {
        padding: 30px 20px;
    }
    
    .who-we-are-box-title {
        font-size: 24px;
    }
    
    .mission-welcome h2 {
        font-size: 36px;
    }
    
    .mission-item {
        padding: 30px 20px;
    }
    
    .cmsmasters-footer__widget-title {
        font-size: 18px;
    }
}
