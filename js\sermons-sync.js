// Sermons sync script for real-time data from dashboard
const API_URL = 'http://localhost:3001/api';

// Function to format sermon date
function formatSermonDate(dateString) {
    const date = new Date(dateString);
    const options = { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
    };
    return date.toLocaleDateString('en-US', options);
}

// Function to extract YouTube video ID from URL
function getYouTubeVideoId(url) {
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);
    return (match && match[2].length === 11) ? match[2] : null;
}

// Function to get YouTube thumbnail URL
function getYouTubeThumbnail(videoUrl) {
    const videoId = getYouTubeVideoId(videoUrl);
    if (videoId) {
        return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
    }
    return 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=600&q=80';
}

// Function to create sermon card HTML
function createSermonCard(sermon) {
    const formattedDate = formatSermonDate(sermon.date);
    const thumbnailUrl = sermon.thumbnailUrl || getYouTubeThumbnail(sermon.videoUrl);
    
    return `
        <div class="sermon-card" data-scroll data-sermon-id="${sermon.id}">
            <div class="sermon-thumbnail">
                <img src="${thumbnailUrl}" alt="${sermon.title}">
                <div class="sermon-play-button">
                    <i class="fas fa-play"></i>
                </div>
            </div>
            <div class="sermon-details">
                <div class="sermon-date">${formattedDate}</div>
                <h3 class="sermon-title">${sermon.title}</h3>
                <p class="sermon-description">${sermon.description}</p>
                <div class="sermon-categories">
                    <span class="sermon-category">${sermon.preacher || 'Pastor'}</span>
                </div>
            </div>
        </div>
    `;
}

// Function to fetch and display sermons
async function fetchAndDisplaySermons() {
    try {
        console.log('🎬 Fetching sermons from API...');
        const response = await fetch(`${API_URL}/sermons`);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const sermons = await response.json();
        console.log('📺 Sermons received:', sermons);
        
        const sermonsContainer = document.querySelector('.sermons-container');
        if (!sermonsContainer) {
            console.error('❌ Sermons container not found');
            return;
        }
        
        // Clear existing content
        sermonsContainer.innerHTML = '';
        
        // Add sermons
        if (sermons && sermons.length > 0) {
            sermons.forEach(sermon => {
                sermonsContainer.innerHTML += createSermonCard(sermon);
            });
            
            // Add click event listeners to sermon cards
            addSermonClickListeners();
            
            console.log(`✅ Successfully displayed ${sermons.length} sermons`);
        } else {
            sermonsContainer.innerHTML = `
                <div class="no-sermons-message">
                    <h3>No sermons available</h3>
                    <p>Check back soon for new sermon content.</p>
                </div>
            `;
        }
        
    } catch (error) {
        console.error('❌ Error fetching sermons:', error);
        
        // Show error message
        const sermonsContainer = document.querySelector('.sermons-container');
        if (sermonsContainer) {
            sermonsContainer.innerHTML = `
                <div class="error-message">
                    <h3>Unable to load sermons</h3>
                    <p>Please check your connection and try again.</p>
                </div>
            `;
        }
    }
}

// Function to add click event listeners to sermon cards
function addSermonClickListeners() {
    const sermonCards = document.querySelectorAll('.sermon-card');
    
    sermonCards.forEach(card => {
        card.addEventListener('click', function() {
            const sermonId = this.getAttribute('data-sermon-id');
            if (sermonId) {
                // Redirect to sermon video page
                window.location.href = `sermon-video.html?id=${sermonId}`;
            }
        });
        
        // Add hover effect
        card.style.cursor = 'pointer';
    });
}

// Function to start real-time sync
function startSermonSync() {
    // Initial load
    fetchAndDisplaySermons();
    
    // Set up periodic sync every 30 seconds
    setInterval(fetchAndDisplaySermons, 30000);
    
    console.log('🔄 Sermon sync started - checking for updates every 30 seconds');
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎬 Sermons sync script loaded');
    startSermonSync();
});

// Export functions for external use
window.SermonSync = {
    fetchAndDisplaySermons,
    startSermonSync
};
