/**
 * Test script to check if the API is working correctly
 */

const http = require('http');

// API URL
const API_HOST = 'localhost';
const API_PORT = 3000;
const API_PATH = '/api/leadership';

// Make a GET request to the API
const req = http.request({
  host: API_HOST,
  port: API_PORT,
  path: API_PATH,
  method: 'GET'
}, (res) => {
  console.log('API response status:', res.statusCode);
  console.log('API response headers:', res.headers);
  
  let data = '';
  
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('API response data:');
    try {
      const parsedData = JSON.parse(data);
      console.log(JSON.stringify(parsedData, null, 2));
    } catch (err) {
      console.error('Error parsing JSON:', err);
      console.log('Raw data:', data);
    }
  });
});

req.on('error', (err) => {
  console.error('Error making API request:', err);
});

req.end();

console.log('API request sent to:', `http://${API_HOST}:${API_PORT}${API_PATH}`);
