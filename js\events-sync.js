/**
 * Events Page Real-Time Sync Script
 * North Texas SDA Church
 *
 * This script fetches events from the admin dashboard API and displays them
 * on the events.html page while maintaining the original card styling.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Events sync script loaded');

    // API URL
    const API_URL = 'http://localhost:3000/api';

    // Get the events container
    const eventsContainer = document.querySelector('.modern-events-container');

    if (!eventsContainer) {
        console.error('❌ Events container not found');
        return;
    }

    // Function to format date for display
    function formatEventDate(dateString) {
        const date = new Date(dateString);
        const days = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];
        const months = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN',
                       'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];

        return {
            dayName: days[date.getDay()],
            day: date.getDate().toString().padStart(2, '0'),
            month: months[date.getMonth()],
            fullDate: `${months[date.getMonth()]} ${date.getDate()}`
        };
    }

    // Function to format time for display
    function formatTime(timeString) {
        if (!timeString) return '';

        const [hours, minutes] = timeString.split(':');
        const hour = parseInt(hours);
        const ampm = hour >= 12 ? 'PM' : 'AM';
        const displayHour = hour % 12 || 12;

        return `${displayHour}:${minutes} ${ampm}`;
    }

    // Function to create event card HTML
    function createEventCard(event) {
        const { dayName, day, fullDate } = formatEventDate(event.date);
        const startTime = formatTime(event.startTime);
        const endTime = formatTime(event.endTime);
        const timeDisplay = endTime ? `${fullDate} @ ${startTime} - ${endTime}` : `${fullDate} @ ${startTime}`;

        // Use event image if provided, otherwise use a default church image
        const eventImage = event.imageUrl || 'https://blog.ronniefloyd.com/wp-content/uploads/Preaching.png';

        return `
            <div class="modern-event-card">
                <div class="modern-event-left">
                    <div class="modern-event-date">
                        <span class="modern-event-day-name">${dayName}</span>
                        <span class="modern-event-day">${day}</span>
                    </div>
                </div>
                <div class="modern-event-image">
                    <img src="${eventImage}" alt="${event.title}">
                </div>
                <div class="modern-event-content">
                    <h3 class="modern-event-title">${event.title}</h3>
                    <p class="modern-event-description">${event.description}</p>
                    <div class="modern-event-details">
                        <p class="modern-event-time">${timeDisplay}</p>
                        <p class="modern-event-location"><i class="fas fa-map-marker-alt"></i> ${event.location}</p>
                    </div>
                </div>
                <div class="modern-event-action">
                    <a href="#" class="modern-event-button">VIEW DETAILS</a>
                </div>
            </div>
        `;
    }

    // Function to load and display events
    async function loadEvents() {
        try {
            console.log('🔄 Loading events from API...');

            const response = await fetch(`${API_URL}/events`);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const events = await response.json();
            console.log('📋 Loaded events:', events);

            // Filter only upcoming events and sort by date
            const upcomingEvents = events
                .filter(event => event.status === 'upcoming' || event.status === 'ongoing')
                .sort((a, b) => new Date(a.date) - new Date(b.date));

            console.log('📅 Upcoming events:', upcomingEvents);

            // Clear existing events (keep the header)
            eventsContainer.innerHTML = '';

            if (upcomingEvents.length === 0) {
                eventsContainer.innerHTML = `
                    <div class="modern-event-card">
                        <div class="modern-event-content" style="text-align: center; padding: 40px; width: 100%;">
                            <h3 class="modern-event-title">No Upcoming Events</h3>
                            <p class="modern-event-description">Check back soon for new events and announcements!</p>
                        </div>
                    </div>
                `;
                return;
            }

            // Create and append event cards
            upcomingEvents.forEach(event => {
                const eventCardHTML = createEventCard(event);
                eventsContainer.insertAdjacentHTML('beforeend', eventCardHTML);
            });

            console.log('✅ Events displayed successfully');

            // Reinitialize Locomotive Scroll if it exists
            if (window.scroll && typeof window.scroll.update === 'function') {
                setTimeout(() => {
                    window.scroll.update();
                }, 100);
            }

        } catch (error) {
            console.error('❌ Error loading events:', error);

            // Show error message
            eventsContainer.innerHTML = `
                <div class="modern-event-card">
                    <div class="modern-event-content" style="text-align: center; padding: 40px; width: 100%;">
                        <h3 class="modern-event-title">Unable to Load Events</h3>
                        <p class="modern-event-description">Please check your connection and try again later.</p>
                    </div>
                </div>
            `;
        }
    }

    // Function to set up real-time sync
    function setupRealTimeSync() {
        // Load events immediately
        loadEvents();

        // Set up periodic refresh (every 30 seconds)
        setInterval(loadEvents, 30000);

        // Listen for visibility change to refresh when page becomes visible
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                console.log('🔄 Page became visible, refreshing events...');
                loadEvents();
            }
        });

        // Listen for focus event to refresh when window gets focus
        window.addEventListener('focus', function() {
            console.log('🔄 Window focused, refreshing events...');
            loadEvents();
        });
    }

    // Initialize real-time sync
    setupRealTimeSync();

    console.log('✅ Events real-time sync initialized');
});
