const express = require('express');
const cors = require('cors');
const path = require('path');
const jwt = require('jsonwebtoken');
require('dotenv').config();

// Initialize Express app
const app = express();

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// Demo user for testing
const demoUser = {
  id: 1,
  name: 'Admin User',
  email: '<EMAIL>',
  role: 'admin'
};

// Login route
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;

  // For demo purposes, accept <EMAIL> with password 'password'
  if (email === '<EMAIL>' && password === 'password') {
    const token = jwt.sign(
      { id: demoUser.id, name: demoUser.name, email: demoUser.email, role: demoUser.role },
      process.env.JWT_SECRET || 'secret',
      { expiresIn: '1d' }
    );

    return res.json({ token });
  }

  return res.status(400).json({ message: 'Invalid credentials' });
});

// Get user route
app.get('/api/auth/user', (req, res) => {
  // Get token from header
  const token = req.header('x-auth-token');

  // Check if no token
  if (!token) {
    return res.status(401).json({ message: 'No token, authorization denied' });
  }

  try {
    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'secret');

    // Return user data
    return res.json({
      id: decoded.id,
      name: decoded.name,
      email: decoded.email,
      role: decoded.role
    });
  } catch (err) {
    return res.status(401).json({ message: 'Token is not valid' });
  }
});

// Events routes
app.get('/api/events', (req, res) => {
  // Return demo events
  return res.json([
    {
      id: 1,
      title: 'Community Service Day',
      description: 'Join us as we serve our community through various outreach activities.',
      date: '2023-06-15',
      startTime: '09:00:00',
      endTime: '14:00:00',
      location: 'Church Grounds',
      featured: true,
      status: 'upcoming'
    },
    {
      id: 2,
      title: 'Youth Worship Night',
      description: 'A special evening of praise and worship led by our youth ministry.',
      date: '2023-06-22',
      startTime: '18:00:00',
      endTime: '20:30:00',
      location: 'Fellowship Hall',
      featured: false,
      status: 'upcoming'
    },
    {
      id: 3,
      title: 'Family Potluck',
      description: 'Bring your favorite dish and join us for fellowship after the service.',
      date: '2023-06-29',
      startTime: '13:00:00',
      endTime: '15:00:00',
      location: 'Church Fellowship Hall',
      featured: true,
      status: 'upcoming'
    }
  ]);
});

// Blog routes
app.get('/api/posts', (req, res) => {
  // Return demo blog posts
  return res.json([
    {
      id: 1,
      title: 'Walking in Faith',
      content: 'Exploring how faith guides our daily walk with Christ.',
      excerpt: 'Faith is the substance of things hoped for, the evidence of things not seen.',
      author: 'Pastor John Smith',
      category: 'Devotional',
      publishedAt: '2023-06-10T10:00:00.000Z',
      status: 'published'
    },
    {
      id: 2,
      title: 'The Power of Prayer',
      content: 'Understanding the transformative power of prayer in our lives.',
      excerpt: 'Prayer is the key that unlocks all the storehouses of God\'s infinite grace and power.',
      author: 'Pastor Jane Doe',
      category: 'Prayer',
      publishedAt: '2023-06-03T10:00:00.000Z',
      status: 'published'
    }
  ]);
});

// Gallery routes
app.get('/api/gallery', (req, res) => {
  // Return demo gallery items
  return res.json([
    {
      id: 1,
      title: 'Sunday Worship',
      description: 'Moments from our Sunday worship service.',
      image: '/images/gallery/worship.jpg',
      category: 'Worship',
      featured: true
    },
    {
      id: 2,
      title: 'Youth Retreat',
      description: 'Our youth enjoying their annual retreat.',
      image: '/images/gallery/youth.jpg',
      category: 'Youth',
      featured: false
    },
    {
      id: 3,
      title: 'Community Outreach',
      description: 'Serving our community with love.',
      image: '/images/gallery/outreach.jpg',
      category: 'Outreach',
      featured: true
    }
  ]);
});

// Settings routes
app.get('/api/settings', (req, res) => {
  // Return demo settings
  return res.json({
    churchName: {
      value: 'Faith Connect Church',
      type: 'text',
      group: 'general',
      description: 'Name of the church'
    },
    churchTagline: {
      value: 'A place where faith connects us to God and to each other',
      type: 'text',
      group: 'general',
      description: 'Church tagline'
    },
    address: {
      value: '123 Faith Avenue, Dallas, TX 75001',
      type: 'text',
      group: 'contact',
      description: 'Church address'
    },
    phone: {
      value: '(*************',
      type: 'text',
      group: 'contact',
      description: 'Church phone number'
    },
    email: {
      value: '<EMAIL>',
      type: 'text',
      group: 'contact',
      description: 'Church email address'
    }
  });
});

// Serve static files for the public website
app.use(express.static(path.join(__dirname, '../')));

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).send({ message: 'Server Error', error: err.message });
});

// Set port and start server
const PORT = process.env.PORT || 5001;
app.listen(PORT, () => console.log(`Server running on port ${PORT}`));
