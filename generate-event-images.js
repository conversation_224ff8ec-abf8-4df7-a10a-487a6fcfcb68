const fs = require('fs');
const { createCanvas } = require('canvas');

// Create directory if it doesn't exist
const dir = './images/events';
if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
}

// Function to generate a placeholder image
function generatePlaceholderImage(filename, text, width = 300, height = 300, bgColor = '#f95c28', textColor = '#ffffff') {
    const canvas = createCanvas(width, height);
    const ctx = canvas.getContext('2d');
    
    // Fill background
    ctx.fillStyle = bgColor;
    ctx.fillRect(0, 0, width, height);
    
    // Add text
    ctx.fillStyle = textColor;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    // Calculate font size based on canvas dimensions
    const fontSize = Math.min(width, height) / 10;
    ctx.font = `bold ${fontSize}px Arial`;
    
    // Draw text in the center
    ctx.fillText(text, width / 2, height / 2);
    
    // Save the image
    const buffer = canvas.toBuffer('image/jpeg');
    fs.writeFileSync(`${dir}/${filename}`, buffer);
    
    console.log(`Generated: ${filename}`);
}

// Generate event images
generatePlaceholderImage('event1.jpg', 'Community Service Day', 300, 300, '#f95c28', '#ffffff');
generatePlaceholderImage('event2.jpg', 'Youth Concert', 300, 300, '#3b5998', '#ffffff');
generatePlaceholderImage('event3.jpg', 'Bible Study Series', 300, 300, '#4CAF50', '#ffffff');

console.log('All event images generated successfully!');
