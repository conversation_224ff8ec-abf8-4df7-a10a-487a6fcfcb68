[{"id": 1, "name": "<PERSON>", "phone": "(*************", "address": "123 Main St, Anytown, TX 75001", "birthdate": "1980-05-15", "memberSince": "2010-03-10", "ministry": "Worship Team", "status": "active", "notes": "Founding member of the worship team", "createdAt": "2025-05-24T14:00:00.000Z"}, {"id": 2, "name": "<PERSON>", "phone": "(*************", "address": "456 Oak St, Anytown, TX 75001", "birthdate": "1985-08-22", "memberSince": "2012-06-15", "ministry": "Children's Ministry", "status": "active", "notes": "Children's ministry coordinator", "createdAt": "2025-05-24T14:00:00.000Z"}, {"id": 3, "name": "<PERSON>", "phone": "(*************", "address": "789 Pine St, Anytown, TX 75001", "birthdate": "1975-11-30", "memberSince": "2008-01-20", "ministry": "Outreach", "status": "active", "notes": "Community outreach leader", "createdAt": "2025-05-24T14:00:00.000Z"}, {"id": 4, "name": "Dashboard Test User", "phone": "(*************", "address": "789 Dashboard Ave, Test City, TX 12345", "birthdate": "1992-03-15", "memberSince": "2023-01-01", "ministry": "Youth Ministry", "status": "active", "notes": "Added via dashboard test", "createdAt": "2025-05-24T15:51:51.207Z"}, {"id": 6, "name": "<PERSON><PERSON>", "phone": "+91 72762 42709", "address": "SY. No. 107, Ganesh Nagar, Boisar", "birthdate": "2005-05-02", "memberSince": "2015-11-15", "ministry": "Worship Team", "status": "active", "notes": "", "createdAt": "2025-05-24T16:38:24.685Z"}]